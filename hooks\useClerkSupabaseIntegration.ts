import { useAuth } from '@clerk/clerk-expo';
import { useEffect, useState } from 'react';

export function useClerkSupabaseIntegration() {
  const { getToken, isLoaded, isSignedIn } = useAuth();
  const [isSupabaseConnected, setIsSupabaseConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const connectSupabase = async () => {
      if (!isLoaded || !isSignedIn) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Get the JWT token from Clerk
        const token = await getToken({ template: 'supabase' });
        
        if (!token) {
          throw new Error('Failed to get token from Clerk');
        }

        // Token is handled by ClerkSupabaseProvider's custom fetch
        // Just verify we can get a token and mark as connected
        setIsSupabaseConnected(true);
      } catch (err: any) {
        console.error('Error connecting Clerk with Supabase:', err);
        setError(err.message || 'An error occurred while connecting to Supabase');
        setIsSupabaseConnected(false);
      } finally {
        setIsLoading(false);
      }
    };

    connectSupabase();
  }, [isLoaded, isSignedIn, getToken]);

  return {
    isLoaded: isLoaded && !isLoading,
    isSupabaseConnected,
    error,
  };
}
