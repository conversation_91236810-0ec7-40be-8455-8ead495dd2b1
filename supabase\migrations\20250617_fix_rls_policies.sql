-- Fix RLS Policies for Consistent Authentication
-- This migration fixes the most critical RLS policy issues for teacher authentication

-- Drop existing problematic policies for materials
DROP POLICY IF EXISTS "Teachers can manage their own materials" ON materials;
DROP POLICY IF EXISTS "Teachers can view shared materials" ON materials;

-- Drop problematic material shares policies
DROP POLICY IF EXISTS "material_shares_access" ON material_shares;
DROP POLICY IF EXISTS "material_shares_tenant_access" ON material_shares;

-- Drop overly permissive class_students policies
DROP POLICY IF EXISTS "class_students_select" ON class_students;
DROP POLICY IF EXISTS "class_students_insert" ON class_students;
DROP POLICY IF EXISTS "class_students_update" ON class_students;

-- Drop overly permissive enrollment_requests policies
DROP POLICY IF EXISTS "enrollment_requests_select" ON enrollment_requests;
DROP POLICY IF EXISTS "enrollment_requests_insert" ON enrollment_requests;
DROP POLICY IF EXISTS "enrollment_requests_update" ON enrollment_requests;

-- Create fixed RLS policies using proper Clerk authentication

-- Materials table policies - Fixed to use proper teacher authentication
CREATE POLICY "Teachers can manage their own materials" ON materials
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND uploaded_by = (
      SELECT id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

CREATE POLICY "Teachers can view shared materials" ON materials
  FOR SELECT USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND (
      uploaded_by = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      ) OR
      visibility IN ('class', 'school', 'public') OR
      id IN (
        SELECT material_id FROM material_shares
        WHERE shared_with_type = 'teacher' AND
        shared_with_id = (
          SELECT id FROM teachers
          WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
      )
    )
  );

-- Material shares policies - Fixed for proper teacher access
CREATE POLICY "Teachers can manage material shares" ON material_shares
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND shared_by = (
      SELECT id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Class students policies - Fixed to use proper teacher-class relationship
CREATE POLICY "Teachers can manage class students" ON class_students
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND class_id IN (
      SELECT id FROM classes
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- Enrollment requests policies - Fixed for proper teacher access
CREATE POLICY "Teachers can manage enrollment requests" ON enrollment_requests
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND teacher_id = (
      SELECT id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Add comment for documentation
COMMENT ON SCHEMA public IS 'RLS policies updated on 2025-06-17 to fix critical authentication issues for teacher access';
