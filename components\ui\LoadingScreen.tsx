import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { ActivityIndicator, Text, View } from 'react-native';

interface LoadingScreenProps {
  message?: string;
}

export function LoadingScreen({ message = 'Loading...' }: LoadingScreenProps) {
  const isDark = useColorScheme() === 'dark';

  return (
    <View className="flex-1 justify-center items-center p-4">
      <ActivityIndicator size="large" color="#3B82F6" />
      <Text className={`mt-4 font-rubik text-center ${
        isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
      }`}>
        {message}
      </Text>
    </View>
  );
}
