import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, RefreshControl, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';

// Stores
import { useAttendanceStore, type AttendanceSession } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

type TabType = 'today' | 'sessions' | 'reports';

const AttendanceScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { isLoaded } = useSupabaseAuth();
  const { userId: clerkUserId } = useAuth();

  const [activeTab, setActiveTab] = useState<TabType>('today');
  const [initialLoading, setInitialLoading] = useState(true);

  const {
    sessions,
    attendanceStats,
    error,
    refreshing,
    loadSessions,
    loadAttendanceStats,
    refreshData,
    clearError
  } = useAttendanceStore();

  const {
    currentTeacher,
    availableClasses,
    loadTeacherData
  } = useEnrollmentStore();

  // Load teacher data and attendance data
  useEffect(() => {
    const initializeData = async () => {
      if (isLoaded && clerkUserId) {
        try {
          // Load teacher data if not already loaded
          if (!currentTeacher) {
            await loadTeacherData(clerkUserId);
          }
        } catch (error) {
          console.error('Error initializing attendance data:', error);
        } finally {
          setInitialLoading(false);
        }
      } else if (isLoaded) {
        // If loaded but no user, stop loading
        setInitialLoading(false);
      }
    };

    initializeData();
  }, [isLoaded, clerkUserId, currentTeacher, loadTeacherData]);

  // Load attendance data when teacher is available
  useEffect(() => {
    if (currentTeacher?.id) {
      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      loadSessions(currentTeacher.id, undefined, { start: weekAgo, end: today });
      loadAttendanceStats(currentTeacher.id);
    }
  }, [currentTeacher?.id, loadSessions, loadAttendanceStats]);

  const handleRefresh = async () => {
    if (currentTeacher?.id) {
      // Clear all caches to avoid stale data
      await refreshData();

      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      await loadSessions(currentTeacher.id, undefined, { start: weekAgo, end: today });
      await loadAttendanceStats(currentTeacher.id);
    }
  };

  const handleCreateSession = () => {
    if (!availableClasses || availableClasses.length === 0) {
      Alert.alert('No Classes', 'You need to have classes assigned to create attendance sessions.');
      return;
    }
    router.push('/attendance/create-session');
  };

  const handleSessionPress = (session: AttendanceSession) => {
    router.push(`/attendance/take-attendance?sessionId=${session.id}` as any);
  };

  const renderTabButton = (tab: TabType, title: string, iconName: string) => (
    <TouchableOpacity
      key={tab}
      onPress={() => setActiveTab(tab)}
      className={`flex-1 py-3 px-4 rounded-lg mx-1 ${
        activeTab === tab
          ? 'bg-primary-500'
          : isDark ? 'bg-dark-surface' : 'bg-light-surface'
      }`}
    >
      <View className="items-center">
        <IconSymbol
          name={iconName as any}
          size={20}
          color={activeTab === tab ? '#FFFFFF' : isDark ? '#9CA3AF' : '#6B7280'}
        />
        <Text className={`text-sm font-rubik-medium mt-1 ${
          activeTab === tab ? 'text-white' : isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
        }`}>
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderTodayContent = () => {
    const today = new Date().toISOString().split('T')[0];
    const todaySessions = sessions.filter(session => session.session_date === today);

    return (
      <View className="flex-1">
        {/* Quick Stats */}
        {attendanceStats && (
          <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Attendance Overview
            </Text>
            <View className="flex-row justify-between">
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
                  {attendanceStats.total_sessions}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Sessions
                </Text>
              </View>
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl text-success`}>
                  {attendanceStats.present_percentage.toFixed(1)}%
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Present
                </Text>
              </View>
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {attendanceStats.total_students}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Students
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View className="mb-6">
          <TouchableOpacity
            onPress={handleCreateSession}
            className="bg-primary-500 p-4 rounded-lg flex-row items-center justify-center mb-4"
          >
            <IconSymbol name="plus.circle.fill" size={24} color="#FFFFFF" />
            <Text className="text-white font-rubik-semibold text-base ml-2">
              Create New Session for Students
            </Text>
          </TouchableOpacity>

          {/* Separator */}
          <View className={`h-px bg-gray-200 mb-4 ${isDark ? 'bg-gray-700' : 'bg-gray-200'}`} />

          <TouchableOpacity
            onPress={() => router.push('/attendance/teacher-attendance' as any)}
            className={`p-4 rounded-lg flex-row items-center justify-center border-2 border-primary-500 ${
              isDark ? 'bg-dark-surface' : 'bg-light-surface'
            }`}
          >
            <IconSymbol name="person.fill" size={24} color="#3B82F6" />
            <Text className="text-primary-500 font-rubik-semibold text-base ml-2">
              My Attendance (Teacher)
            </Text>
          </TouchableOpacity>
        </View>

        {/* Today's Sessions */}
        <View className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Today&apos;s Sessions
          </Text>

          {todaySessions.length === 0 ? (
            <View className="items-center py-8">
              <IconSymbol
                name="calendar"
                size={48}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                No sessions today
              </Text>
              <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Create a new session to start taking attendance
              </Text>
            </View>
          ) : (
            <View>
              {todaySessions.map((session) => (
                <TouchableOpacity
                  key={session.id}
                  onPress={() => handleSessionPress(session)}
                  className={`p-3 rounded-lg mb-2 border ${
                    isDark ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border'
                  }`}
                >
                  <View className="flex-row items-center justify-between">
                    <View className="flex-1">
                      <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                        {session.class?.name || 'Unknown Class'}
                      </Text>
                      <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        {session.subject} • {session.session_time}
                      </Text>
                    </View>
                    <View className="items-end">
                      <View className={`px-2 py-1 rounded ${
                        session.status === 'completed' ? 'bg-success' :
                        session.status === 'active' ? 'bg-primary-500' : 'bg-gray-400'
                      }`}>
                        <Text className="text-white font-rubik-medium text-xs capitalize">
                          {session.status}
                        </Text>
                      </View>
                      <Text className={`font-rubik text-xs mt-1 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        {session.present_count}/{session.total_students}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderSessionsContent = () => (
    <View className="flex-1">
      <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        Recent Sessions
      </Text>

      {sessions.length === 0 ? (
        <View className="items-center py-12">
          <IconSymbol
            name="list.bullet"
            size={48}
            color={isDark ? '#9CA3AF' : '#6B7280'}
          />
          <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            No sessions yet
          </Text>
          <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Create your first attendance session
          </Text>
        </View>
      ) : (
        <View>
          {sessions.map((session) => (
            <TouchableOpacity
              key={session.id}
              onPress={() => handleSessionPress(session)}
              className={`p-4 rounded-lg mb-3 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
            >
              <View className="flex-row items-center justify-between mb-2">
                <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {session.class?.name || 'Unknown Class'}
                </Text>
                <View className={`px-2 py-1 rounded ${
                  session.status === 'completed' ? 'bg-success' :
                  session.status === 'active' ? 'bg-primary-500' : 'bg-gray-400'
                }`}>
                  <Text className="text-white font-rubik-medium text-xs capitalize">
                    {session.status}
                  </Text>
                </View>
              </View>

              <View className="flex-row items-center justify-between">
                <View>
                  <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    {session.subject} • {session.session_date}
                  </Text>
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    {session.session_time} • {session.session_type}
                  </Text>
                </View>
                <View className="items-end">
                  <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {session.present_count}/{session.total_students}
                  </Text>
                  <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Present
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );

  const renderReportsContent = () => (
    <View className="flex-1">
      <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        Attendance Reports
      </Text>

      {/* Quick Stats Summary */}
      {attendanceStats && (
        <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-semibold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Overall Statistics
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text className={`font-rubik-bold text-xl ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
                {attendanceStats.total_sessions}
              </Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Total Sessions
              </Text>
            </View>
            <View className="items-center">
              <Text className={`font-rubik-bold text-xl text-success`}>
                {attendanceStats.present_percentage.toFixed(1)}%
              </Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Attendance Rate
              </Text>
            </View>
            <View className="items-center">
              <Text className={`font-rubik-bold text-xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {attendanceStats.total_students}
              </Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Total Students
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Report Options */}
      <View className="space-y-3">
        <TouchableOpacity
          onPress={() => router.push('/reports/detailed' as any)}
          className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
        >
          <View className="flex-row items-center">
            <IconSymbol name="chart.bar.fill" size={24} color={isDark ? '#60A5FA' : '#2563EB'} />
            <View className="ml-3 flex-1">
              <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Detailed Reports
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                View comprehensive student attendance analytics
              </Text>
            </View>
            <IconSymbol name="chevron.right" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => router.push('/reports/summary' as any)}
          className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
        >
          <View className="flex-row items-center">
            <IconSymbol name="list.bullet" size={24} color={isDark ? '#34D399' : '#059669'} />
            <View className="ml-3 flex-1">
              <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Summary Reports
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Quick overview and class-wise statistics
              </Text>
            </View>
            <IconSymbol name="chevron.right" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => router.push('/reports/export' as any)}
          className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
        >
          <View className="flex-row items-center">
            <IconSymbol name="square.and.arrow.up" size={24} color={isDark ? '#F59E0B' : '#D97706'} />
            <View className="ml-3 flex-1">
              <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Export Reports
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Download attendance data as CSV or PDF
              </Text>
            </View>
            <IconSymbol name="chevron.right" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => router.push('/reports/trends' as any)}
          className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
        >
          <View className="flex-row items-center">
            <IconSymbol name="calendar" size={24} color={isDark ? '#8B5CF6' : '#7C3AED'} />
            <View className="ml-3 flex-1">
              <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Attendance Trends
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Track attendance patterns over time
              </Text>
            </View>
            <IconSymbol name="chevron.right" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <View className={`mt-6 p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
        <Text className={`font-rubik-semibold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Quick Actions
        </Text>
        <View className="flex-row justify-between">
          <TouchableOpacity
            onPress={() => {
              const today = new Date().toISOString().split('T')[0];
              router.push(`/reports/detailed?date=${today}` as any);
            }}
            className={`flex-1 p-3 rounded-lg mr-2 ${isDark ? 'bg-primary-600' : 'bg-primary-500'}`}
          >
            <Text className="text-white font-rubik-semibold text-center text-sm">
              Today&apos;s Report
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
              const today = new Date().toISOString().split('T')[0];
              router.push(`/reports/detailed?startDate=${weekAgo}&endDate=${today}` as any);
            }}
            className={`flex-1 p-3 rounded-lg ml-2 border-2 border-primary-500 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
          >
            <Text className="text-primary-500 font-rubik-semibold text-center text-sm">
              This Week
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'today':
        return renderTodayContent();
      case 'sessions':
        return renderSessionsContent();
      case 'reports':
        return renderReportsContent();
      default:
        return null;
    }
  };

  // Show loading screen
  if (!isLoaded || initialLoading) {
    return <LoadingScreen message="Loading attendance system..." />;
  }

  // Show error screen
  if (error) {
    return (
      <ErrorScreen
        title="Attendance Error"
        message={error}
        onRetry={() => {
          clearError();
          handleRefresh();
        }}
      />
    );
  }

  // Show message if teacher is not a class teacher
  if (currentTeacher && !currentTeacher.is_class_teacher) {
    return (
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <View className="flex-1 justify-center items-center p-6">
          <IconSymbol
            name="exclamationmark.triangle.fill"
            size={64}
            color={isDark ? '#F59E0B' : '#D97706'}
          />
          <Text className={`text-center mt-4 text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Access Restricted
          </Text>
          <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Only class teachers can access the attendance system. Please contact your administrator if you believe this is an error.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Attendance
          </Text>
          <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Track and manage student attendance
          </Text>
        </View>

        {/* Tab Navigation */}
        <View className="flex-row p-4">
          {renderTabButton('today', 'Today', 'calendar')}
          {renderTabButton('sessions', 'Sessions', 'list.bullet')}
          {renderTabButton('reports', 'Reports', 'chart.bar.fill')}
        </View>

        {/* Content */}
        <ScrollView
          className="flex-1 px-4"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={isDark ? '#60A5FA' : '#2563EB'}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {renderContent()}
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default AttendanceScreen;
