/**
 * Colors used throughout the app for both light and dark modes.
 * This app uses Tailwind CSS for styling, with these colors matching the tailwind.config.js definitions.
 */

// Primary and secondary colors
const primaryColor = '#3B82F6'; // Blue
const secondaryColor = '#10B981'; // Teal

// Light mode colors
const primaryColorLight = primaryColor;
const secondaryColorLight = secondaryColor;

// Dark mode colors
const primaryColorDark = '#60A5FA'; // Lighter blue for dark mode
const secondaryColorDark = '#34D399'; // Lighter teal for dark mode

export const Colors = {
  light: {
    text: '#111827', // neutral-900
    textSecondary: '#4B5563', // neutral-600
    background: '#FFFFFF',
    surface: '#F9FAFB', // neutral-50
    border: '#E5E7EB', // neutral-200
    tint: primaryColorLight,
    icon: '#6B7280', // neutral-500
    tabIconDefault: '#9CA3AF', // neutral-400
    tabIconSelected: primaryColorLight,
    primary: primaryColorLight,
    secondary: secondaryColorLight,
    success: '#10B981', // green-500
    warning: '#F59E0B', // amber-500
    error: '#EF4444', // red-500
    info: primaryColorLight,
  },
  dark: {
    text: '#F3F4F6', // neutral-100
    textSecondary: '#9CA3AF', // neutral-400
    background: '#121212',
    surface: '#1E1E1E',
    border: '#2E2E2E',
    tint: primaryColorDark,
    icon: '#9CA3AF', // neutral-400
    tabIconDefault: '#6B7280', // neutral-500
    tabIconSelected: primaryColorDark,
    primary: primaryColorDark,
    secondary: secondaryColorDark,
    success: '#34D399', // green-400 (lighter for dark mode)
    warning: '#FBBF24', // amber-400 (lighter for dark mode)
    error: '#F87171', // red-400 (lighter for dark mode)
    info: primaryColorDark,
  },
};
