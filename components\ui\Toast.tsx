import { useColorScheme } from '@/hooks/useColorScheme';
import React, { createContext, useCallback, useContext, useRef, useState } from 'react';
import { Animated, Text, TouchableOpacity, View } from 'react-native';
import { IconSymbol } from './IconSymbol';

interface ToastProps {
  title: string;
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface ToastContextType {
  show: (toast: ToastProps) => void;
  hide: () => void;
}

const ToastContext = createContext<ToastContextType>({
  show: () => {},
  hide: () => {},
});

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const isDark = useColorScheme() === 'dark';
  const [toast, setToast] = useState<ToastProps | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const timeoutRef = useRef<NodeJS.Timeout>();

  const hide = useCallback(() => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => setToast(null));
  }, [fadeAnim]);

  const show = useCallback((newToast: ToastProps) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setToast(newToast);
    fadeAnim.setValue(0);

    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();

    timeoutRef.current = setTimeout(() => {
      hide();
    }, newToast.duration || 3000);
  }, [fadeAnim, hide]);

  const getIconName = (type: ToastProps['type'] = 'info') => {
    switch (type) {
      case 'success':
        return 'checkmark.circle.fill';
      case 'error':
        return 'exclamationmark.triangle.fill';
      case 'warning':
        return 'exclamationmark.circle.fill';
      default:
        return 'info.circle.fill';
    }
  };

  const getIconColor = (type: ToastProps['type'] = 'info') => {
    switch (type) {
      case 'success':
        return '#10B981';
      case 'error':
        return '#EF4444';
      case 'warning':
        return '#F59E0B';
      default:
        return '#3B82F6';
    }
  };

  return (
    <ToastContext.Provider value={{ show, hide }}>
      {children}
      {toast && (
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }),
              },
            ],
          }}
          className="absolute bottom-4 left-4 right-4"
        >
          <View className={`rounded-xl p-4 ${
            isDark ? 'bg-dark-surface' : 'bg-light-surface'
          }`}>
            <View className="flex-row items-start">
              <IconSymbol
                name={getIconName(toast.type)}
                size={24}
                color={getIconColor(toast.type)}
              />
              <View className="flex-1 ml-3">
                <Text className={`font-rubik-bold ${
                  isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  {toast.title}
                </Text>
                <Text className={`font-rubik mt-1 ${
                  isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                }`}>
                  {toast.message}
                </Text>
              </View>
              <TouchableOpacity
                onPress={hide}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <IconSymbol
                  name="xmark"
                  size={20}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      )}
    </ToastContext.Provider>
  );
}

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}
