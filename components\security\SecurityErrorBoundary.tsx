import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSecurityStore } from '@/stores/securityStore';

interface SecurityErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

interface SecurityErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class SecurityErrorBoundary extends React.Component<
  SecurityErrorBoundaryProps,
  SecurityErrorBoundaryState
> {
  constructor(props: SecurityErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): SecurityErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Security Error Boundary caught an error:', error, errorInfo);
    
    // Flag suspicious activity in security store
    try {
      useSecurityStore.getState().flagSuspiciousActivity(`Error boundary triggered: ${error.message}`);
    } catch (e) {
      console.error('Failed to flag suspicious activity:', e);
    }
    
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return <SecurityErrorFallback onRetry={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}

interface SecurityErrorFallbackProps {
  onRetry: () => void;
}

const SecurityErrorFallback: React.FC<SecurityErrorFallbackProps> = ({ onRetry }) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <SafeAreaView 
      className={`
        flex-1 justify-center items-center p-5
        ${isDark ? 'bg-dark-background' : 'bg-light-background'}
      `}
    >
      <Text 
        className={`
          text-lg font-rubik-bold mb-2
          ${isDark ? 'text-dark-text' : 'text-light-text'}
        `}
      >
        Security Error
      </Text>
      
      <Text 
        className={`
          text-center mb-5 font-rubik
          ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}
        `}
      >
        A security error occurred. Please restart the app and try again.
      </Text>
      
      <TouchableOpacity
        onPress={onRetry}
        className="bg-primary-500 px-6 py-3 rounded-lg"
      >
        <Text className="text-white font-rubik-semibold">
          Retry
        </Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};
