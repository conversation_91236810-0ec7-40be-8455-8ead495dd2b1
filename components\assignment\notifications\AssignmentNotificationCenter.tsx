import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface AssignmentNotification {
  id: string;
  type: 'deadline_reminder' | 'submission_received' | 'grade_released' | 'late_submission' | 'missing_submission';
  title: string;
  message: string;
  assignmentId: string;
  assignmentTitle: string;
  studentId?: string;
  studentName?: string;
  createdAt: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  actionRequired?: boolean;
}

interface AssignmentNotificationCenterProps {
  classId: string;
  userRole: 'teacher' | 'student';
  userId: string;
}

export default function AssignmentNotificationCenter({
  classId,
  userRole,
  userId,
}: AssignmentNotificationCenterProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const [notifications, setNotifications] = useState<AssignmentNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'high_priority'>('all');

  useEffect(() => {
    fetchNotifications();
  }, [classId, userId]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockNotifications: AssignmentNotification[] = [
        {
          id: '1',
          type: 'deadline_reminder',
          title: 'Assignment Due Soon',
          message: 'Math Assignment is due in 2 hours',
          assignmentId: 'assign1',
          assignmentTitle: 'Math Assignment',
          createdAt: new Date().toISOString(),
          isRead: false,
          priority: 'high',
          actionRequired: true,
        },
        {
          id: '2',
          type: 'submission_received',
          title: 'New Submission',
          message: 'John Doe submitted Science Project',
          assignmentId: 'assign2',
          assignmentTitle: 'Science Project',
          studentId: 'student1',
          studentName: 'John Doe',
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          isRead: false,
          priority: 'medium',
        },
        {
          id: '3',
          type: 'grade_released',
          title: 'Grade Available',
          message: 'Your grade for History Essay is now available',
          assignmentId: 'assign3',
          assignmentTitle: 'History Essay',
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          isRead: true,
          priority: 'low',
        },
        {
          id: '4',
          type: 'late_submission',
          title: 'Late Submission',
          message: 'Jane Smith submitted English Report after deadline',
          assignmentId: 'assign4',
          assignmentTitle: 'English Report',
          studentId: 'student2',
          studentName: 'Jane Smith',
          createdAt: new Date(Date.now() - 10800000).toISOString(),
          isRead: false,
          priority: 'medium',
        },
        {
          id: '5',
          type: 'missing_submission',
          title: 'Missing Submission',
          message: '3 students have not submitted Physics Lab Report',
          assignmentId: 'assign5',
          assignmentTitle: 'Physics Lab Report',
          createdAt: new Date(Date.now() - 14400000).toISOString(),
          isRead: true,
          priority: 'high',
          actionRequired: true,
        },
      ];

      setNotifications(mockNotifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchNotifications();
    setRefreshing(false);
  };

  const markAsRead = async (notificationId: string) => {
    try {
      // Update local state
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId ? { ...notif, isRead: true } : notif
        )
      );
      
      // TODO: Call API to mark as read
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, isRead: true }))
      );
      
      // TODO: Call API to mark all as read
      Alert.alert('Success', 'All notifications marked as read');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setNotifications(prev =>
                prev.filter(notif => notif.id !== notificationId)
              );
              // TODO: Call API to delete notification
            } catch (error) {
              console.error('Error deleting notification:', error);
            }
          },
        },
      ]
    );
  };

  const getNotificationIcon = (type: AssignmentNotification['type']) => {
    switch (type) {
      case 'deadline_reminder':
        return 'alarm-outline';
      case 'submission_received':
        return 'document-text-outline';
      case 'grade_released':
        return 'star-outline';
      case 'late_submission':
        return 'time-outline';
      case 'missing_submission':
        return 'warning-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getNotificationColor = (type: AssignmentNotification['type'], priority: string) => {
    if (priority === 'high') return '#EF4444';
    if (priority === 'medium') return '#F59E0B';
    
    switch (type) {
      case 'deadline_reminder':
        return '#EF4444';
      case 'submission_received':
        return '#10B981';
      case 'grade_released':
        return '#3B82F6';
      case 'late_submission':
        return '#F59E0B';
      case 'missing_submission':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const filteredNotifications = notifications.filter(notif => {
    switch (filter) {
      case 'unread':
        return !notif.isRead;
      case 'high_priority':
        return notif.priority === 'high';
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const renderNotification = (notification: AssignmentNotification, index: number) => (
    <Animated.View
      key={notification.id}
      entering={FadeInDown.delay(index * 100).duration(400)}
    >
      <TouchableOpacity
        onPress={() => markAsRead(notification.id)}
        className={`p-4 mb-3 rounded-xl ${
          notification.isRead
            ? isDark ? 'bg-dark-card opacity-70' : 'bg-light-card opacity-70'
            : isDark ? 'bg-dark-card' : 'bg-light-card'
        } ${notification.priority === 'high' ? 'border-l-4 border-red-500' : ''}`}
      >
        <View className="flex-row items-start">
          <View className={`p-2 rounded-lg mr-3 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
            <Ionicons
              name={getNotificationIcon(notification.type) as any}
              size={20}
              color={getNotificationColor(notification.type, notification.priority)}
            />
          </View>

          <View className="flex-1">
            <View className="flex-row items-start justify-between mb-1">
              <Text className={`font-rubik-bold ${
                notification.isRead
                  ? isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
                  : isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                {notification.title}
              </Text>
              
              <View className="flex-row items-center">
                {!notification.isRead && (
                  <View className="w-2 h-2 bg-primary-500 rounded-full mr-2" />
                )}
                <TouchableOpacity
                  onPress={() => deleteNotification(notification.id)}
                  className="p-1"
                >
                  <Ionicons name="close" size={16} color={isDark ? '#666' : '#999'} />
                </TouchableOpacity>
              </View>
            </View>

            <Text className={`font-rubik mb-2 ${
              notification.isRead
                ? isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
                : isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              {notification.message}
            </Text>

            <View className="flex-row items-center justify-between">
              <Text className={`font-rubik-medium text-sm text-primary-500`}>
                {notification.assignmentTitle}
              </Text>
              
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {new Date(notification.createdAt).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </View>

            {notification.actionRequired && (
              <View className="mt-2 px-2 py-1 bg-orange-100 dark:bg-orange-900/30 rounded">
                <Text className="text-orange-600 dark:text-orange-300 text-xs font-rubik-medium">
                  Action Required
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="p-4 border-b border-gray-200 dark:border-gray-700">
        <View className="flex-row items-center justify-between mb-4">
          <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Assignment Notifications
          </Text>
          
          {unreadCount > 0 && (
            <TouchableOpacity
              onPress={markAllAsRead}
              className="px-3 py-1 bg-primary-500 rounded-lg"
            >
              <Text className="text-white font-rubik-medium text-sm">
                Mark All Read ({unreadCount})
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Filter Tabs */}
        <View className="flex-row space-x-2">
          {[
            { id: 'all', title: 'All', count: notifications.length },
            { id: 'unread', title: 'Unread', count: unreadCount },
            { id: 'high_priority', title: 'Priority', count: notifications.filter(n => n.priority === 'high').length },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.id}
              onPress={() => setFilter(tab.id as any)}
              className={`flex-row items-center px-3 py-2 rounded-lg ${
                filter === tab.id
                  ? 'bg-primary-500'
                  : isDark
                  ? 'bg-dark-card'
                  : 'bg-light-card'
              }`}
            >
              <Text
                className={`font-rubik-medium text-sm ${
                  filter === tab.id
                    ? 'text-white'
                    : isDark
                    ? 'text-dark-text'
                    : 'text-light-text'
                }`}
              >
                {tab.title}
              </Text>
              {tab.count > 0 && (
                <View className={`ml-2 px-2 py-0.5 rounded-full ${
                  filter === tab.id ? 'bg-white/20' : 'bg-primary-500'
                }`}>
                  <Text className={`text-xs font-rubik-bold ${
                    filter === tab.id ? 'text-white' : 'text-white'
                  }`}>
                    {tab.count}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Notifications List */}
      <ScrollView
        className="flex-1 p-4"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredNotifications.length > 0 ? (
          filteredNotifications.map((notification, index) =>
            renderNotification(notification, index)
          )
        ) : (
          <View className="items-center justify-center py-12">
            <Ionicons
              name="notifications-off-outline"
              size={48}
              color={isDark ? '#666' : '#999'}
            />
            <Text className={`mt-4 font-rubik text-lg ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              No notifications
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              You're all caught up!
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
