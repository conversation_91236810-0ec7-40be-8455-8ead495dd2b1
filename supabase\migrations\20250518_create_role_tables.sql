-- Create separate tables for different user roles

-- Teachers table
CREATE TABLE IF NOT EXISTS teachers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  clerk_user_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  clerk_user_id TEXT,
  grade TEXT,
  section TEXT,
  roll_number TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Admins table
CREATE TABLE IF NOT EXISTS admins (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  clerk_user_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Parents table
CREATE TABLE IF NOT EXISTS parents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  clerk_user_id TEXT,
  student_id UUID REFERENCES students(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Add RLS policies for each table
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE parents ENABLE ROW LEVEL SECURITY;

-- Create policies for teachers
CREATE POLICY "Teachers can be created by anyone" ON teachers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Teachers can be viewed by users in the same tenant" ON teachers
  FOR SELECT USING (tenant_id = auth.jwt() -> 'tenant_id');

CREATE POLICY "Teachers can be updated by admins in the same tenant" ON teachers
  FOR UPDATE USING (tenant_id = auth.jwt() -> 'tenant_id' AND 
                   EXISTS (SELECT 1 FROM admins WHERE clerk_user_id = auth.uid()));

-- Create policies for students
CREATE POLICY "Students can be created by anyone" ON students
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Students can be viewed by users in the same tenant" ON students
  FOR SELECT USING (tenant_id = auth.jwt() -> 'tenant_id');

CREATE POLICY "Students can be updated by admins in the same tenant" ON students
  FOR UPDATE USING (tenant_id = auth.jwt() -> 'tenant_id' AND 
                   EXISTS (SELECT 1 FROM admins WHERE clerk_user_id = auth.uid()));

-- Create policies for admins
CREATE POLICY "Admins can be created by anyone" ON admins
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can be viewed by users in the same tenant" ON admins
  FOR SELECT USING (tenant_id = auth.jwt() -> 'tenant_id');

CREATE POLICY "Admins can be updated by admins in the same tenant" ON admins
  FOR UPDATE USING (tenant_id = auth.jwt() -> 'tenant_id' AND 
                   EXISTS (SELECT 1 FROM admins WHERE clerk_user_id = auth.uid()));

-- Create policies for parents
CREATE POLICY "Parents can be created by anyone" ON parents
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Parents can be viewed by users in the same tenant" ON parents
  FOR SELECT USING (tenant_id = auth.jwt() -> 'tenant_id');

CREATE POLICY "Parents can be updated by admins in the same tenant" ON parents
  FOR UPDATE USING (tenant_id = auth.jwt() -> 'tenant_id' AND 
                   EXISTS (SELECT 1 FROM admins WHERE clerk_user_id = auth.uid()));
