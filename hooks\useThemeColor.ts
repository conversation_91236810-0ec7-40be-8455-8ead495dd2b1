/**
 * Learn more about light and dark modes:
 * https://docs.expo.dev/guides/color-schemes/
 */

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Define the ColorTheme type to ensure it includes all properties
type ColorTheme = {
  text: string;
  textSecondary: string;
  background: string;
  surface: string;
  border: string;
  tint: string;
  icon: string;
  tabIconDefault: string;
  tabIconSelected: string;
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
};

// Ensure the Colors object conforms to our type definition
const _lightCheck: ColorTheme = Colors.light;
const _darkCheck: ColorTheme = Colors.dark;

export function useThemeColor(
  props: { light?: string; dark?: string },
  colorName: keyof ColorTheme
) {
  const theme = useColorScheme() ?? 'light';
  const colorFromProps = props[theme];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    return Colors[theme][colorName as keyof typeof Colors.light];
  }
}
