import { useColorScheme } from '@/hooks/useColorScheme';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function MockTestDetail() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const {
    selectedMockTest,
    loading,
    error,
    fetchMockTestById,
    deleteMockTest,
    publishMockTest,
    closeMockTest,
  } = useAssignmentStore();

  const [mockTest, setMockTest] = useState(selectedMockTest);

  useEffect(() => {
    if (id && id !== mockTest?.id) {
      fetchMockTestById(id).then(setMockTest);
    }
  }, [id, fetchMockTestById, mockTest?.id]);

  const handleDelete = () => {
    if (!mockTest) return;
    
    Alert.alert(
      'Delete Mock Test',
      `Are you sure you want to delete "${mockTest.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            await deleteMockTest(mockTest.id);
            router.back();
          },
        },
      ]
    );
  };

  const handleStatusChange = () => {
    if (!mockTest) return;
    
    if (mockTest.status === 'draft') {
      Alert.alert(
        'Publish Mock Test',
        `Publish "${mockTest.title}" to make it available to students?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Publish',
            onPress: () => publishMockTest(mockTest.id),
          },
        ]
      );
    } else if (mockTest.status === 'published') {
      Alert.alert(
        'Close Mock Test',
        `Close "${mockTest.title}" to stop accepting attempts?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Close',
            onPress: () => closeMockTest(mockTest.id),
          },
        ]
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-500';
      case 'published':
        return 'bg-green-500';
      case 'closed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getQuestionTypeIcon = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'radio-button-on';
      case 'short_answer':
        return 'text';
      case 'essay':
        return 'document-text';
      case 'mixed':
        return 'layers';
      default:
        return 'help-circle';
    }
  };

  if (loading) {
    return (
      <View className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text className={`mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Loading mock test...
        </Text>
      </View>
    );
  }

  if (error || !mockTest) {
    return (
      <View className={`flex-1 justify-center items-center px-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <Ionicons name="alert-circle" size={48} color="#EF4444" />
        <Text className={`text-center mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {error || 'Mock test not found'}
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          className="mt-4 px-4 py-2 bg-primary rounded-lg"
        >
          <Text className="text-white font-rubik-medium">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
        </TouchableOpacity>
        
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Mock Test Details
        </Text>
        
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              'Mock Test Actions',
              'Choose an action',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Edit', onPress: () => Alert.alert('Edit', 'Edit functionality coming soon') },
                { text: 'Manage Questions', onPress: () => router.push(`/reports/mock-test/questions/${mockTest.id}` as any) },
                { text: 'View Attempts', onPress: () => Alert.alert('Attempts', `${mockTest.attempts_count || 0} attempts`) },
                { 
                  text: mockTest.status === 'draft' ? 'Publish' : mockTest.status === 'published' ? 'Close' : 'Reopen',
                  onPress: handleStatusChange
                },
                { text: 'Delete', style: 'destructive', onPress: handleDelete },
              ]
            );
          }}
          className="p-2"
        >
          <Ionicons name="ellipsis-vertical" size={24} color={isDark ? '#9CA3AF' : '#6B7280'} />
        </TouchableOpacity>
      </View>

      {/* Mock Test Info */}
      <View className="px-4 pb-4">
        <View className={`p-4 rounded-lg border ${isDark ? 'bg-dark-card border-dark-border' : 'bg-light-card border-light-border'}`}>
          {/* Title and Status */}
          <View className="flex-row items-start justify-between mb-3">
            <Text className={`text-2xl font-rubik-bold flex-1 mr-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {mockTest.title}
            </Text>
            <View className={`px-3 py-1 rounded-full ${getStatusColor(mockTest.status)}`}>
              <Text className="text-white text-sm font-rubik-medium capitalize">
                {mockTest.status}
              </Text>
            </View>
          </View>

          {/* Quick Actions */}
          <View className="flex-row space-x-3 mb-4">
            <TouchableOpacity
              onPress={() => router.push(`/reports/mock-test/questions/${mockTest.id}` as any)}
              className="flex-1 bg-primary-500 py-3 rounded-lg items-center"
            >
              <View className="flex-row items-center">
                <Ionicons name="help-circle-outline" size={20} color="white" />
                <Text className="ml-2 text-white font-rubik-semibold">Manage Questions</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={() => Alert.alert('Attempts', `View all student attempts for this test`)}
              className="flex-1 bg-blue-500 py-3 rounded-lg items-center"
            >
              <View className="flex-row items-center">
                <Ionicons name="people-outline" size={20} color="white" />
                <Text className="ml-2 text-white font-rubik-semibold">View Attempts</Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Description */}
          {mockTest.description && (
            <View className="mb-4">
              <Text className={`text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {mockTest.description}
              </Text>
            </View>
          )}

          {/* Instructions */}
          {mockTest.instructions && (
            <View className="mb-4">
              <Text className={`text-sm font-rubik-medium mb-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Instructions:
              </Text>
              <Text className={`text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {mockTest.instructions}
              </Text>
            </View>
          )}

          {/* Test Details */}
          <View className="space-y-3">
            <View className="flex-row items-center">
              <Ionicons name="time-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Time Limit: {mockTest.time_limit} minutes
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name={getQuestionTypeIcon(mockTest.question_type) as any} size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Question Type: {mockTest.question_type.replace('_', ' ')}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="trophy-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Max Points: {mockTest.max_points}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="refresh-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Attempts Allowed: {mockTest.attempts_allowed}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="help-circle-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Questions: {mockTest.questions_count || 0}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="people-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Attempts: {mockTest.attempts_count || 0}
              </Text>
            </View>
          </View>

          {/* Features */}
          <View className="flex-row flex-wrap mt-4 space-x-2">
            {mockTest.auto_graded && (
              <View className="bg-green-100 px-2 py-1 rounded-full mb-2">
                <Text className="text-green-700 text-xs font-rubik-medium">Auto-graded</Text>
              </View>
            )}
            
            {mockTest.show_results_immediately && (
              <View className="bg-blue-100 px-2 py-1 rounded-full mb-2">
                <Text className="text-blue-700 text-xs font-rubik-medium">Instant Results</Text>
              </View>
            )}

            {mockTest.gemini_generated && (
              <View className="bg-purple-100 px-2 py-1 rounded-full mb-2">
                <Text className="text-purple-700 text-xs font-rubik-medium">AI Generated</Text>
              </View>
            )}
          </View>

          {/* Created/Updated Info */}
          <View className="mt-4 pt-4 border-t border-gray-200">
            <Text className={`text-xs ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Created: {new Date(mockTest.created_at).toLocaleDateString()}
            </Text>
            {mockTest.updated_at !== mockTest.created_at && (
              <Text className={`text-xs ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Updated: {new Date(mockTest.updated_at).toLocaleDateString()}
              </Text>
            )}
          </View>
        </View>

        {/* Action Buttons */}
        <View className="flex-row space-x-3 mt-4">
          <TouchableOpacity
            onPress={() => Alert.alert('Questions', 'Manage questions functionality coming soon')}
            className="flex-1 py-3 bg-blue-500 rounded-lg items-center"
          >
            <Text className="text-white font-rubik-medium">Manage Questions</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => Alert.alert('Attempts', 'View attempts functionality coming soon')}
            className="flex-1 py-3 bg-green-500 rounded-lg items-center"
          >
            <Text className="text-white font-rubik-medium">View Attempts</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}
