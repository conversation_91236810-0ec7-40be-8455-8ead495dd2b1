import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useAuth } from "@clerk/clerk-expo";
import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Index() {
  const { isLoaded: authLoaded, isSignedIn } = useAuth();
  const { supabaseUser, isLoaded: supabaseLoaded } = useSupabaseAuth();
  const [isChecking, setIsChecking] = useState(true);
  const colorScheme = useColorScheme() ?? "light";
  const isDark = colorScheme === "dark";

  useEffect(() => {
    // Add a small delay to ensure auth state is properly loaded
    if (authLoaded && supabaseLoaded) {
      const timer = setTimeout(() => {
        setIsChecking(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [authLoaded, supabaseLoaded]);

  // Show loading indicator while checking authentication
  if (!authLoaded || !supabaseLoaded || isChecking) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDark
            ? Colors.dark.background
            : Colors.light.background,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ActivityIndicator
            size="large"
            color={isDark ? Colors.dark.primary : Colors.light.primary}
          />
        </View>
      </SafeAreaView>
    );
  }

  // If not signed in, redirect to sign-in page
  if (!isSignedIn) {
    return <Redirect href="/sign-in" />;
  }

  // If signed in, check if user exists in Supabase and has teacher role
  if (isSignedIn && supabaseUser) {
    // Check if user has teacher role
    if (supabaseUser.role !== 'teacher') {
      // For non-teacher users, redirect to sign-in
      console.warn('User is not a teacher:', supabaseUser.role);
      return <Redirect href="/sign-in" />;
    }

    // Check if teacher has a tenant_id (is associated with a school)
    if (!supabaseUser.tenant_id) {
      console.warn('Teacher is not associated with any school');
      return <Redirect href="/sign-in" />;
    }

    // Teacher is properly authenticated and associated with a school
    return <Redirect href="/(tabs)/home" />;
  }

  // If signed in but no Supabase user data yet, wait for it to load
  if (isSignedIn && !supabaseUser) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDark
            ? Colors.dark.background
            : Colors.light.background,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ActivityIndicator
            size="large"
            color={isDark ? Colors.dark.primary : Colors.light.primary}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Default fallback
  return <Redirect href="/sign-in" />;
}
