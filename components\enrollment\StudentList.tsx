import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useEnrollmentStore, type Class, type ClassStudent } from '@/stores/enrollmentStore';
import React from 'react';
import { Alert, FlatList, RefreshControl, Text, TouchableOpacity, View } from 'react-native';

interface StudentListProps {
  selectedClass: Class | null;
  onStudentPress?: (student: ClassStudent) => void;
}

export const StudentList: React.FC<StudentListProps> = ({
  selectedClass,
  onStudentPress
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const {
    classStudents,
    removeStudentFromClass,
    loadClassStudents,
    refreshData,
    refreshing,
    isLoading
  } = useEnrollmentStore();

  React.useEffect(() => {
    if (selectedClass) {
      loadClassStudents(selectedClass.id);
    }
  }, [selectedClass, loadClassStudents]);

  const handleRemoveStudent = (student: ClassStudent) => {
    if (!selectedClass || !student.student) return;

    Alert.alert(
      'Remove Student',
      `Are you sure you want to remove ${student.student.name} from ${selectedClass.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => removeStudentFromClass(selectedClass.id, student.student_id)
        }
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: ClassStudent['status']) => {
    switch (status) {
      case 'active':
        return isDark ? '#10B981' : '#059669';
      case 'inactive':
        return isDark ? '#9CA3AF' : '#6B7280';
      case 'transferred':
        return isDark ? '#F59E0B' : '#D97706';
      default:
        return isDark ? '#9CA3AF' : '#6B7280';
    }
  };

  const getStatusIcon = (status: ClassStudent['status']) => {
    switch (status) {
      case 'active':
        return 'checkmark.circle.fill';
      case 'inactive':
        return 'pause.circle.fill';
      case 'transferred':
        return 'arrow.right.circle.fill';
      default:
        return 'questionmark.circle.fill';
    }
  };

  const renderStudentItem = ({ item }: { item: ClassStudent }) => {
    if (!item.student) return null;

    return (
      <TouchableOpacity
        onPress={() => onStudentPress?.(item)}
        className={`p-4 rounded-lg mb-3 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
        style={{
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        }}
      >
        {/* Header */}
        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-1">
            <Text className={`font-rubik-semibold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {item.student.name}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              {item.student.email}
            </Text>
          </View>
          <View className="flex-row items-center">
            <IconSymbol
              name={getStatusIcon(item.status)}
              size={20}
              color={getStatusColor(item.status)}
            />
            <Text
              className="ml-2 font-rubik-medium text-sm capitalize"
              style={{ color: getStatusColor(item.status) }}
            >
              {item.status}
            </Text>
          </View>
        </View>

        {/* Student Details */}
        <View className="mb-3">
          {item.student.class && (
            <View className="flex-row mb-1">
              <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Grade:
              </Text>
              <Text className={`font-rubik ml-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {item.student.class}
              </Text>
            </View>
          )}
          {item.student.section && (
            <View className="flex-row mb-1">
              <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Section:
              </Text>
              <Text className={`font-rubik ml-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {item.student.section}
              </Text>
            </View>
          )}
          {item.student.roll_number && (
            <View className="flex-row mb-1">
              <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Roll Number:
              </Text>
              <Text className={`font-rubik ml-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {item.student.roll_number}
              </Text>
            </View>
          )}
        </View>

        {/* Enrollment Code */}
        {item.student.enrollment_code && (
          <View className="mb-3">
            <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Enrollment Code:
            </Text>
            <Text className={`font-rubik-bold ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
              {item.student.enrollment_code}
            </Text>
          </View>
        )}

        {/* Footer */}
        <View className="flex-row items-center justify-between">
          <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Enrolled: {formatDate(item.enrollment_date)}
          </Text>

          {/* Action Buttons */}
          {item.status === 'active' && (
            <View className="flex-row">
              <TouchableOpacity
                onPress={() => handleRemoveStudent(item)}
                disabled={isLoading}
                className="px-3 py-1 rounded bg-error"
              >
                <Text className="text-white font-rubik-medium text-sm">Remove</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center py-12">
      <IconSymbol
        name="person.2.fill"
        size={48}
        color={isDark ? '#9CA3AF' : '#6B7280'}
      />
      <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        No students enrolled
      </Text>
      <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        {selectedClass ? `No students in ${selectedClass.name} yet` : 'Select a class to view students'}
      </Text>
    </View>
  );

  const renderLoadingState = () => (
    <View className="flex-1 justify-center items-center py-12">
      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        Loading students...
      </Text>
    </View>
  );

  if (!selectedClass) {
    return (
      <View className="flex-1 justify-center items-center">
        <IconSymbol
          name="building.2.fill"
          size={48}
          color={isDark ? '#9CA3AF' : '#6B7280'}
        />
        <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
          Select a class
        </Text>
        <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
          Choose a class to view enrolled students
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1">
      {/* Class Header */}
      <View className={`p-4 border-b ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'}`}>
        <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {selectedClass.name}
        </Text>
        {(selectedClass.grade || selectedClass.section) && (
          <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            {selectedClass.grade && `Grade ${selectedClass.grade}`}
            {selectedClass.grade && selectedClass.section && ' • '}
            {selectedClass.section && `Section ${selectedClass.section}`}
          </Text>
        )}
        <Text className={`font-rubik text-sm mt-1 ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
          {classStudents.length} students enrolled
        </Text>
      </View>

      {/* Student List */}
      {isLoading ? (
        renderLoadingState()
      ) : (
        <FlatList
          data={classStudents}
          renderItem={renderStudentItem}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={refreshData}
              tintColor={isDark ? '#60A5FA' : '#2563EB'}
            />
          }
          contentContainerStyle={{
            flexGrow: 1,
            padding: 16
          }}
        />
      )}
    </View>
  );
};
