import { Link, Redirect } from 'expo-router';
import { Text, View, Button } from 'react-native';

export default function TestPage() {
  console.log('Test page loaded!');
  
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', gap: 20 }}>
      <Text style={{ fontSize: 24 }}>Test Page</Text>
      <Link href="/" asChild>
        <Button title="Go to Index" />
      </Link>
      <Link href="/sign-in" asChild>
        <Button title="Go to Sign In" />
      </Link>
      <Link href="/(tabs)/home" asChild>
        <Button title="Go to Home" />
      </Link>
    </View>
  );
}
