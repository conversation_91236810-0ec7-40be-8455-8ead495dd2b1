import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface NotificationPreferences {
  deadlineReminders: {
    enabled: boolean;
    timing: '1hour' | '2hours' | '1day' | '2days';
  };
  submissionNotifications: {
    enabled: boolean;
    immediate: boolean;
    digest: boolean;
  };
  gradeNotifications: {
    enabled: boolean;
    immediate: boolean;
  };
  lateSubmissionAlerts: {
    enabled: boolean;
    teacherOnly: boolean;
  };
  missingSubmissionReminders: {
    enabled: boolean;
    frequency: 'daily' | 'weekly';
  };
  emailNotifications: {
    enabled: boolean;
    types: string[];
  };
  pushNotifications: {
    enabled: boolean;
    quiet_hours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
}

interface NotificationSettingsProps {
  userId: string;
  userRole: 'teacher' | 'student';
  onSettingsChange?: (settings: NotificationPreferences) => void;
}

export default function NotificationSettings({
  userId,
  userRole,
  onSettingsChange,
}: NotificationSettingsProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const [preferences, setPreferences] = useState<NotificationPreferences>({
    deadlineReminders: {
      enabled: true,
      timing: '2hours',
    },
    submissionNotifications: {
      enabled: true,
      immediate: true,
      digest: false,
    },
    gradeNotifications: {
      enabled: true,
      immediate: true,
    },
    lateSubmissionAlerts: {
      enabled: true,
      teacherOnly: true,
    },
    missingSubmissionReminders: {
      enabled: true,
      frequency: 'daily',
    },
    emailNotifications: {
      enabled: false,
      types: [],
    },
    pushNotifications: {
      enabled: true,
      quiet_hours: {
        enabled: true,
        start: '22:00',
        end: '07:00',
      },
    },
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadUserPreferences();
  }, [userId]);

  const loadUserPreferences = async () => {
    try {
      // TODO: Load user preferences from API
      // For now, using default preferences
    } catch (error) {
      console.error('Error loading notification preferences:', error);
    }
  };

  const savePreferences = async () => {
    try {
      setLoading(true);
      // TODO: Save preferences to API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      onSettingsChange?.(preferences);
      Alert.alert('Success', 'Notification preferences saved successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      Alert.alert('Error', 'Failed to save notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = (section: keyof NotificationPreferences, key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
  };

  const renderSettingSection = (
    title: string,
    description: string,
    children: React.ReactNode,
    delay: number = 0
  ) => (
    <Animated.View
      entering={FadeInDown.delay(delay).duration(400)}
      className={`p-4 mb-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}
    >
      <Text className={`font-rubik-bold text-lg mb-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        {title}
      </Text>
      <Text className={`font-rubik text-sm mb-4 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
        {description}
      </Text>
      {children}
    </Animated.View>
  );

  const renderToggleSetting = (
    label: string,
    value: boolean,
    onToggle: (value: boolean) => void,
    description?: string
  ) => (
    <View className="flex-row items-center justify-between py-2">
      <View className="flex-1 mr-3">
        <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {label}
        </Text>
        {description && (
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            {description}
          </Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: "#767577", true: "#007AFF40" }}
        thumbColor={value ? "#007AFF" : "#f4f3f4"}
      />
    </View>
  );

  const renderPickerSetting = (
    label: string,
    value: string,
    options: { label: string; value: string }[],
    onSelect: (value: string) => void
  ) => (
    <View className="py-2">
      <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        {label}
      </Text>
      <View className="flex-row flex-wrap gap-2">
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            onPress={() => onSelect(option.value)}
            className={`px-3 py-2 rounded-lg ${
              value === option.value
                ? 'bg-primary-500'
                : isDark
                ? 'bg-dark-background'
                : 'bg-light-background'
            }`}
          >
            <Text
              className={`font-rubik-medium text-sm ${
                value === option.value
                  ? 'text-white'
                  : isDark
                  ? 'text-dark-text'
                  : 'text-light-text'
              }`}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="p-4 border-b border-gray-200 dark:border-gray-700">
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Notification Settings
        </Text>
        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          Customize how you receive assignment notifications
        </Text>
      </View>

      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {/* Deadline Reminders */}
        {renderSettingSection(
          'Deadline Reminders',
          'Get notified before assignment deadlines',
          <View>
            {renderToggleSetting(
              'Enable deadline reminders',
              preferences.deadlineReminders.enabled,
              (value) => updatePreference('deadlineReminders', 'enabled', value)
            )}
            {preferences.deadlineReminders.enabled && (
              <View className="ml-4 mt-2">
                {renderPickerSetting(
                  'Remind me',
                  preferences.deadlineReminders.timing,
                  [
                    { label: '1 hour before', value: '1hour' },
                    { label: '2 hours before', value: '2hours' },
                    { label: '1 day before', value: '1day' },
                    { label: '2 days before', value: '2days' },
                  ],
                  (value) => updatePreference('deadlineReminders', 'timing', value)
                )}
              </View>
            )}
          </View>,
          100
        )}

        {/* Submission Notifications */}
        {userRole === 'teacher' && renderSettingSection(
          'Submission Notifications',
          'Get notified when students submit assignments',
          <View>
            {renderToggleSetting(
              'Enable submission notifications',
              preferences.submissionNotifications.enabled,
              (value) => updatePreference('submissionNotifications', 'enabled', value)
            )}
            {preferences.submissionNotifications.enabled && (
              <View className="ml-4 space-y-2">
                {renderToggleSetting(
                  'Immediate notifications',
                  preferences.submissionNotifications.immediate,
                  (value) => updatePreference('submissionNotifications', 'immediate', value),
                  'Get notified as soon as submissions are received'
                )}
                {renderToggleSetting(
                  'Daily digest',
                  preferences.submissionNotifications.digest,
                  (value) => updatePreference('submissionNotifications', 'digest', value),
                  'Receive a summary of all submissions once per day'
                )}
              </View>
            )}
          </View>,
          200
        )}

        {/* Grade Notifications */}
        {renderSettingSection(
          'Grade Notifications',
          userRole === 'teacher' 
            ? 'Get notified about grading activities'
            : 'Get notified when grades are released',
          <View>
            {renderToggleSetting(
              userRole === 'teacher' ? 'Enable grading notifications' : 'Enable grade notifications',
              preferences.gradeNotifications.enabled,
              (value) => updatePreference('gradeNotifications', 'enabled', value)
            )}
            {preferences.gradeNotifications.enabled && (
              <View className="ml-4">
                {renderToggleSetting(
                  'Immediate notifications',
                  preferences.gradeNotifications.immediate,
                  (value) => updatePreference('gradeNotifications', 'immediate', value),
                  userRole === 'teacher' 
                    ? 'Get notified when grades need to be assigned'
                    : 'Get notified immediately when grades are released'
                )}
              </View>
            )}
          </View>,
          300
        )}

        {/* Late Submission Alerts */}
        {userRole === 'teacher' && renderSettingSection(
          'Late Submission Alerts',
          'Get notified about late submissions',
          <View>
            {renderToggleSetting(
              'Enable late submission alerts',
              preferences.lateSubmissionAlerts.enabled,
              (value) => updatePreference('lateSubmissionAlerts', 'enabled', value)
            )}
          </View>,
          400
        )}

        {/* Push Notifications */}
        {renderSettingSection(
          'Push Notifications',
          'Control when you receive push notifications',
          <View>
            {renderToggleSetting(
              'Enable push notifications',
              preferences.pushNotifications.enabled,
              (value) => updatePreference('pushNotifications', 'enabled', value)
            )}
            {preferences.pushNotifications.enabled && (
              <View className="ml-4">
                {renderToggleSetting(
                  'Quiet hours',
                  preferences.pushNotifications.quiet_hours.enabled,
                  (value) => updatePreference('pushNotifications', 'quiet_hours', {
                    ...preferences.pushNotifications.quiet_hours,
                    enabled: value,
                  }),
                  'Disable notifications during specified hours (10 PM - 7 AM)'
                )}
              </View>
            )}
          </View>,
          500
        )}

        {/* Email Notifications */}
        {renderSettingSection(
          'Email Notifications',
          'Receive notifications via email',
          <View>
            {renderToggleSetting(
              'Enable email notifications',
              preferences.emailNotifications.enabled,
              (value) => updatePreference('emailNotifications', 'enabled', value),
              'Get important notifications sent to your email address'
            )}
          </View>,
          600
        )}
      </ScrollView>

      {/* Save Button */}
      <View className="p-4 border-t border-gray-200 dark:border-gray-700">
        <TouchableOpacity
          onPress={savePreferences}
          disabled={loading}
          className={`py-3 rounded-xl ${
            loading ? 'bg-gray-400' : 'bg-primary-500'
          }`}
        >
          <Text className="text-white text-center font-rubik-medium">
            {loading ? 'Saving...' : 'Save Preferences'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
