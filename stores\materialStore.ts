import { CACHE_CONFIG, cacheManager } from '@/lib/cache';
import { generateMaterialWithGemini } from '@/lib/gemini';
import { supabase } from '@/lib/supabase';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types for materials system
export interface Material {
  id: string;
  tenant_id: string;
  uploaded_by: string;
  title: string;
  description?: string;
  content?: string;
  file_url?: string;
  file_type?: string;
  file_size?: number;
  subject?: string;
  grade_level?: string;
  tags?: string[];
  material_type: 'lesson_plan' | 'worksheet' | 'quiz' | 'resource' | 'assignment' | 'presentation';
  visibility: 'private' | 'class' | 'school' | 'public';
  gemini_generated: boolean;
  download_count: number;
  view_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  uploaded_by_name?: string;
}

export interface MaterialShare {
  id: string;
  tenant_id: string;
  material_id: string;
  shared_with_type: 'teacher' | 'class' | 'student';
  shared_with_id: string;
  shared_by: string;
  permissions: 'view' | 'download' | 'edit';
  shared_at: string;
  expires_at?: string;
}

export interface CurriculumPlan {
  id: string;
  tenant_id: string;
  teacher_id: string;
  class_id?: string;
  title: string;
  description?: string;
  subject: string;
  grade_level?: string;
  academic_year?: string;
  semester?: string;
  start_date?: string;
  end_date?: string;
  total_lessons: number;
  completed_lessons: number;
  status: 'draft' | 'active' | 'completed' | 'archived';
  created_at: string;
  updated_at: string;
}

export interface CurriculumLesson {
  id: string;
  tenant_id: string;
  curriculum_plan_id: string;
  lesson_number: number;
  title: string;
  description?: string;
  objectives?: string[];
  duration_minutes?: number;
  lesson_date?: string;
  status: 'planned' | 'in_progress' | 'completed' | 'skipped';
  notes?: string;
  materials_used?: string[];
  homework_assigned?: string;
  created_at: string;
  updated_at: string;
}

export interface MaterialCategory {
  id: string;
  tenant_id: string;
  name: string;
  description?: string;
  parent_category_id?: string;
  color_code?: string;
  icon_name?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
}

export interface MaterialStats {
  total_materials: number;
  total_views: number;
  total_downloads: number;
  materials_by_type: Record<string, number>;
  recent_materials: number;
}

export interface MaterialFilters {
  search_query?: string;
  subject?: string;
  material_type?: string;
  grade_level?: string;
  tags?: string[];
  category_id?: string;
  visibility?: string;
}

interface MaterialState {
  // Data
  materials: Material[];
  sharedMaterials: Material[];
  curriculumPlans: CurriculumPlan[];
  curriculumLessons: CurriculumLesson[];
  categories: MaterialCategory[];
  materialShares: MaterialShare[];
  materialStats: MaterialStats | null;

  // Selected items
  selectedMaterial: Material | null;
  selectedCurriculumPlan: CurriculumPlan | null;
  selectedLesson: CurriculumLesson | null;

  // UI state
  isLoading: boolean;
  isSaving: boolean;
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  refreshing: boolean;

  // Filters
  filters: MaterialFilters;

  // Actions - Materials
  loadMaterials: (teacherId: string, filters?: MaterialFilters) => Promise<void>;
  loadSharedMaterials: (teacherId: string) => Promise<void>;
  createMaterial: (materialData: Omit<Material, 'id' | 'tenant_id' | 'created_at' | 'updated_at' | 'download_count' | 'view_count'>) => Promise<Material | null>;
  updateMaterial: (id: string, updates: Partial<Material>) => Promise<void>;
  deleteMaterial: (id: string) => Promise<void>;
  uploadMaterialFile: (file: DocumentPicker.DocumentPickerAsset, materialId?: string) => Promise<string | null>;
  shareMaterial: (materialId: string, shareData: Omit<MaterialShare, 'id' | 'tenant_id' | 'material_id' | 'shared_at'>) => Promise<void>;
  incrementViewCount: (materialId: string) => Promise<void>;
  incrementDownloadCount: (materialId: string) => Promise<void>;

  // Actions - Curriculum
  loadCurriculumPlans: (teacherId: string) => Promise<void>;
  createCurriculumPlan: (planData: Omit<CurriculumPlan, 'id' | 'tenant_id' | 'created_at' | 'updated_at' | 'total_lessons' | 'completed_lessons'>) => Promise<CurriculumPlan | null>;
  updateCurriculumPlan: (id: string, updates: Partial<CurriculumPlan>) => Promise<void>;
  deleteCurriculumPlan: (id: string) => Promise<void>;
  loadCurriculumLessons: (curriculumPlanId: string) => Promise<void>;
  createCurriculumLesson: (lessonData: Omit<CurriculumLesson, 'id' | 'tenant_id' | 'created_at' | 'updated_at'>) => Promise<CurriculumLesson | null>;
  updateCurriculumLesson: (id: string, updates: Partial<CurriculumLesson>) => Promise<void>;
  deleteCurriculumLesson: (id: string) => Promise<void>;

  // Actions - Categories
  loadCategories: (tenantId: string) => Promise<void>;
  createCategory: (categoryData: Omit<MaterialCategory, 'id' | 'tenant_id' | 'created_at'>) => Promise<MaterialCategory | null>;

  // Actions - AI
  generateMaterialWithAI: (prompt: string, materialType: string, subject: string, gradeLevel: string, teacherId: string) => Promise<Material | null>;

  // Actions - Stats
  loadMaterialStats: (teacherId: string) => Promise<void>;

  // Actions - Utility
  setFilters: (filters: MaterialFilters) => void;
  setSelectedMaterial: (material: Material | null) => void;
  setSelectedCurriculumPlan: (plan: CurriculumPlan | null) => void;
  setSelectedLesson: (lesson: CurriculumLesson | null) => void;
  searchMaterials: (query: string, teacherId: string) => Promise<void>;
  refreshData: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export const useMaterialStore = create<MaterialState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      materials: [],
      sharedMaterials: [],
      curriculumPlans: [],
      curriculumLessons: [],
      categories: [],
      materialShares: [],
      materialStats: null,
      selectedMaterial: null,
      selectedCurriculumPlan: null,
      selectedLesson: null,
      isLoading: false,
      isSaving: false,
      isUploading: false,
      uploadProgress: 0,
      error: null,
      refreshing: false,
      filters: {},

      // Load materials for a teacher
      loadMaterials: async (teacherId: string, filters?: MaterialFilters) => {
        const cacheKey = `materials_${teacherId}_${JSON.stringify(filters || {})}`;

        try {
          console.log('Loading materials for teacher:', teacherId);
          console.log('Current filters:', filters);
          
          // Clear the cache before loading to ensure fresh data
          await cacheManager.delete(cacheKey);
          
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const materials = await cacheManager.get<Material[]>(
            cacheKey,
            async () => {
              // Try using the RPC function first, fallback to direct query
              try {
                console.log('Attempting to use RPC function...');
                const { data: rpcData, error: rpcError } = await supabase
                  .rpc('search_materials', {
                    p_teacher_id: teacherId,
                    p_search_query: filters?.search_query || null,
                    p_subject: filters?.subject || null,
                    p_material_type: filters?.material_type || null,
                    p_grade_level: filters?.grade_level || null,
                    p_tags: filters?.tags || null,
                    p_limit: 100,
                    p_offset: 0
                  });

                if (rpcError) {
                  console.error('RPC error:', rpcError);
                  throw rpcError;
                }
                
                console.log('RPC function returned data:', rpcData);
                return rpcData as Material[];
              } catch (rpcError) {
                console.warn('RPC function not available, using direct query:', rpcError);
                
                // Fallback to direct query
                let query = supabase
                  .from('materials_with_teachers')
                  .select('*')
                  .eq('uploaded_by', teacherId)
                  .order('created_at', { ascending: false });

                if (filters?.search_query) {
                  query = query.or(
                    `title.ilike.%${filters.search_query}%,description.ilike.%${filters.search_query}%`
                  );
                }

                if (filters?.subject) {
                  query = query.eq('subject', filters.subject);
                }

                if (filters?.material_type) {
                  query = query.eq('material_type', filters.material_type);
                }

                if (filters?.grade_level) {
                  query = query.eq('grade_level', filters.grade_level);
                }

                if (filters?.tags && filters.tags.length > 0) {
                  query = query.contains('tags', filters.tags);
                }

                console.log('Executing direct query...');
                const { data: queryData, error: queryError } = await query;

                if (queryError) {
                  console.error('Direct query error:', queryError);
                  throw queryError;
                }

                console.log('Direct query returned data:', queryData);
                return queryData as Material[];
              }
            },
            CACHE_CONFIG.ASSIGNMENTS
          );

          console.log('Final materials data:', materials);
          
          set((state) => {
            state.materials = materials || [];
            state.isLoading = false;
          });
        } catch (error) {
          console.error('Error loading materials:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load materials';
            state.isLoading = false;
            state.materials = [];
          });
        }
      },

      // Load shared materials
      loadSharedMaterials: async (teacherId: string) => {
        const cacheKey = `shared_materials_${teacherId}`;

        try {
          const sharedMaterials = await cacheManager.get<Material[]>(
            cacheKey,
            async () => {
              const { data, error } = await supabase
                .from('materials')
                .select(`
                  *,
                  uploaded_by_name:teachers!uploaded_by(
                    name,
                    email
                  )
                `)
                .in('visibility', ['school', 'public'])
                .neq('uploaded_by', teacherId)
                .order('created_at', { ascending: false });

              if (error) throw new Error(`Failed to load shared materials: ${error.message}`);

              // Also get materials shared directly with this teacher
              const { data: directShares, error: sharesError } = await supabase
                .from('material_shares')
                .select(`
                  materials (
                    *,
                    uploaded_by_name:teachers!uploaded_by(
                      name,
                      email
                    )
                  )
                `)
                .eq('shared_with_type', 'teacher')
                .eq('shared_with_id', teacherId);

              if (sharesError) throw new Error(`Failed to load direct shares: ${sharesError.message}`);

              // Combine and deduplicate materials
              const allMaterials = [
                ...(data || []),
                ...(directShares?.map(share => share.materials) || [])
              ].filter((material): material is Material => !!material);

              // Remove duplicates by id
              const uniqueMaterials = Array.from(
                new Map(allMaterials.map(item => [item.id, item])).values()
              );

              return uniqueMaterials;
            },
            CACHE_CONFIG.ASSIGNMENTS
          );

          set((state) => {
            state.sharedMaterials = sharedMaterials || [];
          });
        } catch (error) {
          console.error('Error loading shared materials:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load shared materials';
            state.sharedMaterials = [];
          });
        }
      },

      // Create new material
      createMaterial: async (materialData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher from enrollment store
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { data, error } = await supabase
            .from('materials')
            .insert({
              ...materialData,
              tenant_id: currentTeacher.tenant_id,
            })
            .select()
            .single();

          if (error) throw new Error(`Failed to create material: ${error.message}`);

          const newMaterial = data as Material;

          set((state) => {
            state.materials.unshift(newMaterial);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`materials_${materialData.uploaded_by}`);

          return newMaterial;
        } catch (error) {
          console.error('Error creating material:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create material';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Update material
      updateMaterial: async (id, updates) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const { error } = await supabase
            .from('materials')
            .update({
              ...updates,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id);

          if (error) throw new Error(`Failed to update material: ${error.message}`);

          set((state) => {
            const index = state.materials.findIndex(m => m.id === id);
            if (index !== -1) {
              state.materials[index] = { ...state.materials[index], ...updates };
            }
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('materials_');
        } catch (error) {
          console.error('Error updating material:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update material';
            state.isSaving = false;
          });
        }
      },

      // Delete material
      deleteMaterial: async (id) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // First, delete any associated shares
          const { error: sharesError } = await supabase
            .from('material_shares')
            .delete()
            .eq('material_id', id);

          if (sharesError) {
            console.error('Error deleting material shares:', sharesError);
          }

          // Get the material to delete its file if it exists
          const { data: material } = await supabase
            .from('materials')
            .select('file_url')
            .eq('id', id)
            .single();

          // Delete the file from storage if it exists
          if (material?.file_url) {
            const fileKey = material.file_url.split('/').pop();
            if (fileKey) {
              const { error: storageError } = await supabase.storage
                .from('materials')
                .remove([`materials/${fileKey}`]);

              if (storageError) {
                console.error('Error deleting material file:', storageError);
              }
            }
          }

          // Finally, delete the material record
          const { error } = await supabase
            .from('materials')
            .delete()
            .eq('id', id);

          if (error) throw new Error(`Failed to delete material: ${error.message}`);

          set((state) => {
            state.materials = state.materials.filter(m => m.id !== id);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('materials_');
        } catch (error) {
          console.error('Error deleting material:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete material';
            state.isSaving = false;
          });
        }
      },

      // Upload material file
      uploadMaterialFile: async (file, materialId) => {
        try {
          set((state) => {
            state.isUploading = true;
            state.uploadProgress = 0;
            state.error = null;
          });

          // Clean filename
          const cleanFileName = file.name.replace(/[^a-zA-Z0-9.]/g, '_');
          const filePath = `materials/${Date.now()}_${cleanFileName}`;

          // Read file as base64
          const fileContent = await FileSystem.readAsStringAsync(file.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          // Convert to binary
          const binaryData = Uint8Array.from(atob(fileContent), (c) => c.charCodeAt(0));

          // Upload to Supabase Storage
          const { data, error } = await supabase.storage
            .from('materials')
            .upload(filePath, binaryData, {
              contentType: file.mimeType || 'application/octet-stream',
              upsert: true,
            });

          if (error) throw new Error(`Failed to upload file: ${error.message}`);

          // Get public URL
          const { data: urlData } = supabase.storage
            .from('materials')
            .getPublicUrl(filePath);

          set((state) => {
            state.isUploading = false;
            state.uploadProgress = 100;
          });

          return urlData.publicUrl;
        } catch (error) {
          console.error('Error uploading file:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to upload file';
            state.isUploading = false;
            state.uploadProgress = 0;
          });
          return null;
        }
      },

      // Share material
      shareMaterial: async (materialId, shareData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher from enrollment store
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { error } = await supabase
            .from('material_shares')
            .insert({
              ...shareData,
              material_id: materialId,
              tenant_id: currentTeacher.tenant_id,
            });

          if (error) throw new Error(`Failed to share material: ${error.message}`);

          set((state) => {
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('materials_');
        } catch (error) {
          console.error('Error sharing material:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to share material';
            state.isSaving = false;
          });
        }
      },

      // Increment view count
      incrementViewCount: async (materialId) => {
        try {
          await supabase.rpc('increment_material_view_count', {
            material_id: materialId
          });

          set((state) => {
            const material = state.materials.find(m => m.id === materialId);
            if (material) {
              material.view_count += 1;
            }
          });
        } catch (error) {
          console.error('Error incrementing view count:', error);
        }
      },

      // Increment download count
      incrementDownloadCount: async (materialId) => {
        try {
          await supabase.rpc('increment_material_download_count', {
            material_id: materialId
          });

          set((state) => {
            const material = state.materials.find(m => m.id === materialId);
            if (material) {
              material.download_count += 1;
            }
          });
        } catch (error) {
          console.error('Error incrementing download count:', error);
        }
      },

      // Load curriculum plans
      loadCurriculumPlans: async (teacherId) => {
        const cacheKey = `curriculum_plans_${teacherId}`;

        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const plans = await cacheManager.get<CurriculumPlan[]>(
            cacheKey,
            async () => {
              const { data, error } = await supabase
                .from('curriculum_plans')
                .select('*')
                .eq('teacher_id', teacherId)
                .order('created_at', { ascending: false });

              if (error) throw new Error(`Failed to load curriculum plans: ${error.message}`);
              return data as CurriculumPlan[];
            },
            CACHE_CONFIG.ASSIGNMENTS
          );

          set((state) => {
            state.curriculumPlans = plans || [];
            state.isLoading = false;
          });
        } catch (error) {
          console.error('Error loading curriculum plans:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load curriculum plans';
            state.isLoading = false;
          });
        }
      },

      // Create curriculum plan
      createCurriculumPlan: async (planData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher from enrollment store
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { data, error } = await supabase
            .from('curriculum_plans')
            .insert({
              ...planData,
              tenant_id: currentTeacher.tenant_id,
            })
            .select()
            .single();

          if (error) throw new Error(`Failed to create curriculum plan: ${error.message}`);

          const newPlan = data as CurriculumPlan;

          set((state) => {
            state.curriculumPlans.unshift(newPlan);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`curriculum_plans_${planData.teacher_id}`);

          return newPlan;
        } catch (error) {
          console.error('Error creating curriculum plan:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create curriculum plan';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Update curriculum plan
      updateCurriculumPlan: async (id, updates) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const { error } = await supabase
            .from('curriculum_plans')
            .update({
              ...updates,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id);

          if (error) throw new Error(`Failed to update curriculum plan: ${error.message}`);

          set((state) => {
            const index = state.curriculumPlans.findIndex(p => p.id === id);
            if (index !== -1) {
              state.curriculumPlans[index] = { ...state.curriculumPlans[index], ...updates };
            }
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('curriculum_plans_');
        } catch (error) {
          console.error('Error updating curriculum plan:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update curriculum plan';
            state.isSaving = false;
          });
        }
      },

      // Delete curriculum plan
      deleteCurriculumPlan: async (id) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const { error } = await supabase
            .from('curriculum_plans')
            .delete()
            .eq('id', id);

          if (error) throw new Error(`Failed to delete curriculum plan: ${error.message}`);

          set((state) => {
            state.curriculumPlans = state.curriculumPlans.filter(p => p.id !== id);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('curriculum_plans_');
        } catch (error) {
          console.error('Error deleting curriculum plan:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete curriculum plan';
            state.isSaving = false;
          });
        }
      },

      // Load curriculum lessons
      loadCurriculumLessons: async (curriculumPlanId) => {
        const cacheKey = `curriculum_lessons_${curriculumPlanId}`;

        try {
          const lessons = await cacheManager.get<CurriculumLesson[]>(
            cacheKey,
            async () => {
              const { data, error } = await supabase
                .from('curriculum_lessons')
                .select('*')
                .eq('curriculum_plan_id', curriculumPlanId)
                .order('lesson_number', { ascending: true });

              if (error) throw new Error(`Failed to load curriculum lessons: ${error.message}`);
              return data as CurriculumLesson[];
            },
            CACHE_CONFIG.ASSIGNMENTS
          );

          set((state) => {
            state.curriculumLessons = lessons || [];
          });
        } catch (error) {
          console.error('Error loading curriculum lessons:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load curriculum lessons';
          });
        }
      },

      // Create curriculum lesson
      createCurriculumLesson: async (lessonData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher from enrollment store
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { data, error } = await supabase
            .from('curriculum_lessons')
            .insert({
              ...lessonData,
              tenant_id: currentTeacher.tenant_id,
            })
            .select()
            .single();

          if (error) throw new Error(`Failed to create curriculum lesson: ${error.message}`);

          const newLesson = data as CurriculumLesson;

          set((state) => {
            state.curriculumLessons.push(newLesson);
            state.curriculumLessons.sort((a, b) => a.lesson_number - b.lesson_number);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`curriculum_lessons_${lessonData.curriculum_plan_id}`);

          return newLesson;
        } catch (error) {
          console.error('Error creating curriculum lesson:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create curriculum lesson';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Update curriculum lesson
      updateCurriculumLesson: async (id, updates) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const { error } = await supabase
            .from('curriculum_lessons')
            .update({
              ...updates,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id);

          if (error) throw new Error(`Failed to update curriculum lesson: ${error.message}`);

          set((state) => {
            const index = state.curriculumLessons.findIndex(l => l.id === id);
            if (index !== -1) {
              state.curriculumLessons[index] = { ...state.curriculumLessons[index], ...updates };
            }
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('curriculum_lessons_');
        } catch (error) {
          console.error('Error updating curriculum lesson:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update curriculum lesson';
            state.isSaving = false;
          });
        }
      },

      // Delete curriculum lesson
      deleteCurriculumLesson: async (id) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const { error } = await supabase
            .from('curriculum_lessons')
            .delete()
            .eq('id', id);

          if (error) throw new Error(`Failed to delete curriculum lesson: ${error.message}`);

          set((state) => {
            state.curriculumLessons = state.curriculumLessons.filter(l => l.id !== id);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern('curriculum_lessons_');
        } catch (error) {
          console.error('Error deleting curriculum lesson:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete curriculum lesson';
            state.isSaving = false;
          });
        }
      },

      // Load categories
      loadCategories: async (tenantId) => {
        const cacheKey = `material_categories_${tenantId}`;

        try {
          const categories = await cacheManager.get<MaterialCategory[]>(
            cacheKey,
            async () => {
              const { data, error } = await supabase
                .from('material_categories')
                .select('*')
                .eq('tenant_id', tenantId)
                .eq('is_active', true)
                .order('sort_order', { ascending: true });

              if (error) throw new Error(`Failed to load categories: ${error.message}`);
              return data as MaterialCategory[];
            },
            CACHE_CONFIG.CLASSES // 10 minutes cache
          );

          set((state) => {
            state.categories = categories || [];
          });
        } catch (error) {
          console.error('Error loading categories:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load categories';
          });
        }
      },

      // Create category
      createCategory: async (categoryData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher from enrollment store
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { data, error } = await supabase
            .from('material_categories')
            .insert({
              ...categoryData,
              tenant_id: currentTeacher.tenant_id,
            })
            .select()
            .single();

          if (error) throw new Error(`Failed to create category: ${error.message}`);

          const newCategory = data as MaterialCategory;

          set((state) => {
            state.categories.push(newCategory);
            state.categories.sort((a, b) => a.sort_order - b.sort_order);
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`material_categories_${currentTeacher.tenant_id}`);

          return newCategory;
        } catch (error) {
          console.error('Error creating category:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create category';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Generate material with AI
      generateMaterialWithAI: async (prompt, materialType, subject, gradeLevel, teacherId) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const generatedContent = await generateMaterialWithGemini(prompt, materialType, subject, gradeLevel);

          if (!generatedContent) {
            throw new Error('Failed to generate content with AI');
          }

          const materialData = {
            uploaded_by: teacherId,
            title: generatedContent.title,
            description: generatedContent.description,
            content: generatedContent.content,
            subject,
            grade_level: gradeLevel,
            material_type: materialType as Material['material_type'],
            visibility: 'private' as Material['visibility'],
            gemini_generated: true,
            is_active: true,
            download_count: 0,
            view_count: 0,
            tags: generatedContent.tags || [],
          };

          const material = await get().createMaterial(materialData);

          set((state) => {
            state.isSaving = false;
          });

          return material;
        } catch (error) {
          console.error('Error generating material with AI:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to generate material with AI';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Load material stats
      loadMaterialStats: async (teacherId) => {
        const cacheKey = `material_stats_${teacherId}`;

        try {
          const stats = await cacheManager.get<MaterialStats>(
            cacheKey,
            async () => {
              const { data, error } = await supabase
                .rpc('get_teacher_material_stats', {
                  teacher_id: teacherId
                });

              if (error) throw new Error(`Failed to load material stats: ${error.message}`);
              return data?.[0] as MaterialStats;
            },
            CACHE_CONFIG.TEACHER_STATS // 2 minutes cache
          );

          set((state) => {
            state.materialStats = stats || {
              total_materials: 0,
              total_views: 0,
              total_downloads: 0,
              materials_by_type: {},
              recent_materials: 0
            };
          });
        } catch (error) {
          console.error('Error loading material stats:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load material stats';
          });
        }
      },

      // Set filters
      setFilters: (filters) => {
        set((state) => {
          state.filters = { ...state.filters, ...filters };
        });
      },

      // Set selected material
      setSelectedMaterial: (material) => {
        set((state) => {
          state.selectedMaterial = material;
        });
      },

      // Set selected curriculum plan
      setSelectedCurriculumPlan: (plan) => {
        set((state) => {
          state.selectedCurriculumPlan = plan;
        });
      },

      // Set selected lesson
      setSelectedLesson: (lesson) => {
        set((state) => {
          state.selectedLesson = lesson;
        });
      },

      // Search materials
      searchMaterials: async (query, teacherId) => {
        await get().loadMaterials(teacherId, { search_query: query });
      },

      // Refresh all data
      refreshData: async () => {
        try {
          set((state) => {
            state.refreshing = true;
            state.error = null;
          });

          // Invalidate all material caches
          await cacheManager.invalidatePattern('materials_');
          await cacheManager.invalidatePattern('curriculum_');
          await cacheManager.invalidatePattern('material_categories_');

          set((state) => {
            state.refreshing = false;
          });
        } catch (error) {
          console.error('Error refreshing data:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to refresh data';
            state.refreshing = false;
          });
        }
      },

      // Clear error
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      // Reset store
      reset: () => {
        set((state) => {
          state.materials = [];
          state.sharedMaterials = [];
          state.curriculumPlans = [];
          state.curriculumLessons = [];
          state.categories = [];
          state.materialShares = [];
          state.materialStats = null;
          state.selectedMaterial = null;
          state.selectedCurriculumPlan = null;
          state.selectedLesson = null;
          state.isLoading = false;
          state.isSaving = false;
          state.isUploading = false;
          state.uploadProgress = 0;
          state.error = null;
          state.refreshing = false;
          state.filters = {};
        });
      },
    }))
  )
); 