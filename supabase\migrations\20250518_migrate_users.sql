-- Migrate existing users to the new role-specific tables

-- First, make sure the new tables exist
\i 20250518_create_role_tables.sql

-- Migrate admin users
INSERT INTO admins (id, name, email, tenant_id, clerk_user_id, created_at)
SELECT id, name, email, tenant_id, clerk_user_id, created_at
FROM users
WHERE role = 'admin'
ON CONFLICT (email) DO NOTHING;

-- Migrate teacher users
INSERT INTO teachers (id, name, email, tenant_id, clerk_user_id, created_at)
SELECT id, name, email, tenant_id, clerk_user_id, created_at
FROM users
WHERE role = 'teacher'
ON CONFLICT (email) DO NOTHING;

-- Migrate student users
INSERT INTO students (id, name, email, tenant_id, clerk_user_id, created_at)
SELECT id, name, email, tenant_id, clerk_user_id, created_at
FROM users
WHERE role = 'student'
ON CONFLICT (email) DO NOTHING;

-- Migrate parent users
INSERT INTO parents (id, name, email, tenant_id, clerk_user_id, created_at)
SELECT id, name, email, tenant_id, clerk_user_id, created_at
FROM users
WHERE role = 'parent'
ON CONFLICT (email) DO NOTHING;

-- Create a view to maintain backward compatibility
CREATE OR REPLACE VIEW users_view AS
SELECT id, name, email, tenant_id, clerk_user_id, 'admin' as role, created_at, updated_at FROM admins
UNION ALL
SELECT id, name, email, tenant_id, clerk_user_id, 'teacher' as role, created_at, updated_at FROM teachers
UNION ALL
SELECT id, name, email, tenant_id, clerk_user_id, 'student' as role, created_at, updated_at FROM students
UNION ALL
SELECT id, name, email, tenant_id, clerk_user_id, 'parent' as role, created_at, updated_at FROM parents;

-- Note: We're not dropping the users table yet to maintain backward compatibility
-- You can drop it later when all code has been migrated to use the new tables
-- DROP TABLE users;
