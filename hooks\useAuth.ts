import { useAuth as useClerkAuth } from "@clerk/clerk-expo";
import { useEffect, useState } from "react";

export function useAuth() {
  const { isLoaded, isSignedIn, userId } = useClerkAuth();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (isLoaded) {
      setIsReady(true);
    }
  }, [isLoaded]);

  return {
    isAuthenticated: isSignedIn,
    isLoading: !isReady,
    userId,
  };
} 