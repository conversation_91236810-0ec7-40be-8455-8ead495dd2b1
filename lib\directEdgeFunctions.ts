import { supabaseUrl } from './supabase';

/**
 * Directly calls a Supabase Edge Function using fetch
 * 
 * @param functionName The name of the Edge Function to call
 * @param body The request body
 * @param token The JWT token from Clerk
 * @returns The function response
 */
export const callEdgeFunction = async (
  functionName: string,
  body: any,
  token: string
): Promise<any> => {
  try {
    if (!token) {
      throw new Error('Authentication token is required');
    }

    // Construct the URL for the Edge Function
    const url = `${supabaseUrl}/functions/v1/${functionName}`;
    
    console.log(`Calling Edge Function directly: ${url}`);
    
    // Make the request
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(body),
    });
    
    // Log response status
    console.log(`Edge Function response status: ${response.status}`);
    
    // Parse the response
    const data = await response.json();
    
    // If the response is not ok, throw an error
    if (!response.ok) {
      throw new Error(JSON.stringify({
        status: response.status,
        message: data.message || 'Edge Function returned an error',
        details: data,
      }));
    }
    
    return data;
  } catch (error: any) {
    console.error(`Error calling Edge Function ${functionName}:`, error);
    throw error;
  }
};
