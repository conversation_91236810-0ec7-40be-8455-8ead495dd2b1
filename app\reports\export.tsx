import { useAuth } from '@clerk/clerk-expo';
import * as FileSystem from 'expo-file-system';
import { useRouter } from 'expo-router';
import * as Sharing from 'expo-sharing';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

type ExportFormat = 'csv' | 'json';
type ExportType = 'sessions' | 'students' | 'summary';

const ExportReportsScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { userId: clerkUserId } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { sessions, attendanceStats, loadSessions, loadAttendanceStats } = useAttendanceStore();
  const { currentTeacher, availableClasses, loadTeacherData, loadAvailableClasses } = useEnrollmentStore();

  // Load data
  useEffect(() => {
    const initializeData = async () => {
      if (clerkUserId && !currentTeacher) {
        await loadTeacherData(clerkUserId);
      }
    };
    initializeData();
  }, [clerkUserId, currentTeacher, loadTeacherData]);

  useEffect(() => {
    const loadReportData = async () => {
      if (currentTeacher?.id) {
        try {
          setIsLoading(true);
          setError(null);

          await loadAvailableClasses(currentTeacher.id);

          const today = new Date().toISOString().split('T')[0];
          const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

          await loadSessions(currentTeacher.id, undefined, { start: monthAgo, end: today });
          await loadAttendanceStats(currentTeacher.id);

        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to load export data');
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadReportData();
  }, [currentTeacher?.id, loadAvailableClasses, loadSessions, loadAttendanceStats]);

  const generateCSV = (data: any[], headers: string[]): string => {
    const csvHeaders = headers.join(',');
    const csvRows = data.map(row =>
      headers.map(header => {
        const value = row[header];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    );
    return [csvHeaders, ...csvRows].join('\n');
  };

  const exportSessions = async (format: ExportFormat) => {
    try {
      setIsExporting(true);

      const exportData = sessions.map(session => ({
        session_id: session.id,
        class_name: session.class?.name || 'Unknown',
        subject: session.subject,
        session_date: session.session_date,
        session_time: session.session_time,
        session_type: session.session_type,
        status: session.status,
        total_students: session.total_students,
        present_count: session.present_count,
        absent_count: session.total_students - session.present_count,
        attendance_percentage: session.total_students > 0
          ? ((session.present_count / session.total_students) * 100).toFixed(2)
          : '0.00'
      }));

      let content: string;
      let filename: string;
      let mimeType: string;

      if (format === 'csv') {
        const headers = [
          'session_id', 'class_name', 'subject', 'session_date', 'session_time',
          'session_type', 'status', 'total_students', 'present_count', 'absent_count', 'attendance_percentage'
        ];
        content = generateCSV(exportData, headers);
        filename = `attendance_sessions_${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else {
        content = JSON.stringify(exportData, null, 2);
        filename = `attendance_sessions_${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      }

      const fileUri = FileSystem.documentDirectory + filename;
      await FileSystem.writeAsStringAsync(fileUri, content);

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType,
          dialogTitle: 'Export Attendance Sessions'
        });
      } else {
        Alert.alert('Export Complete', `File saved as ${filename}`);
      }

    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Failed', 'Failed to export sessions data');
    } finally {
      setIsExporting(false);
    }
  };

  const exportStudentData = async (format: ExportFormat) => {
    try {
      setIsExporting(true);

      // First, get all unique student IDs from attendance records
      const studentIds = new Set<string>();
      sessions.forEach(session => {
        if (session.attendance_records) {
          session.attendance_records.forEach(record => {
            studentIds.add(record.student_id);
          });
        }
      });

      // Fetch complete student data from Supabase
      const { supabase } = await import('@/lib/supabase');
      const { data: studentsData, error: studentsError } = await supabase
        .from('students')
        .select('*')
        .in('id', Array.from(studentIds));

      if (studentsError) {
        console.error('Error fetching students:', studentsError);
        Alert.alert('Export Failed', 'Failed to fetch student data');
        return;
      }

      // Create a map for quick student lookup
      const studentsMap = new Map();
      studentsData?.forEach(student => {
        studentsMap.set(student.id, student);
      });

      const studentData: any[] = [];

      sessions.forEach(session => {
        if (session.attendance_records) {
          session.attendance_records.forEach(record => {
            // Get complete student information from the database
            const student = studentsMap.get(record.student_id);

            studentData.push({
              session_id: session.id,
              class_name: session.class?.name || 'Unknown',
              subject: session.subject,
              session_date: session.session_date,
              session_time: session.session_time,
              session_type: session.session_type,
              student_id: record.student_id,
              student_name: student?.name || 'Unknown Student',
              student_email: student?.email || '',
              roll_number: student?.roll_number || '',
              student_grade: student?.class || '',
              student_section: student?.section || '',
              student_enrollment_code: student?.enrollment_code || '',
              admission_date: student?.admission_date || '',
              attendance_status: record.status,
              marked_at: record.marked_at,
              marked_by: record.marked_by,
              notes: record.notes || ''
            });
          });
        }
      });

      let content: string;
      let filename: string;
      let mimeType: string;

      if (format === 'csv') {
        const headers = [
          'session_id', 'class_name', 'subject', 'session_date', 'session_time', 'session_type',
          'student_id', 'student_name', 'student_email', 'roll_number', 'student_grade',
          'student_section', 'student_enrollment_code', 'admission_date',
          'attendance_status', 'marked_at', 'marked_by', 'notes'
        ];
        content = generateCSV(studentData, headers);
        filename = `student_attendance_${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else {
        content = JSON.stringify(studentData, null, 2);
        filename = `student_attendance_${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      }

      const fileUri = FileSystem.documentDirectory + filename;
      await FileSystem.writeAsStringAsync(fileUri, content);

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType,
          dialogTitle: 'Export Student Attendance'
        });
      } else {
        Alert.alert('Export Complete', `File saved as ${filename}`);
      }

    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Failed', 'Failed to export student data');
    } finally {
      setIsExporting(false);
    }
  };

  const exportSummary = async (format: ExportFormat) => {
    try {
      setIsExporting(true);

      const summaryData = {
        export_date: new Date().toISOString(),
        teacher_name: currentTeacher?.name || 'Unknown Teacher',
        period: 'Last 30 days',
        overall_stats: attendanceStats,
        class_summaries: availableClasses.map(classItem => {
          const classSessions = sessions.filter(session => session.class_id === classItem.id);

          let totalPresent = 0;
          let totalAbsent = 0;
          let totalRecords = 0;

          classSessions.forEach(session => {
            if (session.attendance_records) {
              session.attendance_records.forEach(record => {
                totalRecords++;
                if (record.status === 'present') totalPresent++;
                else totalAbsent++;
              });
            }
          });

          return {
            class_id: classItem.id,
            class_name: classItem.name,
            total_sessions: classSessions.length,
            total_students: classItem.student_count || 0,
            total_records: totalRecords,
            present_count: totalPresent,
            absent_count: totalAbsent,
            attendance_percentage: totalRecords > 0 ? ((totalPresent / totalRecords) * 100).toFixed(2) : '0.00'
          };
        })
      };

      let content: string;
      let filename: string;
      let mimeType: string;

      if (format === 'csv') {
        // For CSV, flatten the summary data
        const flatData = summaryData.class_summaries.map(cls => ({
          export_date: summaryData.export_date,
          teacher_name: summaryData.teacher_name,
          period: summaryData.period,
          ...cls
        }));

        const headers = [
          'export_date', 'teacher_name', 'period', 'class_id', 'class_name',
          'total_sessions', 'total_students', 'total_records', 'present_count',
          'absent_count', 'attendance_percentage'
        ];
        content = generateCSV(flatData, headers);
        filename = `attendance_summary_${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else {
        content = JSON.stringify(summaryData, null, 2);
        filename = `attendance_summary_${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      }

      const fileUri = FileSystem.documentDirectory + filename;
      await FileSystem.writeAsStringAsync(fileUri, content);

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType,
          dialogTitle: 'Export Attendance Summary'
        });
      } else {
        Alert.alert('Export Complete', `File saved as ${filename}`);
      }

    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Failed', 'Failed to export summary data');
    } finally {
      setIsExporting(false);
    }
  };

  const handleExport = (type: ExportType, format: ExportFormat) => {
    Alert.alert(
      'Export Data',
      `Export ${type} data as ${format.toUpperCase()}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            switch (type) {
              case 'sessions':
                exportSessions(format);
                break;
              case 'students':
                exportStudentData(format);
                break;
              case 'summary':
                exportSummary(format);
                break;
            }
          }
        }
      ]
    );
  };

  if (isLoading) {
    return <LoadingScreen message="Loading export options..." />;
  }

  if (error) {
    return (
      <ErrorScreen
        title="Export Error"
        message={error}
        onRetry={() => {
          setError(null);
          // Retry loading data
        }}
      />
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={() => router.back()}
            className="mr-4"
          >
            <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Export Reports
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Download attendance data
            </Text>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {/* Export Options */}
        <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Export Options
          </Text>

          {/* Sessions Export */}
          <View className={`p-4 rounded-lg mb-6 border ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
            <View className="flex-row items-center mb-4">
              <IconSymbol name="calendar" size={24} color={isDark ? '#60A5FA' : '#2563EB'} />
              <View className="ml-3 flex-1">
                <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Session Data
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Export all attendance sessions with statistics
                </Text>
              </View>
            </View>
            <View className="flex-row gap-3">
              <TouchableOpacity
                onPress={() => handleExport('sessions', 'csv')}
                disabled={isExporting}
                className={`flex-1 p-4 rounded-lg ${
                  isExporting ? 'bg-gray-400' : 'bg-primary-500'
                }`}
              >
                <Text className="text-white font-rubik-semibold text-center">
                  Export CSV
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleExport('sessions', 'json')}
                disabled={isExporting}
                className={`flex-1 p-4 rounded-lg border-2 border-primary-500 ${
                  isExporting ? 'bg-gray-400 border-gray-400' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-semibold text-center ${
                  isExporting ? 'text-gray-500' : 'text-primary-500'
                }`}>
                  Export JSON
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Student Data Export */}
          <View className={`p-4 rounded-lg mb-6 border ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
            <View className="flex-row items-center mb-4">
              <IconSymbol name="person.2.fill" size={24} color={isDark ? '#34D399' : '#059669'} />
              <View className="ml-3 flex-1">
                <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Student Attendance
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Export detailed student attendance records
                </Text>
              </View>
            </View>
            <View className="flex-row gap-3">
              <TouchableOpacity
                onPress={() => handleExport('students', 'csv')}
                disabled={isExporting}
                className={`flex-1 p-4 rounded-lg ${
                  isExporting ? 'bg-gray-400' : 'bg-success'
                }`}
              >
                <Text className="text-white font-rubik-semibold text-center">
                  Export CSV
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleExport('students', 'json')}
                disabled={isExporting}
                className={`flex-1 p-4 rounded-lg border-2 border-success ${
                  isExporting ? 'bg-gray-400 border-gray-400' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-semibold text-center ${
                  isExporting ? 'text-gray-500' : 'text-success'
                }`}>
                  Export JSON
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Summary Export */}
          <View className={`p-4 rounded-lg border ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
            <View className="flex-row items-center mb-4">
              <IconSymbol name="chart.bar.fill" size={24} color={isDark ? '#F59E0B' : '#D97706'} />
              <View className="ml-3 flex-1">
                <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Summary Report
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Export aggregated statistics and summaries
                </Text>
              </View>
            </View>
            <View className="flex-row gap-3">
              <TouchableOpacity
                onPress={() => handleExport('summary', 'csv')}
                disabled={isExporting}
                className={`flex-1 p-4 rounded-lg ${
                  isExporting ? 'bg-gray-400' : 'bg-warning'
                }`}
              >
                <Text className="text-white font-rubik-semibold text-center">
                  Export CSV
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleExport('summary', 'json')}
                disabled={isExporting}
                className={`flex-1 p-4 rounded-lg border-2 border-warning ${
                  isExporting ? 'bg-gray-400 border-gray-400' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-semibold text-center ${
                  isExporting ? 'text-gray-500' : 'text-warning'
                }`}>
                  Export JSON
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Export Info */}
        <View className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Export Information
          </Text>
          <View className="space-y-2">
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              • Data includes last 30 days of attendance records
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              • CSV format is compatible with Excel and Google Sheets
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              • JSON format preserves all data structure and relationships
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              • Files are saved to your device and can be shared
            </Text>
          </View>
        </View>

        {isExporting && (
          <View className={`mt-4 p-4 rounded-lg ${isDark ? 'bg-primary-900' : 'bg-primary-50'}`}>
            <Text className={`text-center font-rubik-semibold ${isDark ? 'text-primary-200' : 'text-primary-700'}`}>
              Exporting data...
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ExportReportsScreen;
