import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Ionicons } from '@expo/vector-icons';
import AssignmentDebugger from '@/components/testing/AssignmentDebugger';
import AuthTester from '@/components/testing/AuthTester';
import ComprehensiveAssignmentTester from '@/components/testing/ComprehensiveAssignmentTester';

export default function DebugScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [activeTab, setActiveTab] = useState<'auth' | 'debugger' | 'comprehensive' | 'info'>('auth');

  const renderInfo = () => (
    <ScrollView className="flex-1 p-6">
      <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-white'} mb-6`}>
        <Text className={`text-xl font-rubik-bold mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
          Assignment System Debug Tools
        </Text>
        
        <View className="space-y-4">
          <View>
            <Text className={`font-rubik-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Auth Tab
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              • Check authentication status{'\n'}
              • Verify user and teacher records{'\n'}
              • Create missing database records{'\n'}
              • Test Clerk-Supabase integration
            </Text>
          </View>

          <View>
            <Text className={`font-rubik-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Debugger Tab
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              • Quick assignment system tests{'\n'}
              • Create test data{'\n'}
              • Test basic functionality{'\n'}
              • Debug specific issues
            </Text>
          </View>

          <View>
            <Text className={`font-rubik-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Comprehensive Tab
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              • Full system testing suite{'\n'}
              • End-to-end test scenarios{'\n'}
              • Performance testing{'\n'}
              • Complete feature validation
            </Text>
          </View>
        </View>
      </View>

      <View className={`p-6 rounded-xl ${isDark ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
        <View className="flex-row items-start">
          <IconSymbol
            name="info.circle.fill"
            size={20}
            color="#3B82F6"
            style={{ marginTop: 2, marginRight: 12 }}
          />
          <View className="flex-1">
            <Text className={`font-rubik-bold ${isDark ? 'text-blue-300' : 'text-blue-800'}`}>
              Testing Guidelines
            </Text>
            <Text className={`font-rubik mt-2 ${isDark ? 'text-blue-200' : 'text-blue-700'}`}>
              1. Start with the Auth tab to ensure proper authentication{'\n'}
              2. Use the Debugger tab for quick tests and issue resolution{'\n'}
              3. Run the Comprehensive tab for full system validation{'\n'}
              4. Check console logs for detailed error information
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="px-6 py-4">
        <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
          Debug & Testing
        </Text>
        <Text className={`text-sm font-rubik mt-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
          Assignment system debugging and testing tools
        </Text>
      </View>

      {/* Tabs */}
      <View className="flex-row p-4 space-x-2">
        <TouchableOpacity
          onPress={() => setActiveTab('auth')}
          className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl ${
            activeTab === 'auth'
              ? 'bg-primary-500'
              : isDark
              ? 'bg-dark-card'
              : 'bg-light-card'
          }`}
        >
          <IconSymbol
            name="key.fill"
            size={16}
            color={activeTab === 'auth' ? 'white' : isDark ? '#FFFFFF' : '#000000'}
          />
          <Text
            className={`ml-2 font-rubik-medium text-sm ${
              activeTab === 'auth'
                ? 'text-white'
                : isDark
                ? 'text-dark-text'
                : 'text-light-text'
            }`}
          >
            Auth
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setActiveTab('debugger')}
          className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl ${
            activeTab === 'debugger'
              ? 'bg-primary-500'
              : isDark
              ? 'bg-dark-card'
              : 'bg-light-card'
          }`}
        >
          <IconSymbol
            name="wrench.and.screwdriver.fill"
            size={16}
            color={activeTab === 'debugger' ? 'white' : isDark ? '#FFFFFF' : '#000000'}
          />
          <Text
            className={`ml-2 font-rubik-medium text-sm ${
              activeTab === 'debugger'
                ? 'text-white'
                : isDark
                ? 'text-dark-text'
                : 'text-light-text'
            }`}
          >
            Debugger
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setActiveTab('comprehensive')}
          className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl ${
            activeTab === 'comprehensive'
              ? 'bg-primary-500'
              : isDark
              ? 'bg-dark-card'
              : 'bg-light-card'
          }`}
        >
          <IconSymbol
            name="checkmark.seal.fill"
            size={16}
            color={activeTab === 'comprehensive' ? 'white' : isDark ? '#FFFFFF' : '#000000'}
          />
          <Text
            className={`ml-2 font-rubik-medium text-sm ${
              activeTab === 'comprehensive'
                ? 'text-white'
                : isDark
                ? 'text-dark-text'
                : 'text-light-text'
            }`}
          >
            Full Test
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setActiveTab('info')}
          className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl ${
            activeTab === 'info'
              ? 'bg-primary-500'
              : isDark
              ? 'bg-dark-card'
              : 'bg-light-card'
          }`}
        >
          <IconSymbol
            name="info.circle.fill"
            size={16}
            color={activeTab === 'info' ? 'white' : isDark ? '#FFFFFF' : '#000000'}
          />
          <Text
            className={`ml-2 font-rubik-medium text-sm ${
              activeTab === 'info'
                ? 'text-white'
                : isDark
                ? 'text-dark-text'
                : 'text-light-text'
            }`}
          >
            Info
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View className="flex-1">
        {activeTab === 'auth' ? (
          <AuthTester />
        ) : activeTab === 'debugger' ? (
          <AssignmentDebugger />
        ) : activeTab === 'comprehensive' ? (
          <ComprehensiveAssignmentTester />
        ) : (
          renderInfo()
        )}
      </View>
    </View>
  );
}
