import { GoogleGenAI } from "@google/genai";

// Initialize the Gemini AI client
const ai = new GoogleGenAI({ apiKey: process.env.EXPO_PUBLIC_GEMINI_API_KEY! });

interface GeneratedAssignment {
  title: string;
  description: string;
  instructions: string;
  maxPoints: number;
  numberOfQuestions: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface GeneratedMaterial {
  title: string;
  description: string;
  content: string;
  tags: string[];
}

export async function generateAssignmentWithGemini(prompt: string, numberOfQuestions: number, difficulty: string): Promise<GeneratedAssignment> {
  try {
    console.log("Starting assignment generation with prompt:", prompt);
    
    if (!process.env.EXPO_PUBLIC_GEMINI_API_KEY) {
      throw new Error("Gemini API key not found in environment variables");
    }

    // Create a structured prompt for better results
    const structuredPrompt = `
      Generate a ${difficulty} difficulty school assignment with ${numberOfQuestions} questions based on the following prompt: "${prompt}"
      
      Please provide the response in the following format:
      - Title: A clear, concise title for the assignment
      - Description: A detailed description of what students need to do
      - Instructions: Step-by-step instructions for completing the assignment, broken down into exactly ${numberOfQuestions} questions
      - Maximum Points: A suggested point value between 10 and 100
      
      Make the assignment engaging, educational, and appropriate for school students.
      Ensure the difficulty level is ${difficulty}.
    `;

    console.log("Sending request to Gemini API...");
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: structuredPrompt,
    });

    console.log("Received response from Gemini API");
    const text = response.text;
    if (!text) throw new Error("No response text received from Gemini API");

    console.log("Raw response:", text);

    // Parse the response text to extract the components
    const titleMatch = text.match(/Title:\s*(.+?)(?=Description:|$)/s);
    const descriptionMatch = text.match(/Description:\s*(.+?)(?=Instructions:|$)/s);
    const instructionsMatch = text.match(/Instructions:\s*(.+?)(?=Maximum Points:|$)/s);
    const maxPointsMatch = text.match(/Maximum Points:\s*(\d+)/);

    if (!titleMatch) console.log("Failed to extract title");
    if (!descriptionMatch) console.log("Failed to extract description");
    if (!instructionsMatch) console.log("Failed to extract instructions");
    if (!maxPointsMatch) console.log("Failed to extract max points");

    const result = {
      title: (titleMatch?.[1] || "").trim(),
      description: (descriptionMatch?.[1] || "").trim(),
      instructions: (instructionsMatch?.[1] || "").trim(),
      maxPoints: parseInt(maxPointsMatch?.[1] || "100", 10),
      numberOfQuestions: numberOfQuestions,
      difficulty: difficulty as 'easy' | 'medium' | 'hard'
    };

    console.log("Successfully parsed response:", result);
    return result;
  } catch (error) {
    console.error("Error generating assignment with Gemini:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message);
      console.error("Error stack:", error.stack);
    }
    throw new Error(`Failed to generate assignment with AI: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Generate material content with Gemini
export async function generateMaterialWithGemini(
  prompt: string,
  materialType: string,
  subject: string,
  gradeLevel: string
): Promise<GeneratedMaterial | null> {
  try {
    console.log("Starting material generation with prompt:", prompt);
    
    if (!process.env.EXPO_PUBLIC_GEMINI_API_KEY) {
      throw new Error("Gemini API key not found in environment variables");
    }

    // Create a structured prompt for material generation
    const structuredPrompt = `
      Create a ${materialType} for ${subject} at ${gradeLevel} level.

      Request: ${prompt}

      Please provide a comprehensive response in the following format:
      - Title: Clear, descriptive title for the material
      - Description: Brief description of what this material covers
      - Content: Full content of the material (detailed text, instructions, examples, etc.)
      - Tags: Relevant tags for categorization (comma-separated)

      Guidelines:
      - Make the content age-appropriate for ${gradeLevel}
      - Include clear explanations and examples
      - Structure the content logically
      - For lesson plans: include objectives, activities, and assessment
      - For worksheets: include varied exercises and clear instructions
      - For quizzes: include questions with answer keys
      - For presentations: include slide-by-slide content
      - Use engaging and educational language
      - Ensure content is practical and usable
    `;

    console.log("Sending material generation request to Gemini API...");
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: structuredPrompt,
    });

    console.log("Received material generation response from Gemini API");
    const text = response.text;
    if (!text) throw new Error("No response text received from Gemini API");

    console.log("Raw material response:", text);

    // Parse the response text to extract the components
    const titleMatch = text.match(/Title:\s*(.+?)(?=Description:|$)/s);
    const descriptionMatch = text.match(/Description:\s*(.+?)(?=Content:|$)/s);
    const contentMatch = text.match(/Content:\s*(.+?)(?=Tags:|$)/s);
    const tagsMatch = text.match(/Tags:\s*(.+?)$/s);

    if (!titleMatch) console.log("Failed to extract title from material");
    if (!descriptionMatch) console.log("Failed to extract description from material");
    if (!contentMatch) console.log("Failed to extract content from material");

    const result: GeneratedMaterial = {
      title: (titleMatch?.[1] || "Generated Material").trim(),
      description: (descriptionMatch?.[1] || "").trim(),
      content: (contentMatch?.[1] || "").trim(),
      tags: tagsMatch?.[1] 
        ? tagsMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
        : [materialType, subject, gradeLevel].filter(Boolean)
    };

    console.log("Successfully parsed material response:", result);
    return result;
  } catch (error) {
    console.error("Error generating material with Gemini:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message);
      console.error("Error stack:", error.stack);
    }
    return null;
  }
}

// AI-Assisted Grading Interfaces
interface GradingFeedback {
  score: number;
  feedback: string;
  strengths: string[];
  improvements: string[];
  rubricScores?: { [criteriaName: string]: number };
}

interface RubricCriteria {
  criteria_name: string;
  description: string;
  max_points: number;
}

// Generate AI feedback for assignment submissions
export async function generateGradingFeedback(
  submissionContent: string,
  assignmentInstructions: string,
  maxPoints: number,
  rubrics?: RubricCriteria[]
): Promise<GradingFeedback> {
  try {
    console.log("Starting AI grading feedback generation");

    if (!process.env.EXPO_PUBLIC_GEMINI_API_KEY) {
      throw new Error("Gemini API key not found in environment variables");
    }

    // Create rubric context if available
    const rubricContext = rubrics && rubrics.length > 0
      ? `\n\nGrading Rubric:\n${rubrics.map(r => `- ${r.criteria_name} (${r.max_points} points): ${r.description}`).join('\n')}`
      : '';

    const structuredPrompt = `
      You are an experienced teacher providing feedback on a student's assignment submission.

      Assignment Instructions: "${assignmentInstructions}"
      Maximum Points: ${maxPoints}${rubricContext}

      Student Submission: "${submissionContent}"

      Please provide detailed feedback in the following format:
      - Score: A numerical score out of ${maxPoints} points
      - Feedback: Comprehensive feedback explaining the grade (2-3 paragraphs)
      - Strengths: List 2-3 specific strengths in the submission
      - Improvements: List 2-3 specific areas for improvement${rubrics && rubrics.length > 0 ? '\n- RubricScores: Individual scores for each rubric criteria' : ''}

      Guidelines:
      - Be constructive and encouraging
      - Provide specific examples from the submission
      - Focus on learning objectives
      - Give actionable improvement suggestions
      - Be fair and consistent in grading
      - Consider effort and understanding demonstrated
    `;

    console.log("Sending grading request to Gemini API...");
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: structuredPrompt,
    });

    console.log("Received grading response from Gemini API");
    const text = response.text;
    if (!text) throw new Error("No response text received from Gemini API");

    console.log("Raw grading response:", text);

    // Parse the response
    const scoreMatch = text.match(/Score:\s*(\d+(?:\.\d+)?)/);
    const feedbackMatch = text.match(/Feedback:\s*(.+?)(?=Strengths:|$)/s);
    const strengthsMatch = text.match(/Strengths:\s*(.+?)(?=Improvements:|$)/s);
    const improvementsMatch = text.match(/Improvements:\s*(.+?)(?=RubricScores:|$)/s);
    const rubricScoresMatch = text.match(/RubricScores:\s*(.+?)$/s);

    // Parse strengths and improvements into arrays
    const parseListItems = (text: string): string[] => {
      return text.split('\n')
        .map(line => line.replace(/^[-•*]\s*/, '').trim())
        .filter(line => line.length > 0);
    };

    // Parse rubric scores if available
    let rubricScores: { [criteriaName: string]: number } | undefined;
    if (rubricScoresMatch && rubrics) {
      rubricScores = {};
      const scoresText = rubricScoresMatch[1];
      rubrics.forEach(rubric => {
        const scoreMatch = scoresText.match(new RegExp(`${rubric.criteria_name}[:\\s]*([\\d\\.]+)`, 'i'));
        if (scoreMatch) {
          rubricScores![rubric.criteria_name] = parseFloat(scoreMatch[1]);
        }
      });
    }

    const result: GradingFeedback = {
      score: scoreMatch ? parseFloat(scoreMatch[1]) : Math.round(maxPoints * 0.75), // Default to 75% if parsing fails
      feedback: (feedbackMatch?.[1] || "Good work on this assignment. Continue to focus on the key concepts.").trim(),
      strengths: strengthsMatch ? parseListItems(strengthsMatch[1]) : ["Shows understanding of the topic"],
      improvements: improvementsMatch ? parseListItems(improvementsMatch[1]) : ["Could provide more detailed explanations"],
      rubricScores
    };

    console.log("Successfully parsed grading feedback:", result);
    return result;
  } catch (error) {
    console.error("Error generating grading feedback with Gemini:", error);
    throw new Error(`Failed to generate AI grading feedback: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Generate suggested improvements for assignment instructions
export async function generateInstructionImprovements(
  currentInstructions: string,
  assignmentTitle: string,
  subject: string,
  gradeLevel: string
): Promise<string> {
  try {
    console.log("Starting instruction improvement generation");

    if (!process.env.EXPO_PUBLIC_GEMINI_API_KEY) {
      throw new Error("Gemini API key not found in environment variables");
    }

    const structuredPrompt = `
      You are an experienced educator helping to improve assignment instructions.

      Assignment Title: "${assignmentTitle}"
      Subject: ${subject}
      Grade Level: ${gradeLevel}

      Current Instructions: "${currentInstructions}"

      Please provide improved instructions that are:
      - Clear and specific
      - Age-appropriate for ${gradeLevel}
      - Include step-by-step guidance
      - Specify expected outcomes
      - Include helpful examples or tips
      - Encourage critical thinking

      Provide only the improved instructions text, no additional formatting or labels.
    `;

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: structuredPrompt,
    });

    const text = response.text;
    if (!text) throw new Error("No response text received from Gemini API");

    return text.trim();
  } catch (error) {
    console.error("Error generating instruction improvements:", error);
    throw new Error(`Failed to generate instruction improvements: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Generate rubric criteria based on assignment details
export async function generateRubricCriteria(
  assignmentTitle: string,
  instructions: string,
  maxPoints: number,
  subject: string
): Promise<RubricCriteria[]> {
  try {
    console.log("Starting rubric generation");

    if (!process.env.EXPO_PUBLIC_GEMINI_API_KEY) {
      throw new Error("Gemini API key not found in environment variables");
    }

    const structuredPrompt = `
      Create a detailed grading rubric for this assignment:

      Title: "${assignmentTitle}"
      Subject: ${subject}
      Instructions: "${instructions}"
      Total Points: ${maxPoints}

      Please provide 3-5 rubric criteria in the following format:
      Criteria1: [Name] | [Description] | [Points]
      Criteria2: [Name] | [Description] | [Points]
      ...

      Guidelines:
      - Criteria names should be clear and specific
      - Descriptions should explain what constitutes good performance
      - Points should add up to ${maxPoints}
      - Focus on key learning objectives
      - Include both content and presentation criteria
    `;

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: structuredPrompt,
    });

    const text = response.text;
    if (!text) throw new Error("No response text received from Gemini API");

    // Parse the criteria
    const criteriaLines = text.split('\n').filter(line => line.includes('|'));
    const criteria: RubricCriteria[] = [];
    let orderIndex = 0;

    for (const line of criteriaLines) {
      const parts = line.split('|').map(part => part.trim());
      if (parts.length >= 3) {
        const name = parts[0].replace(/^Criteria\d+:\s*/, '');
        const description = parts[1];
        const points = parseInt(parts[2]) || Math.floor(maxPoints / criteriaLines.length);

        criteria.push({
          criteria_name: name,
          description: description,
          max_points: points
        });
        orderIndex++;
      }
    }

    // Ensure points add up correctly
    const totalPoints = criteria.reduce((sum, c) => sum + c.max_points, 0);
    if (totalPoints !== maxPoints && criteria.length > 0) {
      const difference = maxPoints - totalPoints;
      criteria[0].max_points += difference;
    }

    return criteria;
  } catch (error) {
    console.error("Error generating rubric criteria:", error);
    throw new Error(`Failed to generate rubric criteria: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}