import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';

// Stores
import { useAttendanceStore } from '@/stores/attendanceStore';

interface AttendanceRecord {
  id: string;
  student_id: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  marked_at: string;
  student?: {
    id: string;
    name: string;
    student_id?: string;
    roll_number?: number;
  };
}

const SessionReportScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { sessionId } = useLocalSearchParams<{ sessionId: string }>();

  const {
    currentSession,
    studentsWithAttendance,
    isLoading,
    error,
    loadSessionWithStudents,
    clearError
  } = useAttendanceStore();

  const [sortBy, setSortBy] = useState<'name' | 'status' | 'time'>('name');
  const [filterStatus, setFilterStatus] = useState<'all' | 'present' | 'absent' | 'late' | 'excused'>('all');

  // Load session data
  useEffect(() => {
    if (sessionId) {
      loadSessionWithStudents(sessionId);
    }
  }, [sessionId, loadSessionWithStudents]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'text-success';
      case 'absent': return 'text-error';
      case 'late': return 'text-warning';
      case 'excused': return 'text-info';
      default: return isDark ? 'text-dark-text' : 'text-light-text';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present': return 'checkmark.circle.fill';
      case 'absent': return 'xmark.circle.fill';
      case 'late': return 'clock.fill';
      case 'excused': return 'questionmark.circle.fill';
      default: return 'person.fill';
    }
  };

  const getAttendanceStats = () => {
    const records = studentsWithAttendance.map(s => s.attendance_record).filter(Boolean);
    const total = records.length;
    const present = records.filter(r => r?.status === 'present').length;
    const absent = records.filter(r => r?.status === 'absent').length;
    const late = records.filter(r => r?.status === 'late').length;
    const excused = records.filter(r => r?.status === 'excused').length;

    return {
      total,
      present,
      absent,
      late,
      excused,
      attendanceRate: total > 0 ? (present / total) * 100 : 0
    };
  };

  const getFilteredAndSortedStudents = () => {
    let filtered = studentsWithAttendance;

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(student => 
        student.attendance_record?.status === filterStatus
      );
    }

    // Sort students
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'status':
          const statusOrder = { present: 0, late: 1, excused: 2, absent: 3 };
          const aStatus = a.attendance_record?.status || 'absent';
          const bStatus = b.attendance_record?.status || 'absent';
          return statusOrder[aStatus as keyof typeof statusOrder] - statusOrder[bStatus as keyof typeof statusOrder];
        case 'time':
          const aTime = a.attendance_record?.marked_at || '';
          const bTime = b.attendance_record?.marked_at || '';
          return new Date(bTime).getTime() - new Date(aTime).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  };

  const handleExportSession = () => {
    Alert.alert(
      'Export Session Report',
      'Choose export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'PDF', onPress: () => exportSessionToPDF() },
        { text: 'Excel', onPress: () => exportSessionToExcel() }
      ]
    );
  };

  const exportSessionToPDF = () => {
    // TODO: Implement PDF export for session
    Alert.alert('Export', 'PDF export will be implemented in the next update.');
  };

  const exportSessionToExcel = () => {
    // TODO: Implement Excel export for session
    Alert.alert('Export', 'Excel export will be implemented in the next update.');
  };

  const handleEditSession = () => {
    router.push(`/attendance/take-attendance?sessionId=${sessionId}` as any);
  };

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen message="Loading session report..." />;
  }

  // Show error screen
  if (error) {
    return (
      <ErrorScreen
        title="Session Error"
        message={error}
        onRetry={() => {
          clearError();
          if (sessionId) {
            loadSessionWithStudents(sessionId);
          }
        }}
      />
    );
  }

  if (!currentSession) {
    return (
      <ErrorScreen
        title="Session Not Found"
        message="The attendance session could not be loaded."
        onRetry={() => router.back()}
      />
    );
  }

  const stats = getAttendanceStats();
  const filteredStudents = getFilteredAndSortedStudents();

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <View className="flex-row items-center justify-between mb-4">
            <TouchableOpacity onPress={() => router.back()}>
              <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
            <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Session Report
            </Text>
            <TouchableOpacity onPress={handleExportSession}>
              <IconSymbol name="square.and.arrow.up" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>

          {/* Session Info */}
          <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <View className="flex-row items-center justify-between mb-2">
              <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {currentSession.class?.name || 'Unknown Class'}
              </Text>
              <View className={`px-3 py-1 rounded-full ${
                currentSession.status === 'completed' ? 'bg-success' : 'bg-warning'
              }`}>
                <Text className="text-white font-rubik-semibold text-xs">
                  {currentSession.status?.toUpperCase() || 'ACTIVE'}
                </Text>
              </View>
            </View>
            
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              {currentSession.subject} • {currentSession.session_date} • {currentSession.session_time}
            </Text>
            
            {currentSession.status !== 'completed' && (
              <TouchableOpacity
                onPress={handleEditSession}
                className="mt-3 bg-primary-500 px-4 py-2 rounded-lg self-start"
              >
                <Text className="text-white font-rubik-semibold text-sm">Edit Session</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Filter and Sort Controls */}
          <View className="flex-row gap-2 mb-2">
            <ScrollView horizontal showsHorizontalScrollIndicator={false} className="flex-1">
              {(['all', 'present', 'absent', 'late', 'excused'] as const).map((status) => (
                <TouchableOpacity
                  key={status}
                  onPress={() => setFilterStatus(status)}
                  className={`px-3 py-2 rounded-lg mr-2 ${
                    filterStatus === status ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                  }`}
                >
                  <Text className={`font-rubik-medium text-sm capitalize ${
                    filterStatus === status ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                  }`}>
                    {status}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View className="flex-row gap-2">
            {(['name', 'status', 'time'] as const).map((sort) => (
              <TouchableOpacity
                key={sort}
                onPress={() => setSortBy(sort)}
                className={`px-3 py-2 rounded-lg ${
                  sortBy === sort ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-medium text-sm capitalize ${
                  sortBy === sort ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  {sort}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {/* Statistics */}
          <View className={`m-4 p-6 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Attendance Summary
            </Text>
            
            <View className="flex-row flex-wrap gap-4 mb-4">
              <View className="flex-1 min-w-[80px] items-center">
                <Text className="text-success font-rubik-bold text-2xl">{stats.present}</Text>
                <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Present
                </Text>
              </View>
              
              <View className="flex-1 min-w-[80px] items-center">
                <Text className="text-error font-rubik-bold text-2xl">{stats.absent}</Text>
                <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Absent
                </Text>
              </View>
              
              <View className="flex-1 min-w-[80px] items-center">
                <Text className="text-warning font-rubik-bold text-2xl">{stats.late}</Text>
                <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Late
                </Text>
              </View>
              
              <View className="flex-1 min-w-[80px] items-center">
                <Text className="text-info font-rubik-bold text-2xl">{stats.excused}</Text>
                <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Excused
                </Text>
              </View>
            </View>
            
            <View className="border-t pt-4" style={{ borderColor: isDark ? '#374151' : '#E5E7EB' }}>
              <View className="flex-row justify-between items-center">
                <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Attendance Rate
                </Text>
                <Text className={`font-rubik-bold text-xl ${
                  stats.attendanceRate >= 90 ? 'text-success' :
                  stats.attendanceRate >= 75 ? 'text-warning' : 'text-error'
                }`}>
                  {stats.attendanceRate.toFixed(1)}%
                </Text>
              </View>
            </View>
          </View>

          {/* Student List */}
          <View className="mx-4 mb-4">
            <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Student Details ({filteredStudents.length})
            </Text>
            
            {filteredStudents.length > 0 ? (
              <View className="space-y-3">
                {filteredStudents.map((student) => {
                  const record = student.attendance_record;
                  return (
                    <View
                      key={student.id}
                      className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
                    >
                      <View className="flex-row items-center justify-between mb-2">
                        <View className="flex-1">
                          <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                            {student.name}
                          </Text>
                          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                            {student.student_id && `ID: ${student.student_id}`}
                            {student.roll_number && ` • Roll: ${student.roll_number}`}
                          </Text>
                        </View>
                        
                        <View className="flex-row items-center">
                          <IconSymbol 
                            name={getStatusIcon(record?.status || 'absent')} 
                            size={20} 
                            color={
                              record?.status === 'present' ? '#10B981' :
                              record?.status === 'absent' ? '#EF4444' :
                              record?.status === 'late' ? '#F59E0B' :
                              record?.status === 'excused' ? '#3B82F6' : '#6B7280'
                            }
                          />
                          <Text className={`font-rubik-semibold ml-2 capitalize ${getStatusColor(record?.status || 'absent')}`}>
                            {record?.status || 'Not Marked'}
                          </Text>
                        </View>
                      </View>
                      
                      {record?.notes && (
                        <View className={`mt-2 p-2 rounded border ${isDark ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background'}`}>
                          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                            {record.notes}
                          </Text>
                        </View>
                      )}
                      
                      {record?.marked_at && (
                        <Text className={`font-rubik text-xs mt-2 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          Marked at: {new Date(record.marked_at).toLocaleString()}
                        </Text>
                      )}
                    </View>
                  );
                })}
              </View>
            ) : (
              <View className={`py-12 items-center ${isDark ? 'bg-dark-surface' : 'bg-light-surface'} rounded-lg`}>
                <IconSymbol name="person.3" size={48} color={isDark ? '#9CA3AF' : '#6B7280'} />
                <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  No students found
                </Text>
                <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Try adjusting your filters
                </Text>
              </View>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default SessionReportScreen;
