NextGen Teacher Mobile App Documentation
Overview
The NextGen Teacher Mobile App empowers educators to manage classroom activities within the NextGen school management SaaS platform. Built with React Native using Expo for cross-platform support (iOS and Android), Clerk for secure authentication, and Supabase (PostgreSQL) for backend and database operations, the app supports a multi-tenant architecture. Teachers can enroll students, mark attendance (including their own via facial recognition), manage assignments and mock tests, share curriculum materials (including Google Gemini-generated content and PDF notes), communicate with students/parents, and access schedules. The app integrates with the broader NextGen ecosystem, where admins handle offline fee management (manual updates for checks/bank transfers, no cash or online payments) and notices (PDF/text, viewable by teachers). Security measures protect sensitive data, ensuring compliance with GDPR and FERPA. The app operates within a school tenant, with access controlled by Clerk’s role-based access control (RBAC).
Current Date: May 23, 2025
Table of Contents

Architecture
Tech Stack
Authentication with Clerk
Database Design
Security Measures
AI Integration with Google Gemini
Teacher Features
Student Enrollment
Attendance Management
Assignment and Mock Test Management
Material Sharing
Implementation Steps
Deployment
Maintenance and Monitoring
Compliance
References

Architecture
The Teacher Mobile App uses a multi-tenant architecture with a single PostgreSQL database, isolating data per school via tenant_id. Key components include:

Frontend: React Native with Expo for cross-platform mobile apps.
Backend: Supabase for managed PostgreSQL, real-time subscriptions, storage, and Edge Functions.
Authentication: Clerk for secure user management, RBAC, and Organizations for tenancy.
AI: Google Gemini for generating educational materials and grading feedback.
API Layer: Supabase REST APIs and Edge Functions for complex logic.
Hosting: Supabase/Clerk cloud services, with apps distributed via App Store/Google Play.

Multi-Tenancy

Approach: Shared database, shared schema with tenant_id for data isolation.
Rationale: Cost-effective and scalable (Multi-Tenant Architecture).
Isolation: Enforced via Supabase row-level security (RLS) and Clerk Organizations.

Tech Stack



Component
Technology
Purpose



Frontend
React Native, Expo
Cross-platform mobile app development


Backend
Supabase (PostgreSQL)
Managed database, real-time, storage, APIs


Authentication
Clerk
Secure user management, RBAC, multi-tenancy


AI
Google Gemini
Generating materials, grading feedback


Additional Tools
Expo EAS, GitHub, Sentry
Build automation, version control, error tracking


Authentication with Clerk
Clerk provides secure authentication for teachers (Clerk Authentication).

Features:
Email/password login, with OAuth (Google, Microsoft) and multi-factor authentication (MFA) options.
Organizations for tenant-based access (one school per tenant).
RBAC for teacher role, restricting access to class-specific data.


Integration:// App.js
import { ClerkProvider } from '@clerk/clerk-expo';
import { Slot } from 'expo-router';

export default function App() {
  return (
    <ClerkProvider publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY}>
      <Slot />
    </ClerkProvider>
  );
}


Security: JWT-based sessions, bot protection, SOC 2/GDPR compliance.

Database Design
The database supports multi-tenancy and teacher-specific features. Key tables relevant to the Teacher App are listed below, aligned with the admin documentation for consistency.
Schema
-- Tenants (Schools/Colleges)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    registration_number VARCHAR(100) NOT NULL,
    timezone VARCHAR(50) NOT NULL,
    academic_calendar_type VARCHAR(50) NOT NULL,
    website VARCHAR(255),
    established_year INTEGER,
    subscription_status VARCHAR(50) DEFAULT 'trial',
    trial_start_date TIMESTAMP WITH TIME ZONE,
    stripe_subscription_id VARCHAR(255),
    admin_id VARCHAR(255) REFERENCES users(clerk_user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Users (Synced with Clerk)
CREATE TABLE users (
    id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(id),
    clerk_user_id VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'teacher', 'student', 'parent')),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Teachers
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    user_id UUID REFERENCES users(id),
    subject VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Students
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    user_id UUID REFERENCES users(id),
    grade VARCHAR(50),
    date_of_birth DATE,
    enrollment_code VARCHAR(50) UNIQUE, -- Unique code for student app activation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Classes
CREATE TABLE classes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    name VARCHAR(255) NOT NULL,
    teacher_id UUID REFERENCES teachers(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Attendance
CREATE TABLE attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    student_id UUID REFERENCES students(id),
    class_id UUID REFERENCES classes(id),
    date DATE NOT NULL,
    status VARCHAR(50) CHECK (status IN ('present', 'absent', 'late')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Teacher Attendance
CREATE TABLE teacher_attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    teacher_id UUID REFERENCES teachers(id),
    date DATE NOT NULL,
    status VARCHAR(50) CHECK (status IN ('present', 'absent')),
    facial_recognition_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Assignments
CREATE TABLE assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    class_id UUID REFERENCES classes(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date DATE,
    gemini_generated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Mock Tests
CREATE TABLE mock_tests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    class_id UUID REFERENCES classes(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    time_limit INTEGER, -- Minutes
    question_type VARCHAR(50), -- e.g., 'multiple_choice', 'short_answer'
    auto_graded BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Materials (Teacher Notes)
CREATE TABLE materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    uploaded_by UUID REFERENCES teachers(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_url TEXT, -- URL to Supabase storage
    gemini_generated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Messages
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    sender_id UUID REFERENCES users(id),
    recipient_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    gemini_assisted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Notices (Viewable by Teachers)
CREATE TABLE notices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    created_by UUID REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    content TEXT,
    file_url TEXT, -- URL to Supabase storage for PDFs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

Row-Level Security (RLS)
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation ON teachers
    USING (tenant_id = current_setting('app.tenant_id')::UUID);
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
CREATE POLICY teacher_access ON students
    USING (tenant_id = current_setting('app.tenant_id')::UUID AND EXISTS (
        SELECT 1 FROM classes WHERE teacher_id = (SELECT id FROM teachers WHERE user_id = current_setting('app.clerk_user_id')::VARCHAR)
    ));
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
CREATE POLICY teacher_access ON attendance
    USING (tenant_id = current_setting('app.tenant_id')::UUID AND class_id IN (
        SELECT id FROM classes WHERE teacher_id = (SELECT id FROM teachers WHERE user_id = current_setting('app.clerk_user_id')::VARCHAR)
    ));
ALTER TABLE teacher_attendance ENABLE ROW LEVEL SECURITY;
CREATE POLICY teacher_access ON teacher_attendance
    USING (tenant_id = current_setting('app.tenant_id')::UUID AND teacher_id = (SELECT id FROM teachers WHERE user_id = current_setting('app.clerk_user_id')::VARCHAR));
ALTER TABLE assignments ENABLE ROW LEVEL SECURITY;
CREATE POLICY teacher_access ON assignments
    USING (tenant_id = current_setting('app.tenant_id')::UUID AND class_id IN (
        SELECT id FROM classes WHERE teacher_id = (SELECT id FROM teachers WHERE user_id = current_setting('app.clerk_user_id')::VARCHAR)
    ));
ALTER TABLE mock_tests ENABLE ROW LEVEL SECURITY;
CREATE POLICY teacher_access ON mock_tests
    USING (tenant_id = current_setting('app.tenant_id')::UUID AND class_id IN (
        SELECT id FROM classes WHERE teacher_id = (SELECT id FROM teachers WHERE user_id = current_setting('app.clerk_user_id')::VARCHAR)
    ));
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
CREATE POLICY teacher_access ON materials
    USING (tenant_id = current_setting('app.tenant_id')::UUID AND uploaded_by = (SELECT id FROM teachers WHERE user_id = current_setting('app.clerk_user_id')::VARCHAR));
ALTER TABLE notices ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation ON notices
    USING (tenant_id = current_setting('app.tenant_id')::UUID);

Indexes
CREATE INDEX idx_teachers_tenant_id ON teachers(tenant_id);
CREATE INDEX idx_students_tenant_id ON students(tenant_id);
CREATE INDEX idx_attendance_class_id ON attendance(class_id);
CREATE INDEX idx_teacher_attendance_teacher_id ON teacher_attendance(teacher_id);
CREATE INDEX idx_assignments_class_id ON assignments(class_id);
CREATE INDEX idx_mock_tests_class_id ON mock_tests(class_id);
CREATE INDEX idx_materials_uploaded_by ON materials(uploaded_by);
CREATE INDEX idx_notices_tenant_id ON notices(tenant_id);

Security Measures
The Teacher App prioritizes data protection, given the sensitivity of student and classroom data.

Authentication: Clerk’s JWTs, MFA, and bot protection prevent unauthorized access.
Encryption:
Data at rest: Supabase AES-256 encryption.
Data in transit: HTTPS/TLS 1.3 via Supabase APIs.


RLS: Restricts teachers to their tenant and class data using tenant_id and clerk_user_id.
Input Validation: Sanitize inputs to prevent SQL injection and XSS (e.g., using libraries like sanitize-html).
Facial Recognition Security: Encrypt biometric data and store verification status, not raw images, in teacher_attendance.
API Security: Supabase Edge Functions use JWT validation and input sanitization.
Access Control: Clerk’s RBAC ensures teachers only access their classes/students.
Compliance: GDPR/FERPA via data export/deletion APIs and consent management.

AI Integration with Google Gemini
Google Gemini enhances material generation and grading (Google Gemini API).
In-App Functionality

Material Generation: Create quizzes, worksheets, or lesson plans, flagged as gemini_generated in materials.
Automated Grading: Provide feedback for assignments/mock tests, stored in gemini_feedback.
Communication Assistance: Suggest message responses, flagged in messages.gemini_assisted.

Implementation
// lib/gemini.js
import { GoogleGenerativeAI } from '@google/generative-ai';
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function generateMaterial(prompt) {
  const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
  const result = await model.generateContent(prompt);
  return result.response.text();
}

Teacher Features
The Teacher App supports all features from the document, tailored for classroom management.

Student Enrollment:
Enroll students in classes, generating unique access codes for the Student & Parent App.
Maintain student records (e.g., grade, personal details).


Student Attendance:
Mark attendance (Present, Absent, Late) for classes or subjects.


Teacher Attendance:
Record own attendance via facial recognition using the device camera.


Assignment Management:
Create assignments with due dates, accept submissions, grade, and provide feedback.


Mock Tests:
Create tests (multiple-choice, short-answer), set time limits, and auto-grade objective questions.


Grade Entry:
Enter grades for assignments/tests, contribute to report cards.


Group Management:
Create class/subject groups for tasks, resources, or communication.


Curriculum Management:
Share lesson plans and materials (Gemini-generated or PDF notes).


Communication:
Send messages/announcements to students/parents.


Timetable/Daily Routine:
View teaching schedules and duties.


Library Access:
Reserve resources, track borrowed items.


Notices:
View admin-posted notices (PDF/text).



Student Enrollment
User Workflow

Select class from a list.
Add student details (name, email, grade).
Generate unique enrollment code (stored in students.enrollment_code).
Share code with student/parent for app activation.
View/edit student records.

Technical Implementation
// app/teacher/enrollment.tsx
import { useState } from 'react';
import { View, TextInput, Button } from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { createClient } from '@supabase/supabase-js';
import { nanoid } from 'nanoid';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

export default function EnrollmentScreen() {
  const { getToken } = useAuth();
  const [student, setStudent] = useState({ name: '', email: '', grade: '' });

  const enrollStudent = async () => {
    const token = await getToken();
    const enrollmentCode = nanoid(10);
    const { error } = await supabase
      .from('students')
      .insert({
        tenant_id: 'CURRENT_TENANT_ID',
        name: student.name,
        email: student.email,
        grade: student.grade,
        enrollment_code: enrollmentCode,
        class_id: 'CLASS_ID',
      });
    if (!error) alert(`Enrollment code: ${enrollmentCode}`);
  };

  return (
    <View>
      <TextInput
        placeholder="Student Name"
        value={student.name}
        onChangeText={(text) => setStudent({ ...student, name: text })}
      />
      <TextInput
        placeholder="Email"
        value={student.email}
        onChangeText={(text) => setStudent({ ...student, email: text })}
      />
      <TextInput
        placeholder="Grade"
        value={student.grade}
        onChangeText={(text) => setStudent({ ...student, grade: text })}
      />
      <Button title="Enroll Student" onPress={enrollStudent} />
    </View>
  );
}

Attendance Management
User Workflow
Student Attendance

Select class and date.
View student list, tap to mark Present/Absent/Late.
Save, triggering real-time updates.

Teacher Attendance

Open attendance screen, use camera for facial recognition.
Verify identity, log as Present (stored in teacher_attendance).

Technical Implementation
// app/teacher/attendance.tsx
import { useState } from 'react';
import { View, Text, Button, FlatList } from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

export default function AttendanceScreen({ classId }) {
  const { getToken } = useAuth();
  const [students, setStudents] = useState([]);
  const [attendance, setAttendance] = useState({});

  const fetchStudents = async () => {
    const token = await getToken();
    const { data } = await supabase
      .from('students')
      .select('id, name')
      .eq('class_id', classId)
      .eq('tenant_id', 'CURRENT_TENANT_ID');
    setStudents(data);
  };

  const markAttendance = async (studentId, status) => {
    const token = await getToken();
    const { error } = await supabase
      .from('attendance')
      .upsert({
        tenant_id: 'CURRENT_TENANT_ID',
        student_id: studentId,
        class_id: classId,
        date: new Date().toISOString().split('T')[0],
        status,
      });
    if (!error) setAttendance({ ...attendance, [studentId]: status });
  };

  return (
    <View>
      <FlatList
        data={students}
        renderItem={({ item }) => (
          <View>
            <Text>{item.name}</Text>
            <Button title="Present" onPress={() => markAttendance(item.id, 'present')} />
            <Button title="Absent" onPress={() => markAttendance(item.id, 'absent')} />
            <Button title="Late" onPress={() => markAttendance(item.id, 'late')} />
          </View>
        )}
      />
      <Button title="Save" onPress={() => console.log('Attendance saved')} />
    </View>
  );
}

Facial Recognition (Placeholder)
// lib/facial-recognition.js
export async function verifyTeacherFace(imageData) {
  // Placeholder: Integrate with a facial recognition API (e.g., AWS Rekognition)
  // Store verification status in teacher_attendance
  const { error } = await supabase
    .from('teacher_attendance')
    .insert({
      tenant_id: 'CURRENT_TENANT_ID',
      teacher_id: 'TEACHER_ID',
      date: new Date().toISOString().split('T')[0],
      status: 'present',
      facial_recognition_verified: true,
    });
  return !error;
}

Assignment and Mock Test Management
User Workflow

Create assignment/test with title, description, due date, and question type.
Assign to class/group.
Accept submissions, grade, and provide feedback (Gemini-assisted).
Auto-grade objective questions for mock tests.

Technical Implementation
// app/teacher/assignment.tsx
import { useState } from 'react';
import { View, TextInput, Button } from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { createClient } from '@supabase/supabase-js';
import { generateMaterial } from '../lib/gemini';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

export default function AssignmentScreen({ classId }) {
  const { getToken } = useAuth();
  const [assignment, setAssignment] = useState({ title: '', description: '', dueDate: '' });

  const createAssignment = async () => {
    const token = await getToken();
    const geminiContent = await generateMaterial(`Generate a ${assignment.title} assignment`);
    const { error } = await supabase
      .from('assignments')
      .insert({
        tenant_id: 'CURRENT_TENANT_ID',
        class_id: classId,
        title: assignment.title,
        description: geminiContent || assignment.description,
        due_date: assignment.dueDate,
        gemini_generated: !!geminiContent,
      });
    if (!error) console.log('Assignment created');
  };

  return (
    <View>
      <TextInput
        placeholder="Assignment Title"
        value={assignment.title}
        onChangeText={(text) => setAssignment({ ...assignment, title: text })}
      />
      <TextInput
        placeholder="Description"
        value={assignment.description}
        onChangeText={(text) => setAssignment({ ...assignment, description: text })}
      />
      <TextInput
        placeholder="Due Date (YYYY-MM-DD)"
        value={assignment.dueDate}
        onChangeText={(text) => setAssignment({ ...assignment, dueDate: text })}
      />
      <Button title="Create Assignment" onPress={createAssignment} />
    </View>
  );
}

Material Sharing
User Workflow

Upload PDF notes or generate materials via Gemini.
Share with class/group, stored in Supabase storage.
View/edit shared materials.

Technical Implementation
// app/teacher/materials.tsx
import { useState } from 'react';
import { View, TextInput, Button } from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { createClient } from '@supabase/supabase-js';
import * as DocumentPicker from 'expo-document-picker';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

export default function MaterialScreen() {
  const { getToken, userId } = useAuth();
  const [material, setMaterial] = useState({ title: '', description: '', file: null });

  const uploadMaterial = async () => {
    const token = await getToken();
    let fileUrl = null;
    if (material.file) {
      const { data, error: uploadError } = await supabase.storage
        .from('materials')
        .upload(`public/${material.file.name}`, material.file);
      if (!uploadError) fileUrl = data.path;
    }
    const { error } = await supabase
      .from('materials')
      .insert({
        tenant_id: 'CURRENT_TENANT_ID',
        uploaded_by: userId,
        title: material.title,
        description: material.description,
        file_url: fileUrl,
      });
    if (!error) console.log('Material uploaded');
  };

  const pickDocument = async () => {
    const result = await DocumentPicker.getDocumentAsync({ type: 'application/pdf' });
    if (result.type === 'success') setMaterial({ ...material, file: result });
  };

  return (
    <View>
      <TextInput
        placeholder="Material Title"
        value={material.title}
        onChangeText={(text) => setMaterial({ ...material, title: text })}
      />
      <TextInput
        placeholder="Description"
        value={material.description}
        onChangeText={(text) => setMaterial({ ...material, description: text })}
      />
      <Button title="Pick PDF" onPress={pickDocument} />
      <Button title="Upload Material" onPress={uploadMaterial} />
    </View>
  );
}

Implementation Steps

Set Up Expo Project:
Initialize: npx create-expo-app NextGen-teacher.
Install dependencies: Clerk, Supabase, Expo Router, DocumentPicker.


Configure Clerk:
Set up Clerk for authentication (Clerk Dashboard).


Set Up Supabase:
Create project, define schema, enable RLS (Supabase Dashboard).


Develop Features:
Build screens for enrollment, attendance, assignments, mock tests, materials, and communication using Expo Router.
Implement real-time updates via Supabase subscriptions.


Integrate Gemini:
Set up Google Cloud project, enable Gemini API.
Add material generation and grading logic.


Add Facial Recognition:
Integrate a biometric API (e.g., AWS Rekognition) or use a library like react-native-vision-camera.


Test:
Validate RLS, Clerk RBAC, and Gemini outputs.
Test on iOS/Android via Expo Go.


Deploy:
Build with Expo EAS: eas build.
Submit to App Store/Google Play.



Deployment

Backend: Supabase cloud for database and storage.
Frontend: App Store/Google Play via Expo EAS.
CI/CD: GitHub Actions:name: CI/CD
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm install
      - run: npm test
      - run: eas build --platform all



Maintenance and Monitoring

Backups: Supabase automated backups.
Scaling: Supabase auto-scaling for traffic.
Monitoring: Supabase dashboard, Sentry for errors, Expo build logs.

Compliance

GDPR/FERPA: Data export/deletion via Clerk/Supabase APIs.
AI Ethics: Anonymize data for Gemini to avoid bias.
Biometric Data: Encrypt and limit facial recognition data storage.

References

React Native Documentation
Expo Documentation
Clerk Authentication
Supabase Official Website
Google Gemini API
Multi-Tenant Architecture
GDPR Overview
FERPA Overview
School Management Software Market Forecast
Best School Management Software 2024

