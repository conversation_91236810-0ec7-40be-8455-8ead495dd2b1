// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/supabase

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.4";

interface CreateUserRequest {
  name: string;
  email: string;
  role: string;
  tenantId: string;
}

serve(async (req) => {
  try {
    // Log request headers for debugging
    const authHeader = req.headers.get("Authorization");
    console.log("Request headers:", {
      authorization: authHeader ? "Present" : "Missing",
      contentType: req.headers.get("Content-Type"),
    });

    // Check if Authorization header is present
    if (!authHeader) {
      console.error("Missing Authorization header");
      return new Response(
        JSON.stringify({
          success: false,
          message: "Authentication error: Missing Authorization header",
        }),
        { headers: { "Content-Type": "application/json" }, status: 401 }
      );
    }

    // Check if SUPABASE_URL and SUPABASE_ANON_KEY are set
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error("Missing Supabase environment variables");
      return new Response(
        JSON.stringify({
          success: false,
          message: "Server configuration error: Missing Supabase credentials",
          debug: {
            hasUrl: !!supabaseUrl,
            hasKey: !!supabaseAnonKey,
          },
        }),
        { headers: { "Content-Type": "application/json" }, status: 500 }
      );
    }

    // Create a Supabase client with the Auth context of the logged-in user
    const supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: { Authorization: authHeader },
      },
    });

    // Verify the token works by making a simple auth call
    const { error: authTestError } = await supabaseClient.auth.getSession();
    if (authTestError) {
      console.error("Invalid or expired token:", authTestError);
      return new Response(
        JSON.stringify({
          success: false,
          message: "Authentication error: Invalid or expired token",
          details: authTestError.message,
        }),
        { headers: { "Content-Type": "application/json" }, status: 401 }
      );
    }

    // Parse request body
    let requestData: CreateUserRequest;
    try {
      requestData = await req.json();
    } catch (error: any) {
      console.error("Error parsing request body:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: "Invalid request body",
          details: error?.message || "Could not parse JSON",
        }),
        { headers: { "Content-Type": "application/json" }, status: 400 }
      );
    }

    const { name, email, role, tenantId } = requestData;

    // Validate required fields
    if (!name || !email || !role || !tenantId) {
      return new Response(
        JSON.stringify({ success: false, message: "Missing required fields" }),
        { headers: { "Content-Type": "application/json" }, status: 400 }
      );
    }

    // Validate role
    const validRoles = ["admin", "teacher", "student", "parent"];
    if (!validRoles.includes(role)) {
      return new Response(
        JSON.stringify({ success: false, message: "Invalid role" }),
        { headers: { "Content-Type": "application/json" }, status: 400 }
      );
    }

    // Get the current user
    const { data, error } = await supabaseClient.auth.getUser();
    if (error || !data.user) {
      console.error("Auth error:", error?.message || "No user found");
      return new Response(
        JSON.stringify({
          success: false,
          message: "Authentication error",
          details: error?.message || "No user found",
        }),
        { headers: { "Content-Type": "application/json" }, status: 401 }
      );
    }

    const user = data.user;

    // Check if the current user has permission to create users
    const { data: currentUserData, error: currentUserError } = await supabaseClient
      .from("users")
      .select("role, tenant_id")
      .eq("clerk_user_id", user.id)
      .single();

    if (currentUserError || !currentUserData) {
      console.error("Error fetching current user data:", currentUserError?.message || "No user data");
      return new Response(
        JSON.stringify({
          success: false,
          message: "Error fetching user data",
          details: currentUserError?.message || `No user record found for clerk_user_id: ${user.id}`,
        }),
        { headers: { "Content-Type": "application/json" }, status: currentUserError ? 500 : 404 }
      );
    }

    // Only admins can create users
    if (currentUserData.role !== "admin") {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Permission denied",
          details: `User role '${currentUserData.role}' cannot create users`,
        }),
        { headers: { "Content-Type": "application/json" }, status: 403 }
      );
    }

    // Ensure the tenant ID matches the current user's tenant ID
    if (currentUserData.tenant_id !== tenantId) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Invalid tenant ID",
          details: `Tenant ID mismatch: ${currentUserData.tenant_id} vs ${tenantId}`,
        }),
        { headers: { "Content-Type": "application/json" }, status: 400 }
      );
    }

    // Check if a user with this email already exists
    const { data: existingUser, error: existingUserError } = await supabaseClient
      .from("users")
      .select("id")
      .eq("email", email)
      .maybeSingle();

    if (existingUserError) {
      console.error("Error checking for existing user:", existingUserError);
      return new Response(
        JSON.stringify({
          success: false,
          message: "Error checking for existing user",
          details: existingUserError.message,
        }),
        { headers: { "Content-Type": "application/json" }, status: 500 }
      );
    }

    if (existingUser) {
      return new Response(
        JSON.stringify({ success: false, message: "User with this email already exists" }),
        { headers: { "Content-Type": "application/json" }, status: 409 }
      );
    }

    // Generate a random UUID for the user
    const userId = crypto.randomUUID();

    console.log("Attempting to create user in database:", {
      id: userId,
      name,
      email,
      role,
      tenant_id: tenantId,
      clerk_user_id: `temp_${userId}`,
    });

    // Try to create the user in Supabase
    const { data: newUser, error: createError } = await supabaseClient
      .from("users")
      .insert({
        id: userId,
        name,
        email,
        role,
        tenant_id: tenantId,
        clerk_user_id: `temp_${userId}`, // This would be replaced with the actual Clerk user ID
      })
      .select()
      .single();

    if (createError) {
      console.error("Error creating user in database:", createError);

      // Check if this is an RLS error (permission denied)
      if (createError.code === "42501" || createError.message.includes("permission denied")) {
        console.log("RLS error detected, trying to use SQL function to bypass RLS...");

        // Try using a SQL function with SECURITY DEFINER to bypass RLS
        const { data: sqlData, error: sqlError } = await supabaseClient.rpc("create_user_bypassing_rls", {
          p_id: userId,
          p_name: name,
          p_email: email,
          p_role: role,
          p_tenant_id: tenantId,
          p_clerk_user_id: `temp_${userId}`,
        });

        if (sqlError || !sqlData?.success) {
          console.error("Error using SQL function:", sqlError);
          return new Response(
            JSON.stringify({
              success: false,
              message: `Failed to create user with SQL function: ${sqlError?.message || "Unknown error"}`,
              details: {
                originalError: createError,
                sqlError: sqlError,
              },
            }),
            { headers: { "Content-Type": "application/json" }, status: 500 }
          );
        }

        return new Response(
          JSON.stringify({
            success: true,
            message: "User created successfully using SQL function",
            user: sqlData.user,
            note: "Created using SQL function to bypass RLS",
          }),
          { headers: { "Content-Type": "application/json" } }
        );
      }

      // For other errors, return the original error
      return new Response(
        JSON.stringify({
          success: false,
          message: `Failed to create user: ${createError.message}`,
          details: createError,
        }),
        { headers: { "Content-Type": "application/json" }, status: 500 }
      );
    }

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: "User created successfully",
        user: newUser,
      }),
      { headers: { "Content-Type": "application/json" } }
    );
  } catch (error: any) {
    // Handle any unexpected errors
    console.error("Unhandled server error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        message: `Server error: ${error?.message || 'Unknown error'}`,
        stack: error?.stack,
      }),
      { headers: { "Content-Type": "application/json" }, status: 500 }
    );
  }
});