import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, View } from 'react-native';
import { IconSymbol } from './IconSymbol';

interface EmptyStateProps {
  title: string;
  message: string;
  icon: string;
}

export function EmptyState({ title, message, icon }: EmptyStateProps) {
  const isDark = useColorScheme() === 'dark';

  return (
    <View className="flex-1 justify-center items-center p-4">
      <IconSymbol
        name={icon}
        size={64}
        color={isDark ? '#4B5563' : '#9CA3AF'}
      />
      <Text className={`text-lg font-rubik-bold mt-4 text-center ${
        isDark ? 'text-dark-text' : 'text-light-text'
      }`}>
        {title}
      </Text>
      <Text className={`text-center mt-2 ${
        isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
      }`}>
        {message}
      </Text>
    </View>
  );
} 