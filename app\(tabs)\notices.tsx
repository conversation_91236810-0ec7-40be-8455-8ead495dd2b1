import PDFViewer from "@/components/PDFViewer";
import NoticeDetails from "@/components/notices/NoticeDetails";
import NoticeHeader from "@/components/notices/NoticeHeader";
import NoticeList from "@/components/notices/NoticeList";
import NoticeLoading from "@/components/notices/NoticeLoading";
import NoticeSearch from "@/components/notices/NoticeSearch";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { Notice, useNoticeStore } from "@/stores/noticeStore";
import { Stack } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import { View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const NoticesScreen = () => {
  const colorScheme = useColorScheme() ?? "light";
  const isDark = colorScheme === "dark";
  const { supabaseUser, isLoaded } = useSupabaseAuth();

  // Zustand store
  const {
    notices,
    isLoading,
    error,
    refreshing,
    lastUpdated,
    fetchNotices,
    refreshData,
    clearError,
  } = useNoticeStore();

  // Local state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedNotice, setSelectedNotice] = useState<Notice | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showPDF, setShowPDF] = useState(false);

  // Load notices when component mounts or user changes
  const loadNotices = useCallback(async () => {
    if (!supabaseUser?.tenant_id) {
      console.log('❌ No tenant_id found for user:', supabaseUser);
      return;
    }

    console.log('👤 Loading notices for user:', {
      id: supabaseUser.id,
      name: supabaseUser.name,
      email: supabaseUser.email,
      role: supabaseUser.role,
      tenant_id: supabaseUser.tenant_id,
      clerk_user_id: supabaseUser.clerk_user_id
    });

    try {
      await fetchNotices(supabaseUser.tenant_id);
    } catch (error) {
      console.error('Failed to load notices:', error);
    }
  }, [supabaseUser?.tenant_id, fetchNotices]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (!supabaseUser?.tenant_id) return;

    try {
      await refreshData(supabaseUser.tenant_id);
    } catch (error) {
      console.error('Failed to refresh notices:', error);
    }
  }, [supabaseUser?.tenant_id, refreshData]);

  // Handle notice selection
  const handleNoticeSelect = useCallback((notice: Notice) => {
    setSelectedNotice(notice);
    setShowDetails(true);
  }, []);

  // Handle view file
  const handleViewFile = useCallback((fileUrl: string) => {
    if (!fileUrl) return;

    // Set the selected notice if not already set
    if (!selectedNotice) {
      const notice = notices.find((n) => n.file_url === fileUrl);
      if (notice) {
        setSelectedNotice(notice);
      }
    }

    // Show the PDF viewer
    setShowPDF(true);
  }, [selectedNotice, notices]);

  // Handle search
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchQuery("");
  }, []);

  // Handle close details
  const handleCloseDetails = useCallback(() => {
    setShowDetails(false);
    setSelectedNotice(null);
  }, []);

  // Handle close PDF
  const handleClosePDF = useCallback(() => {
    setShowPDF(false);
  }, []);

  // Load notices when user is loaded
  useEffect(() => {
    if (isLoaded && supabaseUser?.tenant_id) {
      loadNotices();
    }
  }, [isLoaded, supabaseUser?.tenant_id, loadNotices]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  // Filter notices based on search query
  const filteredNotices = notices.filter((notice) => {
    return (
      notice.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (notice.content &&
        notice.content.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  // Show loading indicator while checking authentication
  if (!isLoaded || (isLoading && !refreshing && notices.length === 0)) {
    return <NoticeLoading />;
  }

  // Render PDF viewer
  const renderPDFViewer = () => {
    if (!selectedNotice?.file_url) return null;

    return (
      <View className="absolute inset-0 z-20">
        <PDFViewer
          fileUrl={selectedNotice.file_url}
          onClose={handleClosePDF}
        />
      </View>
    );
  };

  // Render notice details modal
  const renderNoticeDetails = () => {
    if (!selectedNotice) return null;

    return (
      <NoticeDetails
        notice={selectedNotice}
        onClose={handleCloseDetails}
        onViewFile={handleViewFile}
      />
    );
  };

  return (
    <SafeAreaView
      className={`flex-1 ${
        isDark ? "bg-dark-background" : "bg-light-background"
      }`}
    >
      <Stack.Screen options={{ headerShown: false }} />
      <View className="flex-1 p-4">
        {/* Header */}
        <NoticeHeader
          noticeCount={filteredNotices.length}
          searchQuery={searchQuery}
        />

        {/* Search Bar */}
        <NoticeSearch
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onClearSearch={handleClearSearch}
        />

        {/* Notice List */}
        <NoticeList
          notices={notices}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          onNoticePress={handleNoticeSelect}
          searchQuery={searchQuery}
        />

        {/* Notice Details Modal */}
        {showDetails && renderNoticeDetails()}

        {/* PDF Viewer */}
        {showPDF && renderPDFViewer()}
      </View>
    </SafeAreaView>
  );
};

export default NoticesScreen;
