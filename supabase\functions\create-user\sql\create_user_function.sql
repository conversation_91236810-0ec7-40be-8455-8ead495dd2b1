-- This function allows creating a user while bypassing RLS policies
-- It should be executed by the Edge Function

CREATE OR REPLACE FUNCTION create_user_bypassing_rls(
  p_id UUID,
  p_name TEXT,
  p_email TEXT,
  p_role TEXT,
  p_tenant_id UUID,
  p_clerk_user_id TEXT
) RETURNS JSONB AS $$
DECLARE
  v_result JSONB;
BEGIN
  -- Insert the user
  INSERT INTO users (
    id,
    name,
    email,
    role,
    tenant_id,
    clerk_user_id,
    created_at
  ) VALUES (
    p_id,
    p_name,
    p_email,
    p_role,
    p_tenant_id,
    p_clerk_user_id,
    NOW()
  )
  RETURNING to_jsonb(users.*) INTO v_result;
  
  RETURN jsonb_build_object(
    'success', true,
    'user', v_result
  );
EXCEPTION
  WHEN unique_violation THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'A user with this email or ID already exists',
      'error_code', SQLSTATE
    );
  WHEN others THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Database error: ' || SQLERRM,
      'error_code', SQLSTATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
