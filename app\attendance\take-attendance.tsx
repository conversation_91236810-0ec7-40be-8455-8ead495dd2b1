import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Stores
import { useAttendanceStore } from '@/stores/attendanceStore';

// Components

type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused';

interface StudentAttendanceState {
  studentId: string;
  status: AttendanceStatus;
  notes: string;
  hasChanges: boolean;
}

const TakeAttendanceScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { sessionId } = useLocalSearchParams<{ sessionId: string }>();

  const {
    currentSession,
    studentsWithAttendance,
    isLoading,
    isSaving,
    error,
    loadSessionWithStudents,
    bulkMarkAttendance,
    completeSession,
    resumeSession,
    deleteSession,
    clearError
  } = useAttendanceStore();

  const [localAttendance, setLocalAttendance] = useState<Record<string, StudentAttendanceState>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotes, setShowNotes] = useState<string | null>(null);

  // Load session data
  useEffect(() => {
    if (sessionId) {
      loadSessionWithStudents(sessionId);
    }
  }, [sessionId, loadSessionWithStudents]);

  // Initialize local attendance state
  useEffect(() => {
    if (studentsWithAttendance.length > 0) {
      const initialState: Record<string, StudentAttendanceState> = {};
      studentsWithAttendance.forEach(student => {
        initialState[student.id] = {
          studentId: student.id,
          status: student.attendance_record?.status || 'present',
          notes: student.attendance_record?.notes || '',
          hasChanges: false,
        };
      });
      setLocalAttendance(initialState);
    }
  }, [studentsWithAttendance]);

  const updateStudentAttendance = (studentId: string, status: AttendanceStatus, notes?: string) => {
    setLocalAttendance(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status,
        notes: notes !== undefined ? notes : prev[studentId]?.notes || '',
        hasChanges: true,
      }
    }));
  };

  const updateStudentNotes = (studentId: string, notes: string) => {
    setLocalAttendance(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes,
        hasChanges: true,
      }
    }));
  };

  const handleBulkAction = async (status: AttendanceStatus) => {
    Alert.alert(
      'Bulk Action',
      `Mark all students as ${status}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            const updatedAttendance = { ...localAttendance };
            Object.keys(updatedAttendance).forEach(studentId => {
              updatedAttendance[studentId] = {
                ...updatedAttendance[studentId],
                status,
                hasChanges: true,
              };
            });
            setLocalAttendance(updatedAttendance);
          }
        }
      ]
    );
  };

  const saveAttendance = async () => {
    try {
      const changedRecords = Object.values(localAttendance)
        .filter(record => record.hasChanges)
        .map(record => ({
          studentId: record.studentId,
          status: record.status,
          notes: record.notes || undefined,
        }));

      if (changedRecords.length === 0) {
        Alert.alert('No Changes', 'No attendance changes to save.');
        return;
      }

      await bulkMarkAttendance(changedRecords);

      // Reset hasChanges flags
      setLocalAttendance(prev => {
        const updated = { ...prev };
        Object.keys(updated).forEach(key => {
          updated[key].hasChanges = false;
        });
        return updated;
      });

      Alert.alert('Success', 'Attendance saved successfully!');
    } catch (error) {
      console.error('Error saving attendance:', error);
      Alert.alert('Error', 'Failed to save attendance. Please try again.');
    }
  };

  const handleCompleteSession = async () => {
    if (!currentSession) return;

    Alert.alert(
      'Complete Session',
      'Are you sure you want to complete this session? Once completed, attendance cannot be modified unless you resume the session.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Save any pending changes first
              await saveAttendance();
              // Complete the session
              await completeSession(currentSession.id);
              Alert.alert('Session Completed', 'The attendance session has been completed successfully.');
            } catch (error) {
              console.error('Error completing session:', error);
              Alert.alert('Error', 'Failed to complete session. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleResumeSession = async () => {
    if (!currentSession) return;

    Alert.alert(
      'Resume Session',
      'This will reopen the session for editing. You can make changes and complete it again later.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Resume',
          onPress: async () => {
            try {
              await resumeSession(currentSession.id);
              Alert.alert('Session Resumed', 'You can now edit attendance for this session.');
            } catch (error) {
              console.error('Error resuming session:', error);
              Alert.alert('Error', 'Failed to resume session. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleDeleteSession = async () => {
    if (!currentSession) return;

    Alert.alert(
      'Delete Session',
      'Are you sure you want to delete this session? This action cannot be undone and will remove all attendance records for this session.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteSession(currentSession.id);
              Alert.alert('Session Deleted', 'The attendance session has been deleted successfully.', [
                { text: 'OK', onPress: () => router.back() }
              ]);
            } catch (error) {
              console.error('Error deleting session:', error);
              Alert.alert('Error', 'Failed to delete session. Please try again.');
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case 'present': return 'bg-success';
      case 'absent': return 'bg-error';
      case 'late': return 'bg-warning';
      case 'excused': return 'bg-info';
      default: return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case 'present': return 'checkmark.circle.fill';
      case 'absent': return 'xmark.circle.fill';
      case 'late': return 'clock.fill';
      case 'excused': return 'questionmark.circle.fill';
      default: return 'person.fill';
    }
  };

  const filteredStudents = studentsWithAttendance.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.student_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.roll_number?.toString().includes(searchQuery)
  );

  const getAttendanceCounts = () => {
    const counts = { present: 0, absent: 0, late: 0, excused: 0 };
    Object.values(localAttendance).forEach(record => {
      counts[record.status]++;
    });
    return counts;
  };

  const counts = getAttendanceCounts();
  const hasUnsavedChanges = Object.values(localAttendance).some(record => record.hasChanges);
  const isSessionCompleted = currentSession?.status === 'completed';
  const isInteractionDisabled = isSessionCompleted;

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen message="Loading attendance session..." />;
  }

  // Show error screen
  if (error) {
    return (
      <ErrorScreen
        title="Session Error"
        message={error}
        onRetry={() => {
          clearError();
          if (sessionId) {
            loadSessionWithStudents(sessionId);
          }
        }}
      />
    );
  }

  // Show error if no session
  if (!currentSession) {
    return (
      <ErrorScreen
        title="Session Not Found"
        message="The attendance session could not be loaded."
        onRetry={() => router.back()}
      />
    );
  }

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <View className="flex-row items-center justify-between mb-2">
            <TouchableOpacity onPress={() => router.back()}>
              <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
            <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Take Attendance
            </Text>
            <View className="w-6" />
          </View>

          <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {currentSession.class?.name || 'Unknown Class'}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            {currentSession.subject} • {currentSession.session_date} • {currentSession.session_time}
          </Text>
        </View>

        {/* Stats Bar */}
        <View className={`p-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text className="text-success font-rubik-bold text-lg">{counts.present}</Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>Present</Text>
            </View>
            <View className="items-center">
              <Text className="text-error font-rubik-bold text-lg">{counts.absent}</Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>Absent</Text>
            </View>
            <View className="items-center">
              <Text className="text-warning font-rubik-bold text-lg">{counts.late}</Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>Late</Text>
            </View>
            <View className="items-center">
              <Text className="text-info font-rubik-bold text-lg">{counts.excused}</Text>
              <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>Excused</Text>
            </View>
          </View>
        </View>

        {/* Completed Session Banner */}
        {isSessionCompleted && (
          <View className="mx-4 mb-4 p-4 rounded-lg bg-success/10 border border-success">
            <View className="flex-row items-center">
              <IconSymbol name="checkmark.circle.fill" size={24} color="#10B981" />
              <View className="ml-3 flex-1">
                <Text className="font-rubik-semibold text-success">Session Completed</Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  This session is locked. Use Resume Session to make changes.
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Search Bar */}
        <View className="p-4">
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search students..."
            placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
            editable={!isInteractionDisabled}
            className={`p-3 rounded-lg font-rubik border ${
              isInteractionDisabled ? 'opacity-60' : ''
            } ${
              isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
            }`}
          />
        </View>

        {/* Bulk Actions */}
        {!isSessionCompleted && (
          <View className="px-4 pb-4">
            <Text className={`font-rubik-medium mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Quick Actions:
            </Text>
            <View className="flex-row gap-3">
              <TouchableOpacity
                onPress={() => handleBulkAction('present')}
                className="flex-1 bg-success p-3 rounded-lg flex-row items-center justify-center"
              >
                <IconSymbol name="checkmark.circle.fill" size={16} color="#FFFFFF" />
                <Text className="text-white font-rubik-medium text-sm ml-2">All Present</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleBulkAction('absent')}
                className="flex-1 bg-error p-3 rounded-lg flex-row items-center justify-center"
              >
                <IconSymbol name="xmark.circle.fill" size={16} color="#FFFFFF" />
                <Text className="text-white font-rubik-medium text-sm ml-2">All Absent</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Student List */}
        <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
          {filteredStudents.map((student) => {
            const attendance = localAttendance[student.id];
            if (!attendance) return null;

            return (
              <View
                key={student.id}
                className={`p-4 rounded-lg mb-3 border ${
                  attendance.hasChanges ? 'border-primary-500' : isDark ? 'border-dark-border' : 'border-light-border'
                } ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
              >
                <View className="flex-row items-center justify-between mb-3">
                  <View className="flex-1">
                    <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {student.name}
                    </Text>
                    <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                      {student.student_id && `ID: ${student.student_id}`}
                      {student.roll_number && ` • Roll: ${student.roll_number}`}
                    </Text>
                  </View>
                  {attendance.hasChanges && (
                    <View className="w-2 h-2 bg-primary-500 rounded-full" />
                  )}
                </View>

                {/* Status Buttons */}
                <View className={`flex-row gap-2 mb-4 ${isInteractionDisabled ? 'opacity-60' : ''}`}>
                  {(['present', 'absent', 'late', 'excused'] as AttendanceStatus[]).map((status) => (
                    <TouchableOpacity
                      key={status}
                      onPress={() => !isInteractionDisabled && updateStudentAttendance(student.id, status)}
                      disabled={isInteractionDisabled}
                      className={`flex-1 p-3 rounded-lg flex-row items-center justify-center ${
                        attendance.status === status ? getStatusColor(status) : 'bg-gray-200'
                      }`}
                    >
                      <IconSymbol
                        name={getStatusIcon(status)}
                        size={14}
                        color={attendance.status === status ? '#FFFFFF' : '#6B7280'}
                      />
                      <Text className={`font-rubik-medium text-xs ml-2 capitalize ${
                        attendance.status === status ? 'text-white' : 'text-gray-600'
                      }`}>
                        {status}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Notes */}
                <TouchableOpacity
                  onPress={() => !isInteractionDisabled && setShowNotes(showNotes === student.id ? null : student.id)}
                  disabled={isInteractionDisabled}
                  className={`flex-row items-center ${isInteractionDisabled ? 'opacity-60' : ''}`}
                >
                  <IconSymbol name="pencil" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  <Text className={`font-rubik text-sm ml-2 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    {attendance.notes ? (isInteractionDisabled ? 'View notes' : 'Edit notes') : (isInteractionDisabled ? 'No notes' : 'Add notes')}
                  </Text>
                </TouchableOpacity>

                {showNotes === student.id && (
                  <TextInput
                    value={attendance.notes}
                    onChangeText={(text) => !isInteractionDisabled && updateStudentNotes(student.id, text)}
                    placeholder={isInteractionDisabled ? "Notes (read-only)" : "Add notes for this student..."}
                    placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                    editable={!isInteractionDisabled}
                    multiline
                    numberOfLines={2}
                    className={`mt-2 p-2 rounded border font-rubik text-sm ${
                      isInteractionDisabled ? 'opacity-60' : ''
                    } ${
                      isDark ? 'bg-dark-background text-dark-text border-dark-border' : 'bg-light-background text-light-text border-light-border'
                    }`}
                  />
                )}
              </View>
            );
          })}
        </ScrollView>

        {/* Bottom Actions */}
        <View className={`p-4 border-t ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          {isSessionCompleted ? (
            /* Completed Session Actions */
            <View className="space-y-4">
              <TouchableOpacity
                onPress={handleResumeSession}
                disabled={isSaving}
                className={`p-4 rounded-lg flex-row items-center justify-center ${
                  isSaving ? 'bg-gray-400' : 'bg-primary-500'
                }`}
              >
                <IconSymbol name="arrow.clockwise" size={20} color="#FFFFFF" />
                <Text className="text-white font-rubik-semibold ml-2">
                  {isSaving ? 'Resuming...' : 'Resume Session'}
                </Text>
              </TouchableOpacity>

              {/* Additional spacer for better visual separation */}
              <View className="h-3" />

              <TouchableOpacity
                onPress={handleDeleteSession}
                disabled={isSaving}
                className={`p-4 rounded-lg flex-row items-center justify-center border-2 border-error ${
                  isSaving ? 'bg-gray-400 border-gray-400' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <IconSymbol name="trash.fill" size={20} color={isSaving ? '#9CA3AF' : '#EF4444'} />
                <Text className={`font-rubik-semibold ml-2 ${
                  isSaving ? 'text-gray-500' : 'text-error'
                }`}>
                  {isSaving ? 'Processing...' : 'Delete Session'}
                </Text>
              </TouchableOpacity>

              <Text className={`text-center text-xs font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Resume to edit attendance or delete if created by mistake
              </Text>
            </View>
          ) : (
            /* Active Session Actions */
            <View className="flex-row gap-4">
              <TouchableOpacity
                onPress={saveAttendance}
                disabled={!hasUnsavedChanges || isSaving}
                className={`flex-1 p-4 rounded-lg flex-row items-center justify-center ${
                  hasUnsavedChanges && !isSaving ? 'bg-primary-500' : 'bg-gray-400'
                }`}
              >
                <IconSymbol name="checkmark" size={20} color="#FFFFFF" />
                <Text className="text-white font-rubik-semibold ml-2">
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Text>
              </TouchableOpacity>

              {/* Separator */}
              <View className={`w-px bg-gray-300 ${isDark ? 'bg-gray-600' : 'bg-gray-300'}`} />

              <TouchableOpacity
                onPress={handleCompleteSession}
                disabled={isSaving}
                className={`bg-success p-4 rounded-lg flex-row items-center justify-center min-w-[120px] ${
                  isSaving ? 'opacity-50' : ''
                }`}
              >
                <IconSymbol name="checkmark.seal.fill" size={20} color="#FFFFFF" />
                <Text className="text-white font-rubik-semibold ml-2">Complete</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default TakeAttendanceScreen;
