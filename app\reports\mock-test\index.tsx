import { useColorScheme } from '@/hooks/useColorScheme';
import { useCurrentClass } from '@/hooks/useCurrentClass';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useRouter } from 'expo-router';
import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function MockTestList() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { currentClassId, loading: classLoading } = useCurrentClass();
  
  const {
    mockTests,
    loading,
    error,
    fetchMockTests,
    deleteMockTest,
    publishMockTest,
    closeMockTest,
    setSelectedMockTest,
  } = useAssignmentStore();

  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (currentClassId) {
        fetchMockTests(currentClassId);
      }
    }, [currentClassId, fetchMockTests])
  );

  const onRefresh = useCallback(async () => {
    if (currentClassId) {
      setRefreshing(true);
      await fetchMockTests(currentClassId);
      setRefreshing(false);
    }
  }, [currentClassId, fetchMockTests]);

  const handleDelete = (mockTest: any) => {
    Alert.alert(
      'Delete Mock Test',
      `Are you sure you want to delete "${mockTest.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteMockTest(mockTest.id),
        },
      ]
    );
  };

  const handleStatusChange = (mockTest: any) => {
    if (mockTest.status === 'draft') {
      Alert.alert(
        'Publish Mock Test',
        `Publish "${mockTest.title}" to make it available to students?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Publish',
            onPress: () => publishMockTest(mockTest.id),
          },
        ]
      );
    } else if (mockTest.status === 'published') {
      Alert.alert(
        'Close Mock Test',
        `Close "${mockTest.title}" to stop accepting attempts?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Close',
            onPress: () => closeMockTest(mockTest.id),
          },
        ]
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-500';
      case 'published':
        return 'bg-green-500';
      case 'closed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return 'document-outline';
      case 'published':
        return 'checkmark-circle';
      case 'closed':
        return 'close-circle';
      default:
        return 'document-outline';
    }
  };

  const getQuestionTypeIcon = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'radio-button-on';
      case 'short_answer':
        return 'text';
      case 'essay':
        return 'document-text';
      case 'mixed':
        return 'layers';
      default:
        return 'help-circle';
    }
  };

  const renderMockTest = ({ item: mockTest }: { item: any }) => (
    <TouchableOpacity
      onPress={() => {
        setSelectedMockTest(mockTest);
        router.push(`/reports/mock-test/${mockTest.id}` as any);
      }}
      className={`
        p-4 mb-3 rounded-lg border
        ${isDark ? 'bg-dark-card border-dark-border' : 'bg-light-card border-light-border'}
      `}
    >
      <View className="flex-row justify-between items-start mb-2">
        <View className="flex-1 mr-3">
          <Text className={`text-lg font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {mockTest.title}
          </Text>
          {mockTest.description && (
            <Text className={`text-sm mt-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} numberOfLines={2}>
              {mockTest.description}
            </Text>
          )}
        </View>
        
        <View className="flex-row items-center space-x-2">
          <View className={`px-2 py-1 rounded-full flex-row items-center ${getStatusColor(mockTest.status)}`}>
            <Ionicons name={getStatusIcon(mockTest.status) as any} size={12} color="white" />
            <Text className="text-white text-xs font-rubik-medium ml-1 capitalize">
              {mockTest.status}
            </Text>
          </View>
          
          <TouchableOpacity
            onPress={() => {
              Alert.alert(
                'Mock Test Actions',
                'Choose an action',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Edit',
                    onPress: () => {
                      setSelectedMockTest(mockTest);
                      Alert.alert('Edit Mock Test', `Edit functionality for "${mockTest.title}" will be implemented soon.`);
                    }
                  },
                  {
                    text: 'Manage Questions',
                    onPress: () => {
                      setSelectedMockTest(mockTest);
                      Alert.alert('Questions', `Questions for "${mockTest.title}": ${mockTest.questions_count || 0} questions available.`);
                    }
                  },
                  {
                    text: 'View Attempts',
                    onPress: () => {
                      setSelectedMockTest(mockTest);
                      Alert.alert('Attempts', `Attempts for "${mockTest.title}": ${mockTest.attempts_count || 0} attempts recorded.`);
                    }
                  },
                  {
                    text: mockTest.status === 'draft' ? 'Publish' : mockTest.status === 'published' ? 'Close' : 'Reopen',
                    onPress: () => handleStatusChange(mockTest)
                  },
                  { text: 'Delete', style: 'destructive', onPress: () => handleDelete(mockTest) },
                ]
              );
            }}
            className="p-1"
          >
            <Ionicons 
              name="ellipsis-vertical" 
              size={20} 
              color={isDark ? '#9CA3AF' : '#6B7280'} 
            />
          </TouchableOpacity>
        </View>
      </View>

      <View className="flex-row justify-between items-center mt-3">
        <View className="flex-row items-center space-x-4">
          <View className="flex-row items-center">
            <Ionicons name="time-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
            <Text className={`text-sm ml-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {mockTest.time_limit} min
            </Text>
          </View>
          
          <View className="flex-row items-center">
            <Ionicons name={getQuestionTypeIcon(mockTest.question_type) as any} size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
            <Text className={`text-sm ml-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {mockTest.question_type.replace('_', ' ')}
            </Text>
          </View>

          <View className="flex-row items-center">
            <Ionicons name="trophy-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
            <Text className={`text-sm ml-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {mockTest.max_points} pts
            </Text>
          </View>
        </View>

        <View className="flex-row items-center space-x-3">
          <View className="flex-row items-center">
            <Ionicons name="help-circle-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
            <Text className={`text-sm ml-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {mockTest.questions_count || 0}
            </Text>
          </View>
          
          <View className="flex-row items-center">
            <Ionicons name="people-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
            <Text className={`text-sm ml-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {mockTest.attempts_count || 0}
            </Text>
          </View>
        </View>
      </View>

      <View className="flex-row justify-between items-center mt-2">
        <View className="flex-row items-center space-x-4">
          {mockTest.auto_graded && (
            <View className="flex-row items-center">
              <Ionicons name="checkmark-done" size={14} color="#10B981" />
              <Text className="text-xs text-green-500 ml-1 font-rubik-medium">
                Auto-graded
              </Text>
            </View>
          )}
          
          {mockTest.show_results_immediately && (
            <View className="flex-row items-center">
              <Ionicons name="eye" size={14} color="#3B82F6" />
              <Text className="text-xs text-blue-500 ml-1 font-rubik-medium">
                Instant results
              </Text>
            </View>
          )}
        </View>

        {mockTest.gemini_generated && (
          <View className="flex-row items-center">
            <Ionicons name="sparkles" size={14} color="#8B5CF6" />
            <Text className="text-xs text-purple-500 ml-1 font-rubik-medium">
              AI Generated
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (classLoading || loading) {
    return (
      <View className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text className={`mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Loading mock tests...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className={`flex-1 justify-center items-center px-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <Ionicons name="alert-circle" size={48} color="#EF4444" />
        <Text className={`text-center mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {error}
        </Text>
        <TouchableOpacity
          onPress={onRefresh}
          className="mt-4 px-4 py-2 bg-primary rounded-lg"
        >
          <Text className="text-white font-rubik-medium">Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <View className="flex-row justify-between items-center p-4">
        <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Mock Tests
        </Text>
        <TouchableOpacity
          onPress={() => router.push('/reports/mock-test/create')}
          className="bg-primary px-4 py-2 rounded-lg flex-row items-center"
        >
          <Ionicons name="add" size={20} color="white" />
          <Text className="text-white font-rubik-medium ml-1">Create</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={mockTests}
        renderItem={renderMockTest}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: 16, paddingTop: 0 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#2196F3']}
            tintColor="#2196F3"
          />
        }
        ListEmptyComponent={
          <View className="flex-1 justify-center items-center py-20">
            <Ionicons name="clipboard-outline" size={64} color={isDark ? '#4B5563' : '#9CA3AF'} />
            <Text className={`text-lg font-rubik-medium mt-4 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              No mock tests yet
            </Text>
            <Text className={`text-center mt-2 px-8 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Create your first mock test to get started
            </Text>
            <TouchableOpacity
              onPress={() => router.push('/reports/mock-test/create')}
              className="mt-4 bg-primary px-6 py-3 rounded-lg"
            >
              <Text className="text-white font-rubik-medium">Create Mock Test</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </View>
  );
}
