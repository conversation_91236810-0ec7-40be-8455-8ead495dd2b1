import { MaterialFilters } from '@/components/materials/MaterialFilters';
import { MaterialList } from '@/components/materials/MaterialList';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { SearchBar } from '@/components/ui/SearchBar';
import { useToast } from '@/components/ui/Toast';
import { useAuth } from '@/hooks/useAuth';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { useEnrollmentStore } from '@/stores/enrollmentStore';
import { useMaterialStore } from '@/stores/materialStore';
import { useRouter } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { RefreshControl, StyleSheet, View } from 'react-native';

export default function SharedMaterialsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { session } = useSupabaseAuth();
  const { currentTeacher } = useEnrollmentStore();
  const { 
    sharedMaterials, 
    loadSharedMaterials, 
    isLoading, 
    error,
    filters,
    setFilters,
  } = useMaterialStore();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const toast = useToast();

  // Load shared materials when the screen mounts
  useEffect(() => {
    if (currentTeacher?.id) {
      loadSharedMaterials(currentTeacher.id).catch((error) => {
        console.error('Error loading shared materials:', error);
        toast.show({
          title: 'Error',
          message: 'Failed to load shared materials',
          type: 'error',
        });
      });
    }
  }, [currentTeacher?.id, loadSharedMaterials, toast]);

  // Handle refresh
  const onRefresh = useCallback(async () => {
    if (!currentTeacher?.id) return;
    setRefreshing(true);
    try {
      await loadSharedMaterials(currentTeacher.id);
    } catch (error) {
      console.error('Error refreshing shared materials:', error);
      toast.show({
        title: 'Error',
        message: 'Failed to refresh shared materials',
        type: 'error',
      });
    } finally {
      setRefreshing(false);
    }
  }, [currentTeacher?.id, loadSharedMaterials, toast]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setFilters({ ...filters, search_query: query });
  }, [filters, setFilters]);

  // Filter materials based on search query and filters
  const filteredMaterials = sharedMaterials.filter((material) => {
    // Apply search filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = 
        material.title.toLowerCase().includes(searchLower) ||
        material.description?.toLowerCase().includes(searchLower) ||
        material.subject?.toLowerCase().includes(searchLower) ||
        material.uploaded_by_name?.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Apply material type filter
    if (filters.material_type && material.material_type !== filters.material_type) {
      return false;
    }

    // Apply subject filter
    if (filters.subject && material.subject !== filters.subject) {
      return false;
    }

    // Apply grade level filter
    if (filters.grade_level && material.grade_level !== filters.grade_level) {
      return false;
    }

    return true;
  });

  // Handle material selection
  const handleSelectMaterial = useCallback((materialId: string) => {
    router.push(`/materials/view/${materialId}`);
  }, [router]);

  if (isLoading && !refreshing) {
    return <LoadingScreen message="Loading shared materials..." />;
  }

  if (error) {
    return <ErrorScreen message={error} onRetry={onRefresh} />;
  }

  return (
    <View style={styles.container}>
      <SearchBar
        placeholder="Search shared materials..."
        value={searchQuery}
        onChangeText={handleSearch}
      />
      <MaterialFilters />
      {filteredMaterials.length > 0 ? (
        <MaterialList
          materials={filteredMaterials}
          onSelectMaterial={handleSelectMaterial}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      ) : (
        <EmptyState
          title="No shared materials found"
          message={searchQuery || filters.material_type ? "Try adjusting your filters" : "No materials have been shared with you yet"}
          icon="share"
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});