import { useColorScheme } from '@/hooks/useColorScheme';
import { useCurrentClass } from '@/hooks/useCurrentClass';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function CreateMockTest() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { createMockTest, generateMockTestWithAI, loading, error } = useAssignmentStore();

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [instructions, setInstructions] = useState('');
  const [timeLimit, setTimeLimit] = useState('60');
  const [maxPoints, setMaxPoints] = useState('100');
  const [autoGraded, setAutoGraded] = useState(true);
  const [questionType, setQuestionType] = useState<'multiple_choice' | 'short_answer' | 'mixed'>('multiple_choice');
  const [status, setStatus] = useState<'draft' | 'published'>('draft');
  const [attemptsAllowed, setAttemptsAllowed] = useState('1');
  const [useAI, setUseAI] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const { currentClassId, loading: classLoading } = useCurrentClass();

  // Form validation
  const [errors, setErrors] = useState({
    title: '',
    description: '',
    timeLimit: '',
    maxPoints: '',
    attemptsAllowed: '',
  });

  const validateForm = () => {
    const newErrors = {
      title: '',
      description: '',
      timeLimit: '',
      maxPoints: '',
      attemptsAllowed: '',
    };

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!description.trim()) {
      newErrors.description = 'Description is required';
    }

    const time = parseInt(timeLimit);
    if (!timeLimit || isNaN(time) || time <= 0) {
      newErrors.timeLimit = 'Valid time limit required';
    }

    const points = parseInt(maxPoints);
    if (!maxPoints || isNaN(points) || points <= 0) {
      newErrors.maxPoints = 'Valid points required';
    }

    const attempts = parseInt(attemptsAllowed);
    if (!attemptsAllowed || isNaN(attempts) || attempts <= 0) {
      newErrors.attemptsAllowed = 'Valid attempts count required';
    }

    setErrors(newErrors);
    return Object.values(newErrors).every(error => !error);
  };

  const handleCreate = async () => {
    if (!validateForm() || !currentClassId) return;

    try {
      let result;

      if (useAI && aiPrompt.trim()) {
        result = await generateMockTestWithAI(aiPrompt, currentClassId);
      } else {
        result = await createMockTest({
          title: title.trim(),
          description: description.trim(),
          instructions: instructions.trim(),
          time_limit: parseInt(timeLimit),
          max_points: parseInt(maxPoints),
          question_type: questionType,
          auto_graded: autoGraded,
          status,
          attempts_allowed: parseInt(attemptsAllowed),
          class_id: currentClassId,
          gemini_generated: false,
        });
      }

      if (result) {
        Alert.alert(
          'Success',
          `Mock test ${status === 'published' ? 'created and published' : 'saved as draft'} successfully!`,
          [{ text: 'OK', onPress: () => router.back() }]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create mock test. Please try again.');
    }
  };

  const handleGenerateWithAI = async () => {
    if (!aiPrompt.trim() || !currentClassId) {
      Alert.alert('Error', 'Please enter a prompt for AI generation');
      return;
    }

    try {
      const result = await generateMockTestWithAI(aiPrompt, currentClassId);
      if (result) {
        setTitle(result.title);
        setDescription(result.description || '');
        setUseAI(false);
        setAiPrompt('');
        Alert.alert('Success', 'Mock test generated with AI! You can now review and modify it.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to generate mock test with AI. Please try again.');
    }
  };

  return (
    <SafeAreaView className="flex-1" edges={['top']}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
    <ScrollView 
          className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}
          contentContainerStyle={{ padding: 24, paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
        >
         
         

          <View className="space-y-8">
            {/* Title */}
            <Animated.View 
              entering={FadeInDown.delay(100).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Test Title
              </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <TextInput
            value={title}
                  onChangeText={setTitle}
                  placeholder="Enter test title"
            placeholderTextColor={isDark ? "#666" : "#999"}
                  className={`px-4 py-3.5 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'} ${errors.title ? 'border-2 border-red-500' : ''}`}
                />
              </View>
          {errors.title ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.title}</Text>
          ) : null}
            </Animated.View>

            {/* Description */}
            <Animated.View 
              entering={FadeInDown.delay(200).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Description
          </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <TextInput
            value={description}
                  onChangeText={setDescription}
                  placeholder="Enter test description"
            placeholderTextColor={isDark ? "#666" : "#999"}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
                  className={`px-4 py-3.5 min-h-[120px] font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'} ${errors.description ? 'border-2 border-red-500' : ''}`}
          />
              </View>
          {errors.description ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.description}</Text>
              ) : null}
            </Animated.View>

            {/* Instructions */}
            <Animated.View 
              entering={FadeInDown.delay(300).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Instructions (Optional)
            </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
                <TextInput
                  value={instructions}
                  onChangeText={setInstructions}
                  placeholder="Enter test instructions"
                  placeholderTextColor={isDark ? "#666" : "#999"}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  className={`px-4 py-3.5 min-h-[100px] font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}
                />
        </View>
            </Animated.View>

            {/* Time Limit and Max Points */}
            <Animated.View 
              entering={FadeInDown.delay(400).duration(400)}
              className="flex-row space-x-4"
            >
              <View className="flex-1 space-y-3">
                <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Time Limit (minutes)
          </Text>
                <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <TextInput
            value={timeLimit}
                    onChangeText={setTimeLimit}
                    placeholder="60"
            placeholderTextColor={isDark ? "#666" : "#999"}
            keyboardType="numeric"
                    className={`px-4 py-3.5 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'} ${errors.timeLimit ? 'border-2 border-red-500' : ''}`}
                  />
                </View>
          {errors.timeLimit ? (
                  <Text className="text-red-500 text-sm font-rubik-medium">{errors.timeLimit}</Text>
          ) : null}
        </View>

              <View className="flex-1 space-y-3">
                <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Maximum Points
          </Text>
                <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
                  <TextInput
                    value={maxPoints}
                    onChangeText={setMaxPoints}
                    placeholder="100"
                    placeholderTextColor={isDark ? "#666" : "#999"}
                    keyboardType="numeric"
                    className={`px-4 py-3.5 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'} ${errors.maxPoints ? 'border-2 border-red-500' : ''}`}
                  />
                </View>
                {errors.maxPoints ? (
                  <Text className="text-red-500 text-sm font-rubik-medium">{errors.maxPoints}</Text>
                ) : null}
              </View>
            </Animated.View>

            {/* Question Type */}
            <Animated.View 
              entering={FadeInDown.delay(500).duration(400)}
              className="space-y-6"
            >
              <Text className={`text-base font-rubik-medium mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Question Type
              </Text>
              <View className="flex-row flex-wrap gap-4">
                {(['multiple_choice', 'short_answer', 'mixed'] as const).map((type) => (
                  <TouchableOpacity
                    key={type}
                    onPress={() => setQuestionType(type)}
                    className={`px-6 py-3 rounded-xl ${
                      questionType === type 
                        ? 'bg-primary-500' 
                        : isDark 
                          ? 'bg-dark-card' 
                          : 'bg-light-card'
                    }`}
                  >
                    <Text className={`font-rubik-medium ${
                      questionType === type 
                        ? 'text-white' 
                        : isDark 
                          ? 'text-dark-text' 
                          : 'text-light-text'
                    }`}>
                      {type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
              </Text>
            </TouchableOpacity>
                ))}
              </View>
            </Animated.View>

            {/* Test Settings */}
            <Animated.View 
              entering={FadeInDown.delay(600).duration(400)}
              className="space-y-6 mt-8"
            >
              <Text className={`text-lg font-rubik-semibold ${isDark ? "text-dark-text" : "text-light-text"}`}>
                Test Settings
              </Text>

              <View className="space-y-6">
        <View className="flex-row items-center justify-between">
                  <Text className={`text-base font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                    Auto-graded
          </Text>
          <Switch
            value={autoGraded}
            onValueChange={setAutoGraded}
                    trackColor={{ false: "#767577", true: "#007AFF40" }}
                    thumbColor={autoGraded ? "#007AFF" : "#f4f3f4"}
                  />
                </View>
              </View>
            </Animated.View>

            {/* Attempts Allowed */}
            <Animated.View 
              entering={FadeInDown.delay(700).duration(400)}
              className="space-y-4"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Attempts Allowed
              </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
                <TextInput
                  value={attemptsAllowed}
                  onChangeText={setAttemptsAllowed}
                  placeholder="1"
                  placeholderTextColor={isDark ? "#666" : "#999"}
                  keyboardType="numeric"
                  className={`px-4 py-3.5 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'} ${errors.attemptsAllowed ? 'border-2 border-red-500' : ''}`}
                />
              </View>
              {errors.attemptsAllowed ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.attemptsAllowed}</Text>
              ) : null}
            </Animated.View>

            {/* AI Generation */}
            <Animated.View 
              entering={FadeInDown.delay(800).duration(400)}
              className="space-y-4"
            >
              <View className="flex-row items-center justify-between py-2">
                <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Use AI Generation
                </Text>
                <Switch
                  value={useAI}
                  onValueChange={setUseAI}
                  trackColor={{ false: "#767577", true: "#007AFF40" }}
                  thumbColor={useAI ? "#007AFF" : "#f4f3f4"}
                />
              </View>
              {useAI && (
                <View className={`rounded-xl overflow-hidden shadow-sm ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
                  <TextInput
                    value={aiPrompt}
                    onChangeText={setAiPrompt}
                    placeholder="Enter prompt for AI generation"
                    placeholderTextColor={isDark ? "#666" : "#999"}
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                    className={`px-4 py-3.5 min-h-[100px] font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}
          />
        </View>
              )}
            </Animated.View>

            {/* Action Buttons */}
            <Animated.View 
              entering={FadeInDown.delay(900).duration(400)}
              className="flex-row justify-end space-x-4 mt-6"
            >
              <TouchableOpacity
                onPress={() => router.back()}
                className={`px-6 py-3 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}
              >
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              {useAI ? (
                <TouchableOpacity
                  onPress={handleGenerateWithAI}
                  disabled={loading}
                  className="bg-primary-500 px-6 py-3 rounded-xl"
                >
                  <Text className="text-white font-rubik-medium">
                    {loading ? "Generating..." : "Generate with AI"}
                  </Text>
                </TouchableOpacity>
              ) : (
        <TouchableOpacity
          onPress={handleCreate}
                  disabled={loading}
                  className="bg-primary-500 px-6 py-3 rounded-xl"
        >
          <Text className="text-white font-rubik-medium">
                    {loading ? "Creating..." : "Create Mock Test"}
          </Text>
        </TouchableOpacity>
              )}
            </Animated.View>
      </View>
    </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
