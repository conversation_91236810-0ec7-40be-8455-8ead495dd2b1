import { getUserByClerkId, supabase, User } from "@/lib/supabase";
import { useAuth, useUser } from "@clerk/clerk-expo";
import { useEffect, useState } from "react";

export function useSupabaseAuth() {
  const { isLoaded: authLoaded, isSignedIn, userId } = useAuth();
  const { user, isLoaded: userLoaded } = useUser();
  const [supabaseUser, setSupabaseUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      if (!authLoaded || !userLoaded) {
        setIsLoading(false);
        return;
      }

      if (!isSignedIn || !userId || !user) {
        setSupabaseUser(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        console.log("=== CLERK ID DEBUG ===");
        console.log("Fetching user from Supabase for Clerk ID:", userId);
        console.log("Clerk ID type:", typeof userId);
        console.log("Clerk ID length:", userId?.length);
        console.log("=== END DEBUG ===");

        // First, try to get user by Clerk ID
        let existingUser = await getUserByClerkId(userId);

        if (existingUser) {
          console.log("User found by Clerk ID:", existingUser);
          setSupabaseUser(existingUser);
          return;
        }

        // If not found by Clerk ID, try to find by email and update Clerk ID
        const primaryEmail = user.primaryEmailAddress?.emailAddress;
        if (primaryEmail) {
          console.log("User not found by Clerk ID, trying email:", primaryEmail);

          const { data: userByEmail, error: emailError } = await supabase
            .from('users')
            .select('*')
            .eq('email', primaryEmail)
            .single();

          if (emailError) {
            console.log("Supabase query error:", emailError);
            if (emailError.code === 'PGRST116') {
              // No rows returned - user doesn't exist
              setError("Account not found. Please contact your school administrator to create your teacher account.");
            } else {
              setError("Database error. Please try again.");
            }
            setSupabaseUser(null);
            return;
          }

          if (userByEmail) {
            console.log("User found by email:", userByEmail);

            // Check if this user has a teacher role
            if (userByEmail.role !== 'teacher') {
              setError("Access denied. This app is only for teachers.");
              setSupabaseUser(null);
              return;
            }

            // Update the clerk_user_id to link this Supabase user with the current Clerk user
            const { error: updateError } = await supabase
              .from('users')
              .update({ clerk_user_id: userId })
              .eq('id', userByEmail.id);

            if (updateError) {
              console.error("Error updating clerk_user_id:", updateError);
              setError("Failed to link account. Please try again.");
              setSupabaseUser(null);
              return;
            }

            console.log("Successfully linked Clerk user to Supabase user");

            // Fetch the updated user
            const updatedUser = await getUserByClerkId(userId);
            if (updatedUser) {
              console.log("Successfully retrieved linked user:", updatedUser);
              setSupabaseUser(updatedUser);
            } else {
              setError("Failed to retrieve updated user data.");
              setSupabaseUser(null);
            }
          } else {
            // User not found by email either
            setError("Account not found. Please contact your school administrator to create your teacher account.");
            setSupabaseUser(null);
          }
        } else {
          setError("No email address found in your account.");
          setSupabaseUser(null);
        }
      } catch (err: any) {
        console.error("Error fetching user from Supabase:", err);
        setError(err.message || "An error occurred while fetching user data");
        setSupabaseUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [authLoaded, userLoaded, isSignedIn, userId, user]);

  return {
    isLoaded: authLoaded && userLoaded && !isLoading,
    isSignedIn,
    clerkUser: user,
    supabaseUser,
    error,
  };
}
