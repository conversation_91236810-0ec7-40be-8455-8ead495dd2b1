import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, RefreshControl, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';

// Enrollment components
import { EnrollmentRequestList } from '@/components/enrollment/EnrollmentRequestList';
import { StudentList } from '@/components/enrollment/StudentList';

// Store
import { useEnrollmentStore, type Class } from '@/stores/enrollmentStore';

type TabType = 'enroll' | 'requests' | 'students';

const EnrollmentScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { supabaseUser, isLoaded } = useSupabaseAuth();

  const {
    currentTeacher,
    availableClasses,
    isLoading,
    error,
    refreshing,
    loadTeacherData,
    refreshData,
    clearError,
    reset
  } = useEnrollmentStore();

  const [activeTab, setActiveTab] = useState<TabType>('enroll');
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  // Load teacher data on mount
  useEffect(() => {
    const loadData = async () => {
      if (supabaseUser?.clerk_user_id) {
        await loadTeacherData(supabaseUser.clerk_user_id);
        setInitialLoading(false);
      }
    };

    if (isLoaded) {
      loadData();
    }

    return () => {
      reset();
    };
  }, [isLoaded, supabaseUser?.clerk_user_id, loadTeacherData, reset]);

  // Auto-select first class if only one available
  useEffect(() => {
    if (availableClasses.length === 1 && !selectedClass) {
      setSelectedClass(availableClasses[0]);
    }
  }, [availableClasses, selectedClass]);

  const handleRefresh = async () => {
    await refreshData();
  };

  const handleClassSelect = () => {
    router.push('/enrollment/class-selector');
  };

  const handleEnrollStudent = () => {
    if (!selectedClass) {
      Alert.alert('Select Class', 'Please select a class first');
      return;
    }
    router.push(`/enrollment/enroll-form?classId=${selectedClass.id}`);
  };

  const renderTabButton = (tab: TabType, title: string, icon: string) => {
    const isActive = activeTab === tab;
    
    return (
      <TouchableOpacity
        onPress={() => setActiveTab(tab)}
        className={`flex-1 py-3 px-4 rounded-lg mx-1 ${
          isActive 
            ? 'bg-primary-500' 
            : isDark ? 'bg-dark-surface' : 'bg-light-surface'
        }`}
      >
        <View className="items-center">
          <IconSymbol
            name={icon as any}
            size={20}
            color={isActive ? '#FFFFFF' : isDark ? '#9CA3AF' : '#6B7280'}
          />
          <Text className={`mt-1 font-rubik-medium text-xs ${
            isActive 
              ? 'text-white' 
              : isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
          }`}>
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'enroll':
        return (
          <View className="flex-1">
            {/* Class Selection Header */}
            <View className={`p-4 border-b ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'}`}>
              <TouchableOpacity
                onPress={handleClassSelect}
                className="flex-row items-center justify-between"
              >
                <View className="flex-1">
                  <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Selected Class:
                  </Text>
                  <Text className={`font-rubik-semibold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {selectedClass ? selectedClass.name : 'Select a class'}
                  </Text>
                  {selectedClass && (selectedClass.grade || selectedClass.section) && (
                    <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                      {selectedClass.grade && `Grade ${selectedClass.grade}`}
                      {selectedClass.grade && selectedClass.section && ' • '}
                      {selectedClass.section && `Section ${selectedClass.section}`}
                    </Text>
                  )}
                </View>
                <IconSymbol
                  name="chevron.right"
                  size={20}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
              </TouchableOpacity>
            </View>

            {/* Enroll Button */}
            <View className="p-4">
              <TouchableOpacity
                onPress={handleEnrollStudent}
                disabled={!selectedClass || isLoading}
                className={`py-4 rounded-lg items-center justify-center ${
                  selectedClass && !isLoading ? 'bg-primary-500' : 'bg-gray-400'
                }`}
              >
                <View className="flex-row items-center">
                  <IconSymbol
                    name="person.badge.plus"
                    size={20}
                    color="#FFFFFF"
                  />
                  <Text className="ml-2 text-white font-rubik-semibold text-base">
                    Enroll New Student
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* Quick Stats */}
            {selectedClass && (
              <View className="px-4">
                <View className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
                  <Text className={`font-rubik-semibold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Class Statistics
                  </Text>
                  <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Total Students: {selectedClass.student_count || 0}
                  </Text>
                </View>
              </View>
            )}
          </View>
        );

      case 'requests':
        return <EnrollmentRequestList />;

      case 'students':
        return <StudentList selectedClass={selectedClass} />;

      default:
        return null;
    }
  };

  // Show loading screen
  if (!isLoaded || initialLoading) {
    return <LoadingScreen message="Loading enrollment system..." />;
  }

  // Show error if teacher data failed to load
  if (error) {
    return (
      <ErrorScreen 
        message={error}
        onRetry={() => {
          clearError();
          if (supabaseUser?.clerk_user_id) {
            loadTeacherData(supabaseUser.clerk_user_id);
          }
        }}
      />
    );
  }

  // Show message if teacher is not a class teacher
  if (currentTeacher && !currentTeacher.is_class_teacher) {
    console.log('❌ Access restricted - Teacher data:', currentTeacher);
    return (
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <View className="flex-1 justify-center items-center p-6">
          <IconSymbol
            name="exclamationmark.triangle.fill"
            size={48}
            color={isDark ? '#F59E0B' : '#D97706'}
          />
          <Text className={`text-center mt-4 font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Access Restricted
          </Text>
          <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Only class teachers can access the student enrollment system. Please contact your administrator if you believe this is an error.
          </Text>
          <Text className={`text-center mt-4 font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Debug: is_class_teacher = {currentTeacher.is_class_teacher ? 'true' : 'false'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Add debug logging for successful access
  if (currentTeacher && currentTeacher.is_class_teacher) {
    console.log('✅ Access granted - Teacher data:', currentTeacher);
  }

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Student Enrollment
          </Text>
          <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Manage student enrollments and requests
          </Text>
        </View>

        {/* Tab Navigation */}
        <View className="flex-row p-4">
          {renderTabButton('enroll', 'Enroll', 'person.badge.plus')}
          {renderTabButton('requests', 'Requests', 'list.bullet.clipboard')}
          {renderTabButton('students', 'Students', 'person.2.fill')}
        </View>

        {/* Content */}
        <ScrollView
          className="flex-1"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={isDark ? '#60A5FA' : '#2563EB'}
            />
          }
        >
          {renderContent()}
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default EnrollmentScreen;
