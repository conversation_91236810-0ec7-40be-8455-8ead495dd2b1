import React from 'react';
import { View, Text } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface TimeData {
  submissionTrends: { date: string; submissions: number }[];
  averageTimeToComplete?: number;
  peakSubmissionTime?: string;
  submissionPattern: {
    early: number;
    onTime: number;
    late: number;
  };
}

interface Assignment {
  id: string;
  title: string;
  due_date: string;
}

interface TimeAnalysisProps {
  data: { submissionTrends: { date: string; submissions: number }[] } | null;
  assignment: Assignment;
}

export default function TimeAnalysis({ data, assignment }: TimeAnalysisProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  if (!data) {
    return (
      <View className="items-center justify-center py-8">
        <Text className={`font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          No time analysis data available
        </Text>
      </View>
    );
  }

  // Mock additional time data
  const timeData: TimeData = {
    submissionTrends: data.submissionTrends,
    averageTimeToComplete: 45, // minutes
    peakSubmissionTime: '8:00 PM',
    submissionPattern: {
      early: 12,
      onTime: 18,
      late: 5,
    },
  };

  const renderSubmissionTrends = () => {
    const maxSubmissions = Math.max(...timeData.submissionTrends.map(item => item.submissions));

    return (
      <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Submission Timeline
        </Text>

        <View className="space-y-3">
          {timeData.submissionTrends.map((item, index) => {
            const barWidth = (item.submissions / maxSubmissions) * 100;
            const date = new Date(item.date);
            const isToday = date.toDateString() === new Date().toDateString();
            const isDueDate = date.toDateString() === new Date(assignment.due_date).toDateString();

            return (
              <View key={index} className="space-y-2">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {date.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </Text>
                    {isToday && (
                      <View className="ml-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded">
                        <Text className="text-blue-600 dark:text-blue-300 text-xs font-rubik-medium">
                          Today
                        </Text>
                      </View>
                    )}
                    {isDueDate && (
                      <View className="ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">
                        <Text className="text-red-600 dark:text-red-300 text-xs font-rubik-medium">
                          Due Date
                        </Text>
                      </View>
                    )}
                  </View>
                  <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {item.submissions}
                  </Text>
                </View>
                <View className="h-4 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                  <View 
                    className={`h-full rounded-lg ${
                      isDueDate ? 'bg-red-500' : isToday ? 'bg-blue-500' : 'bg-primary-500'
                    }`}
                    style={{ width: `${barWidth}%` }}
                  />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSubmissionPattern = () => {
    const total = timeData.submissionPattern.early + 
                  timeData.submissionPattern.onTime + 
                  timeData.submissionPattern.late;

    const earlyPercentage = (timeData.submissionPattern.early / total) * 100;
    const onTimePercentage = (timeData.submissionPattern.onTime / total) * 100;
    const latePercentage = (timeData.submissionPattern.late / total) * 100;

    return (
      <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Submission Timing Pattern
        </Text>

        {/* Visual representation */}
        <View className="mb-4">
          <View className="flex-row h-8 rounded-lg overflow-hidden">
            <View 
              className="bg-green-500" 
              style={{ width: `${earlyPercentage}%` }}
            />
            <View 
              className="bg-blue-500" 
              style={{ width: `${onTimePercentage}%` }}
            />
            <View 
              className="bg-red-500" 
              style={{ width: `${latePercentage}%` }}
            />
          </View>
        </View>

        {/* Legend */}
        <View className="space-y-3">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="w-4 h-4 bg-green-500 rounded mr-3" />
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Early Submissions
              </Text>
            </View>
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {timeData.submissionPattern.early} ({earlyPercentage.toFixed(1)}%)
            </Text>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="w-4 h-4 bg-blue-500 rounded mr-3" />
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                On-Time Submissions
              </Text>
            </View>
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {timeData.submissionPattern.onTime} ({onTimePercentage.toFixed(1)}%)
            </Text>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="w-4 h-4 bg-red-500 rounded mr-3" />
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Late Submissions
              </Text>
            </View>
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {timeData.submissionPattern.late} ({latePercentage.toFixed(1)}%)
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderTimeInsights = () => (
    <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
      <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        Time Insights
      </Text>

      <View className="space-y-4">
        {/* Average completion time */}
        <View className="flex-row items-center">
          <View className={`p-3 rounded-lg mr-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
            <Ionicons name="time-outline" size={24} color="#3B82F6" />
          </View>
          <View className="flex-1">
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {timeData.averageTimeToComplete} minutes
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Average completion time
            </Text>
          </View>
        </View>

        {/* Peak submission time */}
        <View className="flex-row items-center">
          <View className={`p-3 rounded-lg mr-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
            <Ionicons name="trending-up-outline" size={24} color="#10B981" />
          </View>
          <View className="flex-1">
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {timeData.peakSubmissionTime}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Peak submission time
            </Text>
          </View>
        </View>

        {/* Days until due */}
        <View className="flex-row items-center">
          <View className={`p-3 rounded-lg mr-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
            <Ionicons name="calendar-outline" size={24} color="#F59E0B" />
          </View>
          <View className="flex-1">
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {Math.ceil((new Date(assignment.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Until due date
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <View className="space-y-4">
      <Animated.View entering={FadeInDown.delay(100).duration(400)}>
        {renderTimeInsights()}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(200).duration(400)}>
        {renderSubmissionTrends()}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        {renderSubmissionPattern()}
      </Animated.View>
    </View>
  );
}
