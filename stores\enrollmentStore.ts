import { CACH<PERSON>_CONFIG, <PERSON><PERSON><PERSON><PERSON><PERSON>, cacheManager } from '@/lib/cache';
import { supabaseAdmin } from '@/lib/supabase';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types for enrollment system
export interface Student {
  id: string;
  name: string;
  email: string;
  tenant_id: string;
  user_id?: string;
  student_id?: string;
  class?: string; // This is the grade in the database
  section?: string;
  roll_number?: string;
  enrollment_code?: string;
  admission_date?: string;
  created_at: string;
  updated_at?: string;
}

export interface EnrollmentRequest {
  id: string;
  tenant_id: string;
  teacher_id: string;
  class_id: string;
  student_name: string;
  student_email: string;
  student_grade?: string;
  student_section?: string;
  roll_number?: string;
  date_of_birth?: string;
  parent_name?: string;
  parent_email?: string;
  parent_phone?: string;
  enrollment_code: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  notes?: string;
  created_at: string;
  updated_at?: string;
}

export interface ClassStudent {
  id: string;
  tenant_id: string;
  class_id: string;
  student_id: string;
  enrollment_date: string;
  status: 'active' | 'inactive' | 'transferred';
  created_at: string;
  updated_at?: string;
  student?: Student;
}

export interface Class {
  id: string;
  tenant_id: string;
  name: string;
  teacher_id: string;
  grade?: string;
  section?: string;
  created_at: string;
  student_count?: number;
}

export interface Teacher {
  id: string;
  name: string;
  email: string;
  tenant_id: string;
  clerk_user_id?: string;
  is_class_teacher: boolean;
  created_at: string;
  updated_at?: string;
}

interface EnrollmentState {
  // State
  enrollmentRequests: EnrollmentRequest[];
  classStudents: ClassStudent[];
  availableClasses: Class[];
  currentTeacher: Teacher | null;
  isLoading: boolean;
  error: string | null;
  refreshing: boolean;

  // Actions
  loadTeacherData: (clerkUserId: string) => Promise<void>;
  loadEnrollmentRequests: (teacherId: string) => Promise<void>;
  loadClassStudents: (classId: string) => Promise<void>;
  loadAvailableClasses: (userId: string) => Promise<void>;
  createEnrollmentRequest: (request: Omit<EnrollmentRequest, 'id' | 'tenant_id' | 'teacher_id' | 'enrollment_code' | 'status' | 'created_at' | 'updated_at'>) => Promise<EnrollmentRequest | null>;
  updateEnrollmentRequest: (id: string, updates: Partial<EnrollmentRequest>) => Promise<void>;
  approveEnrollmentRequest: (requestId: string) => Promise<void>;
  rejectEnrollmentRequest: (requestId: string, reason?: string) => Promise<void>;
  enrollStudent: (classId: string, studentData: Omit<Student, 'id' | 'tenant_id' | 'created_at' | 'updated_at'>) => Promise<Student | null>;
  removeStudentFromClass: (classId: string, studentId: string) => Promise<void>;
  transferStudent: (studentId: string, fromClassId: string, toClassId: string) => Promise<void>;
  refreshData: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export const useEnrollmentStore = create<EnrollmentState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      enrollmentRequests: [],
      classStudents: [],
      availableClasses: [],
      currentTeacher: null,
      isLoading: false,
      error: null,
      refreshing: false,

      // Load teacher data and check if they're a class teacher
      loadTeacherData: async (clerkUserId: string) => {
        const cacheKey = `teacher_data_${clerkUserId}`;

        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          // Clear any existing cache for this key to get fresh data
          await cacheManager.delete(cacheKey);

          const teacher = await cacheManager.get<Teacher>(
            cacheKey,
            async () => {
              console.log('Loading teacher data for Clerk ID:', clerkUserId);

              // Get teacher record directly using clerk_user_id
              const { data: teacherRecords, error: teacherError } = await supabaseAdmin
                .from('teachers')
                .select('*, users!inner(*)')
                .eq('users.clerk_user_id', clerkUserId)
                .single();

              console.log('Teacher query result:', { teacherRecords, teacherError });

              if (teacherError) {
                throw new Error(`Teacher query failed: ${teacherError.message}`);
              }

              if (!teacherRecords) {
                throw new Error('No teacher record found');
              }

              // Transform the data to match Teacher interface
              const teacher: Teacher = {
                id: teacherRecords.id,
                name: teacherRecords.users.name,
                email: teacherRecords.users.email,
                tenant_id: teacherRecords.tenant_id,
                clerk_user_id: teacherRecords.users.clerk_user_id,
                is_class_teacher: teacherRecords.is_class_teacher || false,
                created_at: teacherRecords.created_at,
                updated_at: teacherRecords.updated_at || undefined,
              };

              console.log('Final teacher object:', teacher);
              return teacher;
            },
            CACHE_CONFIG.USER_DATA
          );

          set((state) => {
            state.currentTeacher = teacher;
            state.isLoading = false;
          });

          // Load related data if teacher is a class teacher
          if (teacher?.is_class_teacher) {
            await get().loadAvailableClasses(teacher.id);
            await get().loadEnrollmentRequests(teacher.id);
          }
        } catch (error) {
          console.error('Error loading teacher data:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load teacher data';
            state.isLoading = false;
          });
        }
      },

      // Load enrollment requests for a teacher
      loadEnrollmentRequests: async (teacherId: string) => {
        const cacheKey = `enrollment_requests_${teacherId}`;

        try {
          const requests = await cacheManager.get<EnrollmentRequest[]>(
            cacheKey,
            async () => {
              const { data, error } = await supabaseAdmin
                .from('enrollment_requests')
                .select('*')
                .eq('teacher_id', teacherId)
                .order('created_at', { ascending: false });

              if (error) throw new Error(`Failed to load enrollment requests: ${error.message}`);
              return data as EnrollmentRequest[];
            },
            CACHE_CONFIG.ASSIGNMENTS // 3 minutes cache
          );

          set((state) => {
            state.enrollmentRequests = requests || [];
          });
        } catch (error) {
          console.error('Error loading enrollment requests:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load enrollment requests';
          });
        }
      },

      // Load students in a class
      loadClassStudents: async (classId: string) => {
        const cacheKey = CacheHelpers.studentsKey(classId);

        try {
          const classStudents = await cacheManager.get<ClassStudent[]>(
            cacheKey,
            async () => {
              const { data, error } = await supabaseAdmin
                .from('class_students')
                .select(`
                  *,
                  student:students(*)
                `)
                .eq('class_id', classId)
                .eq('status', 'active')
                .order('created_at', { ascending: false });

              if (error) throw new Error(`Failed to load class students: ${error.message}`);
              return data as ClassStudent[];
            },
            CACHE_CONFIG.STUDENTS
          );

          set((state) => {
            state.classStudents = classStudents || [];
          });
        } catch (error) {
          console.error('Error loading class students:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load class students';
          });
        }
      },

      // Load available classes for a teacher
      loadAvailableClasses: async (teacherId: string) => {
        const cacheKey = CacheHelpers.classesKey(teacherId);

        try {
          const classes = await cacheManager.get<Class[]>(
            cacheKey,
            async () => {
              // Check if teacherId is actually a teacher ID or user ID
              let actualTeacherId = teacherId;

              // If it looks like a user ID (from Clerk), find the teacher record
              if (teacherId.startsWith('user_')) {
                const { data: teacherRecords, error: teacherError } = await supabaseAdmin
                  .from('teachers')
                  .select('id')
                  .eq('clerk_user_id', teacherId);

                if (teacherError) throw new Error(`Failed to find teacher: ${teacherError.message}`);
                if (!teacherRecords || teacherRecords.length === 0) throw new Error('Teacher not found');

                actualTeacherId = teacherRecords[0].id;
              } else {
                // Verify the teacher exists
                const { data: teacherExists, error: verifyError } = await supabaseAdmin
                  .from('teachers')
                  .select('id')
                  .eq('id', teacherId)
                  .single();

                if (verifyError || !teacherExists) {
                  throw new Error('Teacher not found');
                }
              }

              // Then get the classes
              const { data, error } = await supabaseAdmin
                .from('classes')
                .select(`
                  id,
                  tenant_id,
                  name,
                  teacher_id,
                  grade,
                  section,
                  created_at
                `)
                .eq('teacher_id', actualTeacherId);

              if (error) throw new Error(`Failed to load classes: ${error.message}`);

              // Add student count for each class
              const classesWithCount = await Promise.all(
                (data || []).map(async (classItem) => {
                  // Use regular select and count the results
                  const { data: students } = await supabaseAdmin
                    .from('class_students')
                    .select('id')
                    .eq('class_id', classItem.id)
                    .eq('status', 'active');

                  return {
                    ...classItem,
                    student_count: students ? students.length : 0
                  };
                })
              );

              return classesWithCount as Class[];
            },
            CACHE_CONFIG.CLASSES
          );

          set((state) => {
            state.availableClasses = classes || [];
          });
        } catch (error) {
          console.error('Error loading classes:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load classes';
          });
        }
      },

      // Create a new enrollment request
      createEnrollmentRequest: async (requestData) => {
        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const currentTeacher = get().currentTeacher;
          if (!currentTeacher) {
            throw new Error('No teacher data available');
          }

          if (!currentTeacher.is_class_teacher) {
            throw new Error('Only class teachers can create enrollment requests');
          }

          // Generate enrollment code
          const enrollmentCode = Math.random().toString(36).substring(2, 10).toUpperCase();

          const insertData = {
            ...requestData,
            teacher_id: currentTeacher.id,
            tenant_id: currentTeacher.tenant_id,
            enrollment_code: enrollmentCode,
            status: 'pending' as const,
          };

          const { data, error } = await supabaseAdmin
            .from('enrollment_requests')
            .insert(insertData)
            .select()
            .single();

          if (error) throw new Error(`Failed to create enrollment request: ${error.message}`);

          const newRequest = data as EnrollmentRequest;

          set((state) => {
            state.enrollmentRequests.unshift(newRequest);
            state.isLoading = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`enrollment_requests_${currentTeacher.id}`);

          return newRequest;
        } catch (error) {
          console.error('Error creating enrollment request:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create enrollment request';
            state.isLoading = false;
          });
          return null;
        }
      },

      // Update enrollment request
      updateEnrollmentRequest: async (id, updates) => {
        try {
          const { error } = await supabaseAdmin
            .from('enrollment_requests')
            .update(updates)
            .eq('id', id);

          if (error) throw new Error(`Failed to update enrollment request: ${error.message}`);

          set((state) => {
            const index = state.enrollmentRequests.findIndex(req => req.id === id);
            if (index !== -1) {
              state.enrollmentRequests[index] = { ...state.enrollmentRequests[index], ...updates };
            }
          });

          // Invalidate cache
          const currentTeacher = get().currentTeacher;
          if (currentTeacher) {
            await cacheManager.invalidatePattern(`enrollment_requests_${currentTeacher.id}`);
          }
        } catch (error) {
          console.error('Error updating enrollment request:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update enrollment request';
          });
        }
      },

      // Approve enrollment request and create student
      approveEnrollmentRequest: async (requestId) => {
        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const request = get().enrollmentRequests.find(req => req.id === requestId);
          if (!request) {
            throw new Error('Enrollment request not found');
          }

          // Generate student ID
          const studentId = `STD${Date.now().toString().slice(-6)}`;

          // Create student record
          const studentData = {
            name: request.student_name,
            email: request.student_email,
            tenant_id: request.tenant_id,
            student_id: studentId,
            class: request.student_grade, // Use 'class' column instead of 'grade'
            section: request.student_section,
            roll_number: request.roll_number,
            enrollment_code: request.enrollment_code,
          };

          const { data: student, error: studentError } = await supabaseAdmin
            .from('students')
            .insert(studentData)
            .select()
            .single();

          if (studentError) throw new Error(`Failed to create student: ${studentError.message}`);

          // Add student to class
          const { error: classStudentError } = await supabaseAdmin
            .from('class_students')
            .insert({
              tenant_id: request.tenant_id,
              class_id: request.class_id,
              student_id: student.id,
            });

          if (classStudentError) throw new Error(`Failed to add student to class: ${classStudentError.message}`);

          // Update enrollment request status
          await get().updateEnrollmentRequest(requestId, { status: 'completed' });

          set((state) => {
            state.isLoading = false;
          });

          // Invalidate related caches
          await cacheManager.invalidatePattern(`students_${request.class_id}`);
          await cacheManager.invalidatePattern(`classes_`);
        } catch (error) {
          console.error('Error approving enrollment request:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to approve enrollment request';
            state.isLoading = false;
          });
        }
      },

      // Reject enrollment request
      rejectEnrollmentRequest: async (requestId, reason) => {
        try {
          await get().updateEnrollmentRequest(requestId, {
            status: 'rejected',
            notes: reason
          });
        } catch (error) {
          console.error('Error rejecting enrollment request:', error);
        }
      },

      // Direct student enrollment (for immediate enrollment)
      enrollStudent: async (classId, studentData) => {
        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const currentTeacher = get().currentTeacher;
          if (!currentTeacher) {
            throw new Error('No teacher data available');
          }

          if (!currentTeacher.is_class_teacher) {
            throw new Error('Only class teachers can enroll students');
          }

          // Generate enrollment code
          const enrollmentCode = Math.random().toString(36).substring(2, 10).toUpperCase();

          // Generate student ID
          const studentId = `STD${Date.now().toString().slice(-6)}`;

          // Create student record
          const studentRecord = {
            name: studentData.name,
            email: studentData.email,
            tenant_id: currentTeacher.tenant_id,
            student_id: studentId,
            class: studentData.class, // Use class field directly
            section: studentData.section,
            roll_number: studentData.roll_number,
            enrollment_code: enrollmentCode,
          };

          const { data: student, error: studentError } = await supabaseAdmin
            .from('students')
            .insert(studentRecord)
            .select()
            .single();

          if (studentError) throw new Error(`Failed to create student: ${studentError.message}`);

          // Add to class
          const { error: classStudentError } = await supabaseAdmin
            .from('class_students')
            .insert({
              tenant_id: currentTeacher.tenant_id,
              class_id: classId,
              student_id: student.id,
            });

          if (classStudentError) throw new Error(`Failed to add student to class: ${classStudentError.message}`);

          set((state) => {
            state.isLoading = false;
          });

          // Invalidate caches
          await cacheManager.invalidatePattern(`students_${classId}`);
          await cacheManager.invalidatePattern(`classes_`);

          // Return student as-is (class field is already correct)
          return student as Student;
        } catch (error) {
          console.error('Error enrolling student:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to enroll student';
            state.isLoading = false;
          });
          return null;
        }
      },

      // Remove student from class
      removeStudentFromClass: async (classId, studentId) => {
        try {
          const { error } = await supabaseAdmin
            .from('class_students')
            .update({ status: 'inactive' })
            .eq('class_id', classId)
            .eq('student_id', studentId);

          if (error) throw new Error(`Failed to remove student from class: ${error.message}`);

          set((state) => {
            state.classStudents = state.classStudents.filter(
              cs => !(cs.class_id === classId && cs.student_id === studentId)
            );
          });

          // Invalidate caches
          await cacheManager.invalidatePattern(`students_${classId}`);
          await cacheManager.invalidatePattern(`classes_`);
        } catch (error) {
          console.error('Error removing student from class:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to remove student from class';
          });
        }
      },

      // Transfer student between classes
      transferStudent: async (studentId, fromClassId, toClassId) => {
        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const currentTeacher = get().currentTeacher;
          if (!currentTeacher) {
            throw new Error('No teacher data available');
          }

          // Update old class_student record
          const { error: updateError } = await supabaseAdmin
            .from('class_students')
            .update({ status: 'transferred' })
            .eq('class_id', fromClassId)
            .eq('student_id', studentId);

          if (updateError) throw new Error(`Failed to update old class record: ${updateError.message}`);

          // Create new class_student record
          const { error: insertError } = await supabaseAdmin
            .from('class_students')
            .insert({
              tenant_id: currentTeacher.tenant_id,
              class_id: toClassId,
              student_id: studentId,
            });

          if (insertError) throw new Error(`Failed to add student to new class: ${insertError.message}`);

          set((state) => {
            state.isLoading = false;
          });

          // Invalidate caches
          await cacheManager.invalidatePattern(`students_${fromClassId}`);
          await cacheManager.invalidatePattern(`students_${toClassId}`);
          await cacheManager.invalidatePattern(`classes_`);
        } catch (error) {
          console.error('Error transferring student:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to transfer student';
            state.isLoading = false;
          });
        }
      },

      // Refresh all data
      refreshData: async () => {
        try {
          set((state) => {
            state.refreshing = true;
            state.error = null;
          });

          const currentTeacher = get().currentTeacher;
          if (currentTeacher?.is_class_teacher) {
            await Promise.all([
              get().loadEnrollmentRequests(currentTeacher.id),
              get().loadAvailableClasses(currentTeacher.id),
            ]);
          }

          set((state) => {
            state.refreshing = false;
          });
        } catch (error) {
          console.error('Error refreshing data:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to refresh data';
            state.refreshing = false;
          });
        }
      },

      // Clear error
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      // Reset store
      reset: () => {
        set((state) => {
          state.enrollmentRequests = [];
          state.classStudents = [];
          state.availableClasses = [];
          state.currentTeacher = null;
          state.isLoading = false;
          state.error = null;
          state.refreshing = false;
        });
      },
    }))
  )
);
