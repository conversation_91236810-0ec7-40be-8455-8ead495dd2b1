import { IconSymbol, SFSymbols6_0 } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

interface TeacherDashboardCardProps {
  title: string;
  icon: SFSymbols6_0;
  count?: number;
  onPress: () => void;
  loading?: boolean;
}

export const TeacherDashboardCard: React.FC<TeacherDashboardCardProps> = ({
  title,
  icon,
  count,
  onPress,
  loading = false
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={loading}
      className={`
        p-4 rounded-xl mb-3
        ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}
        ${loading ? 'opacity-60' : 'opacity-100'}
        shadow-sm
      `}
      style={{
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}
    >
      <View className="flex-row items-center">
        {/* Icon Container */}
        <View
          className={`
            w-12 h-12 rounded-full items-center justify-center mr-4
            ${isDark ? 'bg-primary-500/20' : 'bg-primary-100'}
          `}
        >
          <IconSymbol
            name={icon}
            size={24}
            color={isDark ? '#60A5FA' : '#2563EB'}
          />
        </View>

        {/* Content */}
        <View className="flex-1">
          <Text
            className={`
              text-base font-rubik-semibold mb-1
              ${isDark ? 'text-dark-text' : 'text-light-text'}
            `}
          >
            {title}
          </Text>

          {count !== undefined && (
            <Text
              className={`
                text-sm font-rubik
                ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}
              `}
            >
              {count} {count === 1 ? 'item' : 'items'}
            </Text>
          )}
        </View>

        {/* Chevron */}
        <IconSymbol
          name="chevron.right"
          size={20}
          color={isDark ? '#9CA3AF' : '#6B7280'}
        />
      </View>
    </TouchableOpacity>
  );
};
