import { useAuth, useUser } from '@clerk/clerk-expo';
import { useCallback } from 'react';
import { getCurrentUserAndTeacher } from '@/lib/authHelpers';

export const useAuthenticatedSupabase = () => {
  const { getToken, isSignedIn } = useAuth();
  const { user } = useUser();

  const getCurrentUserAndTeacherWithAuth = useCallback(async () => {
    try {
      console.log('useAuthenticatedSupabase: Getting user and teacher...');
      
      if (!isSignedIn || !user) {
        throw new Error('User not signed in with Clerk');
      }

      // Get the Supabase token from Clerk
      const token = await getToken({ template: 'supabase' });
      console.log('Clerk token obtained:', !!token);

      if (!token) {
        throw new Error('Failed to get Supabase token from <PERSON>');
      }

      // Call our helper with the Clerk user ID and token
      const result = await getCurrentUserAndTeacher(user.id, token);
      
      console.log('Successfully got user and teacher with auth');
      return result;
      
    } catch (error) {
      console.error('Error in useAuthenticatedSupabase:', error);
      throw error;
    }
  }, [getToken, isSignedIn, user]);

  return {
    getCurrentUserAndTeacher: getCurrentUserAndTeacherWithAuth,
    isSignedIn,
    user,
  };
};
