import { IconSymbol, SFSymbols6_0 } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

interface StatsCardProps {
  title: string;
  value: number;
  icon: SFSymbols6_0;
  onPress?: () => void;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  onPress
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const Component = onPress ? TouchableOpacity : View;

  return (
    <Component
      onPress={onPress}
      className={`
        flex-1 mx-1 p-4 rounded-xl
        ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}
        shadow-sm
      `}
      style={{
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}
    >
      <View className="items-center">
        {/* Icon Container */}
        <View
          className={`
            w-10 h-10 rounded-full items-center justify-center mb-2
            ${isDark ? 'bg-primary-500/20' : 'bg-primary-100'}
          `}
        >
          <IconSymbol
            name={icon}
            size={20}
            color={isDark ? '#60A5FA' : '#2563EB'}
          />
        </View>

        {/* Value */}
        <Text
          className={`
            text-2xl font-rubik-bold mb-1
            ${isDark ? 'text-dark-text' : 'text-light-text'}
          `}
        >
          {value}
        </Text>

        {/* Title */}
        <Text
          className={`
            text-xs font-rubik text-center
            ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}
          `}
        >
          {title}
        </Text>
      </View>
    </Component>
  );
};
