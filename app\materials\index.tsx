import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { useEnrollmentStore } from '@/stores/enrollmentStore';
import { Material, useMaterialStore } from '@/stores/materialStore';
import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    FlatList,
    RefreshControl,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

const MaterialsScreen: React.FC = () => {
  const isDark = useColorScheme() === 'dark';
  const router = useRouter();
  const { isLoaded } = useSupabaseAuth();
  const { userId: clerkUserId } = useAuth();
  const { currentTeacher, loadTeacherData } = useEnrollmentStore();
  
  const {
    materials,
    sharedMaterials,
    isLoading,
    error,
    refreshing,
    loadMaterials,
    loadSharedMaterials,
    incrementViewCount,
    incrementDownloadCount,
    shareMaterial,
    refreshData,
    clearError,
  } = useMaterialStore();

  const [activeTab, setActiveTab] = useState<'my' | 'shared'>('my');
  const [searchQuery, setSearchQuery] = useState('');
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);

  // Load teacher data when the screen mounts
  useEffect(() => {
    if (isLoaded && clerkUserId && !currentTeacher) {
      loadTeacherData(clerkUserId);
    }
  }, [isLoaded, clerkUserId, currentTeacher, loadTeacherData]);

  useEffect(() => {
    if (currentTeacher?.id) {
      loadMaterials(currentTeacher.id);
      loadSharedMaterials(currentTeacher.id);
    }
  }, [currentTeacher?.id]);

  const handleRefresh = useCallback(async () => {
    if (currentTeacher?.id) {
      await refreshData();
      await loadMaterials(currentTeacher.id);
      await loadSharedMaterials(currentTeacher.id);
    }
  }, [currentTeacher?.id]);

  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query);
    if (currentTeacher?.id) {
      await loadMaterials(currentTeacher.id, { search_query: query });
    }
  }, [currentTeacher?.id]);

  const handleMaterialPress = async (material: Material) => {
    await incrementViewCount(material.id);
    router.push(`/materials/view/${material.id}` as any);
  };

  const handleSharePress = (material: Material) => {
    setSelectedMaterial(material);
    setShowShareModal(true);
  };

  const handleShare = async (shareType: 'class' | 'school' | 'public') => {
    if (!selectedMaterial || !currentTeacher?.id) return;

    try {
      // Update material visibility in the database
      await useMaterialStore.getState().updateMaterial(selectedMaterial.id, {
        visibility: shareType,
      });

      // Refresh the materials list
      await loadMaterials(currentTeacher.id);
      await loadSharedMaterials(currentTeacher.id);

      Alert.alert(
        'Success',
        `Material "${selectedMaterial.title}" has been shared successfully!`
      );
      setShowShareModal(false);
      setSelectedMaterial(null);
    } catch (error) {
      console.error('Error sharing material:', error);
      Alert.alert('Error', 'Failed to share material. Please try again.');
    }
  };

  const renderMaterialItem = ({ item, index }: { item: Material; index: number }) => (
    <View
      className={`mx-4 mb-4 rounded-2xl ${
        isDark ? 'bg-dark-surface' : 'bg-white'
      }`}
      style={{
        shadowColor: isDark ? '#000' : '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: isDark ? 0.3 : 0.1,
        shadowRadius: 12,
        elevation: 6,
      }}
    >
      <TouchableOpacity
        onPress={() => handleMaterialPress(item)}
        className="p-5"
        activeOpacity={0.7}
      >
        {/* Header with title and badges */}
        <View className="flex-row items-start justify-between mb-3">
          <View className="flex-1 mr-3">
            <Text className={`font-rubik-bold text-lg leading-6 ${
              isDark ? 'text-dark-text' : 'text-gray-900'
            }`} numberOfLines={2}>
              {item.title}
            </Text>
            {item.description && (
              <Text className={`font-rubik text-sm mt-2 leading-5 ${
                isDark ? 'text-dark-textSecondary' : 'text-gray-600'
              }`} numberOfLines={2}>
                {item.description}
              </Text>
            )}
          </View>

          <View className="flex-row items-center">
            {item.gemini_generated && (
              <View className="bg-gradient-to-r from-purple-500 to-pink-500 px-3 py-1.5 rounded-full mr-2">
                <Text className="text-white text-xs font-rubik-bold">✨ AI</Text>
              </View>
            )}
            <View className={`w-8 h-8 rounded-full items-center justify-center ${
              isDark ? 'bg-dark-background' : 'bg-gray-100'
            }`}>
              <IconSymbol
                name="chevron.right"
                size={14}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
            </View>
          </View>
        </View>

        {/* Tags and metadata */}
        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center flex-wrap">
            <View className={`px-3 py-1.5 rounded-full mr-2 mb-1 ${
              item.material_type === 'lesson_plan' ? (isDark ? 'bg-blue-500/20' : 'bg-blue-50') :
              item.material_type === 'worksheet' ? (isDark ? 'bg-green-500/20' : 'bg-green-50') :
              item.material_type === 'quiz' ? (isDark ? 'bg-orange-500/20' : 'bg-orange-50') :
              item.material_type === 'resource' ? (isDark ? 'bg-gray-500/20' : 'bg-gray-50') :
              item.material_type === 'assignment' ? (isDark ? 'bg-red-500/20' : 'bg-red-50') :
              (isDark ? 'bg-indigo-500/20' : 'bg-indigo-50')
            }`}>
              <Text className={`text-xs font-rubik-semibold ${
                item.material_type === 'lesson_plan' ? (isDark ? 'text-blue-300' : 'text-blue-700') :
                item.material_type === 'worksheet' ? (isDark ? 'text-green-300' : 'text-green-700') :
                item.material_type === 'quiz' ? (isDark ? 'text-orange-300' : 'text-orange-700') :
                item.material_type === 'resource' ? (isDark ? 'text-gray-300' : 'text-gray-700') :
                item.material_type === 'assignment' ? (isDark ? 'text-red-300' : 'text-red-700') :
                (isDark ? 'text-indigo-300' : 'text-indigo-700')
              }`}>
                {item.material_type.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
            {item.subject && (
              <View className={`px-3 py-1.5 rounded-full mr-2 mb-1 ${
                isDark ? 'bg-emerald-500/20' : 'bg-emerald-50'
              }`}>
                <Text className={`text-xs font-rubik-semibold ${
                  isDark ? 'text-emerald-300' : 'text-emerald-700'
                }`}>
                  {item.subject}
                </Text>
              </View>
            )}
            {item.grade_level && (
              <View className={`px-3 py-1.5 rounded-full mb-1 ${
                isDark ? 'bg-amber-500/20' : 'bg-amber-50'
              }`}>
                <Text className={`text-xs font-rubik-semibold ${
                  isDark ? 'text-amber-300' : 'text-amber-700'
                }`}>
                  Grade {item.grade_level}
                </Text>
              </View>
            )}
          </View>
        </View>


        {/* Stats and actions */}
        <View className={`flex-row items-center justify-between pt-3 border-t ${
          isDark ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <Text className={`text-xs font-rubik ${
            isDark ? 'text-dark-textSecondary' : 'text-gray-500'
          }`}>
            {new Date(item.created_at).toLocaleDateString()}
          </Text>

          {activeTab === 'my' && (
            <TouchableOpacity
              onPress={() => handleSharePress(item)}
              className={`p-2 rounded-lg ${
                isDark ? 'bg-primary-500/20' : 'bg-primary-50'
              }`}
            >
              <IconSymbol
                name="square.and.arrow.up"
                size={16}
                color={isDark ? '#60A5FA' : '#3B82F6'}
              />
            </TouchableOpacity>
          )}
        </View>

        {activeTab === 'shared' && item.uploaded_by_name && (
          <View className={`mt-3 pt-3 border-t ${
            isDark ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <View className="flex-row items-center">
              <View className={`w-6 h-6 rounded-full items-center justify-center mr-2 ${
                isDark ? 'bg-purple-500/20' : 'bg-purple-50'
              }`}>
                <IconSymbol
                  name="person.circle"
                  size={12}
                  color={isDark ? '#A78BFA' : '#8B5CF6'}
                />
              </View>
              <Text className={`text-xs font-rubik-medium ${
                isDark ? 'text-dark-textSecondary' : 'text-gray-600'
              }`}>
                Shared by {item.uploaded_by_name}
              </Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );

  const currentMaterials = activeTab === 'my' ? materials : sharedMaterials;
  const filteredMaterials = currentMaterials.filter(material =>
    material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (material.description && material.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View
        className="flex-row items-center justify-between p-4 pb-2"
      >
        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={() => router.back()}
            className="mr-4 p-2 rounded-lg"
            style={{ backgroundColor: isDark ? '#374151' : '#F3F4F6' }}
          >
            <IconSymbol
              name="arrow.left"
              size={20}
              color={isDark ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <View>
            <Text className={`text-2xl font-rubik-bold ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              Materials
            </Text>
            <Text className={`${
              isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
            }`}>
              Manage your teaching materials
            </Text>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => router.push('/materials/create')}
          className="bg-primary-500 p-3 rounded-xl"
          style={{
            elevation: 2,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}
        >
          <IconSymbol name="plus" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View
        className="px-6 mb-6"
      >
        <View className={`flex-row items-center p-4 rounded-2xl ${
          isDark ? 'bg-dark-surface' : 'bg-white'
        }`}
        style={{
          shadowColor: isDark ? '#000' : '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: isDark ? 0.3 : 0.1,
          shadowRadius: 8,
          elevation: 4,
        }}>
          <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
            isDark ? 'bg-primary-500/20' : 'bg-primary-50'
          }`}>
            <IconSymbol
              name="magnifyingglass"
              size={16}
              color={isDark ? '#60A5FA' : '#3B82F6'}
            />
          </View>
          <TextInput
            value={searchQuery}
            onChangeText={handleSearch}
            placeholder="Search materials..."
            placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
            className={`flex-1 font-rubik text-base ${
              isDark ? 'text-dark-text' : 'text-gray-900'
            }`}
          />
        </View>
      </View>

      {/* Tabs */}
      <View
        className="px-6 mb-6"
      >
        <View className={`flex-row p-1 rounded-2xl ${
          isDark ? 'bg-dark-surface' : 'bg-gray-100'
        }`}>
          <TouchableOpacity
            onPress={() => setActiveTab('my')}
            className={`flex-1 py-3 px-4 rounded-xl ${
              activeTab === 'my'
                ? 'bg-primary-500'
                : 'bg-transparent'
            }`}
            style={activeTab === 'my' ? {
              shadowColor: '#3B82F6',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 4,
              elevation: 3,
            } : {}}
          >
            <Text className={`text-center font-rubik-semibold ${
              activeTab === 'my'
                ? 'text-white'
                : isDark ? 'text-dark-text' : 'text-gray-600'
            }`}>
              My Materials
            </Text>
            <Text className={`text-center text-xs mt-1 ${
              activeTab === 'my'
                ? 'text-white/80'
                : isDark ? 'text-dark-textSecondary' : 'text-gray-500'
            }`}>
              {materials.length} items
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setActiveTab('shared')}
            className={`flex-1 py-3 px-4 rounded-xl ${
              activeTab === 'shared'
                ? 'bg-primary-500'
                : 'bg-transparent'
            }`}
            style={activeTab === 'shared' ? {
              shadowColor: '#3B82F6',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 4,
              elevation: 3,
            } : {}}
          >
            <Text className={`text-center font-rubik-semibold ${
              activeTab === 'shared'
                ? 'text-white'
                : isDark ? 'text-dark-text' : 'text-gray-600'
            }`}>
              Shared
            </Text>
            <Text className={`text-center text-xs mt-1 ${
              activeTab === 'shared'
                ? 'text-white/80'
                : isDark ? 'text-dark-textSecondary' : 'text-gray-500'
            }`}>
              {sharedMaterials.length} items
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Materials List */}
      <View className="flex-1">
        {isLoading && filteredMaterials.length === 0 ? (
          <View className="flex-1 justify-center items-center px-6">
            <View className={`w-16 h-16 rounded-full items-center justify-center mb-4 ${
              isDark ? 'bg-primary-500/20' : 'bg-primary-50'
            }`}>
              <ActivityIndicator size="large" color="#3B82F6" />
            </View>
            <Text className={`text-lg font-rubik-semibold mb-2 ${
              isDark ? 'text-dark-text' : 'text-gray-900'
            }`}>
              Loading materials...
            </Text>
            <Text className={`text-center ${
              isDark ? 'text-dark-textSecondary' : 'text-gray-600'
            }`}>
              Please wait while we fetch your materials
            </Text>
          </View>
        ) : filteredMaterials.length === 0 ? (
          <View className="flex-1 justify-center items-center px-6">
            <View className={`w-20 h-20 rounded-full items-center justify-center mb-6 ${
              isDark ? 'bg-gray-500/20' : 'bg-gray-100'
            }`}>
              <IconSymbol
                name="doc.text"
                size={40}
                color={isDark ? '#6B7280' : '#9CA3AF'}
              />
            </View>
            <Text className={`text-2xl font-rubik-bold mb-3 ${
              isDark ? 'text-dark-text' : 'text-gray-900'
            }`}>
              {activeTab === 'my' ? 'No Materials Yet' : 'No Shared Materials'}
            </Text>
            <Text className={`text-center text-base mb-6 ${
              isDark ? 'text-dark-textSecondary' : 'text-gray-600'
            }`}>
              {activeTab === 'my'
                ? 'Create your first material to get started with sharing educational resources'
                : 'No materials have been shared with you yet. Check back later!'
              }
            </Text>
            {activeTab === 'my' && (
              <TouchableOpacity
                onPress={() => router.push('/materials/create')}
                className="bg-primary-500 px-8 py-4 rounded-2xl flex-row items-center"
                style={{
                  shadowColor: '#3B82F6',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 6,
                }}
              >
                <IconSymbol name="plus" size={16} color="#FFFFFF" />
                <Text className="text-white font-rubik-semibold ml-2">Create Material</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <FlatList
            data={filteredMaterials}
            renderItem={renderMaterialItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#3B82F6']}
                tintColor="#3B82F6"
              />
            }
          />
        )}
      </View>

      {/* Share Modal */}
      {showShareModal && selectedMaterial && (
        <View className="absolute inset-0 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className={`w-full max-w-sm p-6 rounded-xl ${
            isDark ? 'bg-dark-surface' : 'bg-light-surface'
          }`}>
            <Text className={`text-xl font-rubik-bold mb-4 ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              Share Material
            </Text>
            <Text className={`mb-6 ${
              isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
            }`}>
              Share "{selectedMaterial.title}" with:
            </Text>

            <TouchableOpacity
              onPress={() => handleShare('class')}
              className="p-4 rounded-lg mb-3 border border-gray-200"
            >
              <Text className={`font-rubik-medium ${
                isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                My Class
              </Text>
              <Text className={`text-sm ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                Share with students in your class
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleShare('school')}
              className="p-4 rounded-lg mb-3 border border-gray-200"
            >
              <Text className={`font-rubik-medium ${
                isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                School Teachers
              </Text>
              <Text className={`text-sm ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                Share with all teachers in your school
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleShare('public')}
              className="p-4 rounded-lg mb-6 border border-gray-200"
            >
              <Text className={`font-rubik-medium ${
                isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                Public
              </Text>
              <Text className={`text-sm ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                Share with everyone
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                setShowShareModal(false);
                setSelectedMaterial(null);
              }}
              className="p-3 rounded-lg bg-gray-200"
            >
              <Text className="text-center font-rubik-medium text-gray-700">Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Error Message */}
      {error && (
        <View className="absolute bottom-4 left-4 right-4">
          <View className="bg-red-500 p-3 rounded-lg flex-row items-center">
            <IconSymbol name="exclamationmark.triangle.fill" size={20} color="#FFFFFF" />
            <Text className="text-white ml-2 flex-1">{error}</Text>
            <TouchableOpacity onPress={clearError}>
              <IconSymbol name="xmark" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default MaterialsScreen;