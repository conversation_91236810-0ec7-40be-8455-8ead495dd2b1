-- Create attendance system tables

-- Attendance sessions table
CREATE TABLE IF NOT EXISTS attendance_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  subject VARCHAR(100),
  session_date DATE NOT NULL,
  session_time TIME NOT NULL,
  session_type VARCHAR(20) DEFAULT 'regular' CHECK (session_type IN ('regular', 'exam', 'lab', 'assembly')),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  total_students INTEGER DEFAULT 0,
  present_count INTEGER DEFAULT 0,
  absent_count INTEGER DEFAULT 0,
  late_count INTEGER DEFAULT 0,
  excused_count INTEGER DEFAULT 0,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student attendance records table
CREATE TABLE IF NOT EXISTS attendance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  session_id UUID NOT NULL REFERENCES attendance_sessions(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL CHECK (status IN ('present', 'absent', 'late', 'excused')),
  marked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  marked_by UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(session_id, student_id)
);

-- Teacher attendance records table (for facial recognition)
CREATE TABLE IF NOT EXISTS teacher_attendance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  attendance_date DATE NOT NULL,
  check_in_time TIMESTAMP WITH TIME ZONE,
  check_out_time TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'half_day')),
  facial_recognition_confidence DECIMAL(5,2), -- Confidence score from facial recognition
  location_lat DECIMAL(10,8), -- GPS coordinates for location verification
  location_lng DECIMAL(11,8),
  device_info JSONB, -- Device information for security
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(teacher_id, attendance_date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_teacher_date ON attendance_sessions(teacher_id, session_date);
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_class_date ON attendance_sessions(class_id, session_date);
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_tenant ON attendance_sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_session ON attendance_records(session_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_student ON attendance_records(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_tenant ON attendance_records(tenant_id);
CREATE INDEX IF NOT EXISTS idx_teacher_attendance_teacher_date ON teacher_attendance(teacher_id, attendance_date);
CREATE INDEX IF NOT EXISTS idx_teacher_attendance_tenant ON teacher_attendance(tenant_id);

-- Function to update attendance session counts
CREATE OR REPLACE FUNCTION update_attendance_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the session counts when attendance records change
  UPDATE attendance_sessions 
  SET 
    present_count = (
      SELECT COUNT(*) FROM attendance_records 
      WHERE session_id = COALESCE(NEW.session_id, OLD.session_id) AND status = 'present'
    ),
    absent_count = (
      SELECT COUNT(*) FROM attendance_records 
      WHERE session_id = COALESCE(NEW.session_id, OLD.session_id) AND status = 'absent'
    ),
    late_count = (
      SELECT COUNT(*) FROM attendance_records 
      WHERE session_id = COALESCE(NEW.session_id, OLD.session_id) AND status = 'late'
    ),
    excused_count = (
      SELECT COUNT(*) FROM attendance_records 
      WHERE session_id = COALESCE(NEW.session_id, OLD.session_id) AND status = 'excused'
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.session_id, OLD.session_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update counts
DROP TRIGGER IF EXISTS trigger_update_attendance_counts ON attendance_records;
CREATE TRIGGER trigger_update_attendance_counts
  AFTER INSERT OR UPDATE OR DELETE ON attendance_records
  FOR EACH ROW EXECUTE FUNCTION update_attendance_counts();

-- Function to get attendance statistics
CREATE OR REPLACE FUNCTION get_attendance_stats(
  p_teacher_id UUID,
  p_class_id UUID DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  total_sessions INTEGER,
  total_students INTEGER,
  average_attendance DECIMAL(5,2),
  present_percentage DECIMAL(5,2),
  absent_percentage DECIMAL(5,2),
  late_percentage DECIMAL(5,2),
  excused_percentage DECIMAL(5,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(DISTINCT s.id)::INTEGER as total_sessions,
    COUNT(DISTINCT r.student_id)::INTEGER as total_students,
    CASE 
      WHEN COUNT(r.id) > 0 THEN 
        ROUND((COUNT(CASE WHEN r.status = 'present' THEN 1 END)::DECIMAL / COUNT(r.id)::DECIMAL) * 100, 2)
      ELSE 0::DECIMAL(5,2)
    END as average_attendance,
    CASE 
      WHEN COUNT(r.id) > 0 THEN 
        ROUND((COUNT(CASE WHEN r.status = 'present' THEN 1 END)::DECIMAL / COUNT(r.id)::DECIMAL) * 100, 2)
      ELSE 0::DECIMAL(5,2)
    END as present_percentage,
    CASE 
      WHEN COUNT(r.id) > 0 THEN 
        ROUND((COUNT(CASE WHEN r.status = 'absent' THEN 1 END)::DECIMAL / COUNT(r.id)::DECIMAL) * 100, 2)
      ELSE 0::DECIMAL(5,2)
    END as absent_percentage,
    CASE 
      WHEN COUNT(r.id) > 0 THEN 
        ROUND((COUNT(CASE WHEN r.status = 'late' THEN 1 END)::DECIMAL / COUNT(r.id)::DECIMAL) * 100, 2)
      ELSE 0::DECIMAL(5,2)
    END as late_percentage,
    CASE 
      WHEN COUNT(r.id) > 0 THEN 
        ROUND((COUNT(CASE WHEN r.status = 'excused' THEN 1 END)::DECIMAL / COUNT(r.id)::DECIMAL) * 100, 2)
      ELSE 0::DECIMAL(5,2)
    END as excused_percentage
  FROM attendance_sessions s
  LEFT JOIN attendance_records r ON s.id = r.session_id
  WHERE s.teacher_id = p_teacher_id
    AND (p_class_id IS NULL OR s.class_id = p_class_id)
    AND (p_start_date IS NULL OR s.session_date >= p_start_date)
    AND (p_end_date IS NULL OR s.session_date <= p_end_date);
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing (optional)
-- This will be removed in production
INSERT INTO attendance_sessions (tenant_id, class_id, teacher_id, subject, session_date, session_time, session_type, total_students)
SELECT 
  t.tenant_id,
  c.id as class_id,
  t.id as teacher_id,
  'Mathematics' as subject,
  CURRENT_DATE as session_date,
  '09:00:00' as session_time,
  'regular' as session_type,
  1 as total_students
FROM teachers t
JOIN classes c ON c.teacher_id = t.id
WHERE t.is_class_teacher = true
LIMIT 1;
