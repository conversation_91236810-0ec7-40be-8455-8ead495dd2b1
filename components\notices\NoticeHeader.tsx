import React, { memo } from 'react';
import {
  View,
  Text,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

interface NoticeHeaderProps {
  noticeCount: number;
  searchQuery: string;
}

const NoticeHeader: React.FC<NoticeHeaderProps> = memo(({
  noticeCount,
  searchQuery,
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const getSubtitleText = () => {
    if (searchQuery) {
      return `${noticeCount} ${noticeCount === 1 ? "result" : "results"} for "${searchQuery}"`;
    }
    return `${noticeCount} ${noticeCount === 1 ? "notice" : "notices"}`;
  };

  return (
    <View className="mb-6">
      <Text
        className={`text-2xl font-rubik-bold ${
          isDark ? "text-dark-text" : "text-light-text"
        }`}
      >
        Notices
      </Text>
      <Text
        className={`${
          isDark ? "text-dark-textSecondary" : "text-light-textSecondary"
        }`}
      >
        {getSubtitleText()}
      </Text>
    </View>
  );
});

NoticeHeader.displayName = 'NoticeHeader';

export default NoticeHeader;
