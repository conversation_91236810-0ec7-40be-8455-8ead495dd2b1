import { supabase } from './supabase';

// Global variable to store Clerk auth functions (set by the app)
let clerkAuthFunctions: {
  getToken: (options: { template: string }) => Promise<string | null>;
  isSignedIn: boolean;
  user: any;
} | null = null;

// Function to set Clerk auth functions (called from app initialization)
export const setClerkAuthFunctions = (authFunctions: typeof clerkAuthFunctions) => {
  clerkAuthFunctions = authFunctions;
};

// Helper function to get current authenticated user and teacher info using Clerk
export const getCurrentUserAndTeacher = async (clerkUserId?: string, clerkToken?: string) => {
  try {
    console.log('Getting current user and teacher...');

    // If no parameters provided, try to get them from global Clerk functions
    if (!clerkUserId && !clerkToken && clerkAuthFunctions) {
      console.log('Using global Clerk auth functions...');

      if (!clerkAuthFunctions.isSignedIn || !clerkAuthFunctions.user) {
        throw new Error('User not signed in with Clerk');
      }

      clerkUserId = clerkAuthFunctions.user.id;
      clerkToken = await clerkAuthFunctions.getToken({ template: 'supabase' });

      console.log('Got Clerk info from global functions:', {
        hasUserId: !!clerkUserId,
        hasToken: !!clerkToken
      });
    }

    // We don't need to authenticate with Supabase directly since we're using Clerk
    // Just use the Clerk user ID to query our database
    let userClerkId = clerkUserId;

    if (!userClerkId) {
      console.log('No Clerk user ID provided');
      throw new Error('User not authenticated - no Clerk user ID available');
    }

    // Get user record from our users table using Clerk user ID
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, name, email, role, tenant_id')
      .eq('clerk_user_id', userClerkId)
      .single();

    console.log('User query result:', {
      userData,
      userError,
      clerkUserId: userClerkId
    });

    if (userError) {
      console.error('User query error:', userError);
      
      // If user not found, it might be because the clerk_user_id doesn't match
      // Let's try to find by email as fallback
      if (userError.code === 'PGRST116') { // No rows returned
        console.log('User not found by clerk_user_id, trying by email...');

        // Try to get email from Clerk user data
        const userEmail = clerkAuthFunctions?.user?.emailAddresses?.[0]?.emailAddress;
        if (userEmail) {
          console.log('Trying to find user by email:', userEmail);
          const { data: userByEmail, error: emailError } = await supabase
            .from('users')
            .select('id, name, email, role, tenant_id')
            .eq('email', userEmail)
            .single();

          if (emailError || !userByEmail) {
            throw new Error(`User not found in database. Please contact your administrator. Email: ${userEmail}`);
          }

          console.log('Found user by email:', userByEmail);

          // Update the clerk_user_id to link this user
          const { error: updateError } = await supabase
            .from('users')
            .update({ clerk_user_id: userClerkId })
            .eq('id', userByEmail.id);

          if (updateError) {
            console.error('Error updating clerk_user_id:', updateError);
            // Don't fail completely, just use the found user
          }

          userData = userByEmail;
        } else {
          throw new Error('User not found in database and no email available from Clerk');
        }
      } else {
        throw new Error(`Database error: ${userError.message}`);
      }
    }

    if (!userData) {
      throw new Error('User not found in database');
    }

    // Get teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('id, tenant_id, subject_specialization, years_of_experience')
      .eq('user_id', userData.id)
      .single();

    console.log('Teacher query result:', {
      teacherData,
      teacherError,
      userId: userData.id
    });

    if (teacherError) {
      console.error('Teacher query error:', teacherError);
      
      if (teacherError.code === 'PGRST116') { // No rows returned
        throw new Error('Teacher record not found. Please contact your administrator to set up your teacher profile.');
      } else {
        throw new Error(`Teacher database error: ${teacherError.message}`);
      }
    }

    if (!teacherData) {
      throw new Error('Teacher record not found');
    }

    console.log('Successfully found user and teacher:', {
      user: userData,
      teacher: teacherData
    });

    return {
      user: userData,
      teacher: teacherData
    };

  } catch (error) {
    console.error('Error in getCurrentUserAndTeacher:', error);
    throw error;
  }
};

// Helper function to create a user record if it doesn't exist
export const createUserIfNotExists = async (clerkUserId: string, email: string, name: string) => {
  try {
    console.log('Creating user record:', { clerkUserId, email, name });
    
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_user_id', clerkUserId)
      .single();
    
    if (existingUser) {
      console.log('User already exists:', existingUser);
      return existingUser;
    }

    // Create new user
    const { data: newUser, error } = await supabase
      .from('users')
      .insert({
        clerk_user_id: clerkUserId,
        email,
        name,
        role: 'teacher', // Default role
        tenant_id: 'default-tenant', // You might need to adjust this
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating user:', error);
      throw new Error(`Failed to create user: ${error.message}`);
    }

    console.log('User created successfully:', newUser);
    return newUser;

  } catch (error) {
    console.error('Error in createUserIfNotExists:', error);
    throw error;
  }
};

// Helper function to create a teacher record if it doesn't exist
export const createTeacherIfNotExists = async (userId: string, tenantId: string) => {
  try {
    console.log('Creating teacher record:', { userId, tenantId });
    
    // Check if teacher already exists
    const { data: existingTeacher } = await supabase
      .from('teachers')
      .select('id')
      .eq('user_id', userId)
      .single();
    
    if (existingTeacher) {
      console.log('Teacher already exists:', existingTeacher);
      return existingTeacher;
    }

    // Create new teacher
    const { data: newTeacher, error } = await supabase
      .from('teachers')
      .insert({
        user_id: userId,
        tenant_id: tenantId,
        subject_specialization: 'General',
        years_of_experience: 1,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating teacher:', error);
      throw new Error(`Failed to create teacher: ${error.message}`);
    }

    console.log('Teacher created successfully:', newTeacher);
    return newTeacher;

  } catch (error) {
    console.error('Error in createTeacherIfNotExists:', error);
    throw error;
  }
};

// Helper function to get or create user and teacher
export const getOrCreateUserAndTeacher = async () => {
  try {
    // First try to get existing user and teacher
    try {
      return await getCurrentUserAndTeacher();
    } catch (error) {
      console.log('Failed to get existing user/teacher, attempting to create:', error.message);
    }

    // If that fails, try to create the missing records
    // Use global Clerk auth functions if available
    if (!clerkAuthFunctions || !clerkAuthFunctions.isSignedIn || !clerkAuthFunctions.user) {
      throw new Error('No active Clerk session');
    }

    const user = clerkAuthFunctions.user;
    const userEmail = user.emailAddresses?.[0]?.emailAddress || '<EMAIL>';
    const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown User';

    // Create user if needed
    const userRecord = await createUserIfNotExists(user.id, userEmail, userName);

    // Create teacher if needed
    const teacher = await createTeacherIfNotExists(userRecord.id, userRecord.tenant_id || 'default-tenant');

    return {
      user: userRecord,
      teacher
    };

  } catch (error) {
    console.error('Error in getOrCreateUserAndTeacher:', error);
    throw error;
  }
};
