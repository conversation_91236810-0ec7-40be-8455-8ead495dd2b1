import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { useAssignmentStore } from '@/stores/assignmentStore';

interface Assignment {
  id: string;
  title: string;
  description: string;
  instructions: string;
  due_date: string;
  max_points: number;
  status: 'draft' | 'published' | 'closed';
  attachment_urls: string[];
  allow_late_submissions: boolean;
}

interface Submission {
  id?: string;
  content: string;
  attachment_urls: string[];
  status: 'draft' | 'submitted';
}

interface StudentSubmissionInterfaceProps {
  assignment: Assignment;
  existingSubmission?: Submission;
  studentId: string;
  onSubmissionComplete: () => void;
}

export default function StudentSubmissionInterface({
  assignment,
  existingSubmission,
  studentId,
  onSubmissionComplete,
}: StudentSubmissionInterfaceProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const [submission, setSubmission] = useState<Submission>({
    content: existingSubmission?.content || '',
    attachment_urls: existingSubmission?.attachment_urls || [],
    status: existingSubmission?.status || 'draft',
  });

  const [attachments, setAttachments] = useState<Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    uri: string;
    uploading?: boolean;
  }>>([]);

  const [loading, setLoading] = useState(false);
  const [autoSaving, setAutoSaving] = useState(false);

  // Check if assignment is overdue
  const isOverdue = new Date() > new Date(assignment.due_date);
  const canSubmit = assignment.status === 'published' && (assignment.allow_late_submissions || !isOverdue);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (submission.content.trim() && submission.status === 'draft') {
        handleAutoSave();
      }
    }, 2000);

    return () => clearTimeout(autoSaveTimer);
  }, [submission.content]);

  const handleAutoSave = async () => {
    if (autoSaving) return;
    
    setAutoSaving(true);
    try {
      // Auto-save logic would go here
      // For now, just simulate the save
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setAutoSaving(false);
    }
  };

  const handleFileUpload = async (type: 'document' | 'image') => {
    try {
      let result;
      
      if (type === 'document') {
        result = await DocumentPicker.getDocumentAsync({
          type: '*/*',
          copyToCacheDirectory: true,
          multiple: true,
        });
      } else {
        const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (!permissionResult.granted) {
          Alert.alert('Permission Required', 'Permission to access camera roll is required!');
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.All,
          allowsEditing: false,
          quality: 0.8,
          allowsMultipleSelection: true,
        });
      }

      if (!result.canceled && result.assets) {
        const newAttachments = result.assets.map(asset => ({
          id: Date.now().toString() + Math.random().toString(),
          name: asset.name || `file_${Date.now()}`,
          type: asset.mimeType || 'application/octet-stream',
          size: asset.size || 0,
          uri: asset.uri,
          uploading: true,
        }));

        setAttachments(prev => [...prev, ...newAttachments]);

        // Simulate upload process
        for (const attachment of newAttachments) {
          setTimeout(() => {
            setAttachments(prev => 
              prev.map(a => a.id === attachment.id ? { ...a, uploading: false } : a)
            );
            setSubmission(prev => ({
              ...prev,
              attachment_urls: [...prev.attachment_urls, attachment.uri],
            }));
          }, 2000);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to upload file');
    }
  };

  const removeAttachment = (id: string) => {
    const attachment = attachments.find(a => a.id === id);
    if (attachment) {
      setAttachments(prev => prev.filter(a => a.id !== id));
      setSubmission(prev => ({
        ...prev,
        attachment_urls: prev.attachment_urls.filter(url => url !== attachment.uri),
      }));
    }
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      // Save draft logic
      await new Promise(resolve => setTimeout(resolve, 1000));
      Alert.alert('Draft Saved', 'Your work has been saved as a draft.');
    } catch (error) {
      Alert.alert('Error', 'Failed to save draft');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!submission.content.trim()) {
      Alert.alert('Submission Required', 'Please add some content to your submission.');
      return;
    }

    Alert.alert(
      'Submit Assignment',
      'Are you sure you want to submit this assignment? You won\'t be able to edit it after submission.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Submit',
          onPress: async () => {
            setLoading(true);
            try {
              // Submit logic
              await new Promise(resolve => setTimeout(resolve, 1500));
              setSubmission(prev => ({ ...prev, status: 'submitted' }));
              Alert.alert(
                'Assignment Submitted',
                'Your assignment has been submitted successfully!',
                [{ text: 'OK', onPress: onSubmissionComplete }]
              );
            } catch (error) {
              Alert.alert('Error', 'Failed to submit assignment');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const formatTimeRemaining = () => {
    const now = new Date();
    const dueDate = new Date(assignment.due_date);
    const diffTime = dueDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffTime < 0) {
      return 'Overdue';
    } else if (diffDays === 0) {
      const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
      return diffHours <= 1 ? 'Due in less than 1 hour' : `Due in ${diffHours} hours`;
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else {
      return `Due in ${diffDays} days`;
    }
  };

  const getFileIcon = (type: string) => {
    if (type.includes('image')) return 'image-outline';
    if (type.includes('pdf')) return 'document-text-outline';
    if (type.includes('video')) return 'videocam-outline';
    return 'document-outline';
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
        </TouchableOpacity>
        
        <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Submit Assignment
        </Text>
        
        <View className="flex-row items-center">
          {autoSaving && (
            <View className="flex-row items-center mr-3">
              <ActivityIndicator size="small" color="#3B82F6" />
              <Text className={`ml-2 text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Saving...
              </Text>
            </View>
          )}
          
          <View className={`px-3 py-1 rounded-full ${
            submission.status === 'submitted' 
              ? 'bg-green-100 dark:bg-green-900/30' 
              : 'bg-yellow-100 dark:bg-yellow-900/30'
          }`}>
            <Text className={`font-rubik-medium text-sm ${
              submission.status === 'submitted'
                ? 'text-green-700 dark:text-green-300'
                : 'text-yellow-700 dark:text-yellow-300'
            }`}>
              {submission.status === 'submitted' ? 'Submitted' : 'Draft'}
            </Text>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="p-4 space-y-6">
          {/* Assignment Info */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <Text className={`text-xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {assignment.title}
            </Text>
            <Text className={`font-rubik mb-3 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {assignment.description}
            </Text>
            
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="calendar-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
                <Text className={`ml-2 font-rubik text-sm ${
                  isOverdue ? 'text-red-500' : isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
                }`}>
                  {formatTimeRemaining()}
                </Text>
              </View>
              
              <View className="flex-row items-center">
                <Ionicons name="trophy-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
                <Text className={`ml-2 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {assignment.max_points} points
                </Text>
              </View>
            </View>
          </View>

          {/* Instructions */}
          {assignment.instructions && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Instructions
              </Text>
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {assignment.instructions}
              </Text>
            </View>
          )}

          {/* Assignment Attachments */}
          {assignment.attachment_urls.length > 0 && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Assignment Resources
              </Text>
              {assignment.attachment_urls.map((url, index) => (
                <TouchableOpacity
                  key={index}
                  className="flex-row items-center p-3 rounded-lg bg-gray-100 dark:bg-gray-800 mb-2"
                >
                  <Ionicons name="document-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  <Text className={`ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Resource {index + 1}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Submission Content */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Your Submission *
            </Text>
            
            <TextInput
              value={submission.content}
              onChangeText={(text) => setSubmission(prev => ({ ...prev, content: text }))}
              placeholder="Type your assignment submission here..."
              placeholderTextColor={isDark ? '#666' : '#999'}
              multiline
              numberOfLines={10}
              textAlignVertical="top"
              editable={submission.status !== 'submitted' && canSubmit}
              className={`p-4 rounded-lg border ${
                isDark ? 'bg-dark-background text-dark-text border-dark-border' : 'bg-light-background text-light-text border-light-border'
              } ${submission.status === 'submitted' ? 'opacity-60' : ''}`}
              style={{ minHeight: 200 }}
            />
            
            <Text className={`text-right mt-2 text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {submission.content.length} characters
            </Text>
          </View>

          {/* File Attachments */}
          {submission.status !== 'submitted' && canSubmit && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Attachments (Optional)
              </Text>
              
              <View className="flex-row space-x-3 mb-4">
                <TouchableOpacity
                  onPress={() => handleFileUpload('document')}
                  className={`flex-1 p-4 rounded-xl border-2 border-dashed items-center ${
                    isDark ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background'
                  }`}
                >
                  <Ionicons name="document-attach-outline" size={24} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  <Text className={`font-rubik-medium mt-2 text-center ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Documents
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => handleFileUpload('image')}
                  className={`flex-1 p-4 rounded-xl border-2 border-dashed items-center ${
                    isDark ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background'
                  }`}
                >
                  <Ionicons name="image-outline" size={24} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  <Text className={`font-rubik-medium mt-2 text-center ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Images
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Attachment List */}
              {attachments.map((attachment) => (
                <View key={attachment.id} className="flex-row items-center p-3 rounded-lg bg-gray-100 dark:bg-gray-800 mb-2">
                  <Ionicons name={getFileIcon(attachment.type)} size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  <View className="flex-1 ml-3">
                    <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {attachment.name}
                    </Text>
                    <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                      {formatFileSize(attachment.size)}
                    </Text>
                  </View>
                  
                  {attachment.uploading ? (
                    <ActivityIndicator size="small" color="#3B82F6" />
                  ) : (
                    <TouchableOpacity onPress={() => removeAttachment(attachment.id)} className="p-2">
                      <Ionicons name="trash-outline" size={16} color="#EF4444" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Submission Status */}
          {!canSubmit && (
            <View className={`p-4 rounded-xl ${
              isOverdue ? 'bg-red-50 dark:bg-red-900/20' : 'bg-gray-50 dark:bg-gray-900/20'
            }`}>
              <View className="flex-row items-center mb-2">
                <Ionicons 
                  name={isOverdue ? "time-outline" : "lock-closed-outline"} 
                  size={20} 
                  color={isOverdue ? "#EF4444" : "#6B7280"} 
                />
                <Text className={`ml-2 font-rubik-medium ${
                  isOverdue ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {isOverdue ? 'Assignment Overdue' : 'Assignment Closed'}
                </Text>
              </View>
              <Text className={`font-rubik text-sm ${
                isOverdue ? 'text-red-700 dark:text-red-300' : 'text-gray-700 dark:text-gray-300'
              }`}>
                {isOverdue 
                  ? assignment.allow_late_submissions 
                    ? 'This assignment is overdue but late submissions are allowed.'
                    : 'This assignment is overdue and no longer accepts submissions.'
                  : 'This assignment has been closed by your teacher.'
                }
              </Text>
            </View>
          )}
        </Animated.View>
      </ScrollView>

      {/* Action Buttons */}
      {canSubmit && submission.status !== 'submitted' && (
        <View className="flex-row space-x-3 p-4 border-t border-gray-200 dark:border-gray-700">
          <TouchableOpacity
            onPress={handleSaveDraft}
            disabled={loading}
            className={`flex-1 py-3 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}
          >
            <Text className={`text-center font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {loading ? 'Saving...' : 'Save Draft'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading || !submission.content.trim()}
            className={`flex-1 py-3 rounded-xl ${
              loading || !submission.content.trim() 
                ? 'bg-gray-400' 
                : 'bg-primary-500'
            }`}
          >
            <Text className="text-white text-center font-rubik-medium">
              {loading ? 'Submitting...' : 'Submit Assignment'}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
