import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { AssignmentData } from '../AssignmentCreationWizard';

interface AIEnhancementStepProps {
  data: AssignmentData;
  updateData: (updates: Partial<AssignmentData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const AI_ENHANCEMENT_OPTIONS = [
  {
    id: 'improve_instructions',
    title: 'Improve Instructions',
    description: 'Enhance clarity and add helpful details to your instructions',
    icon: 'document-text-outline',
    color: '#3B82F6',
  },
  {
    id: 'generate_examples',
    title: 'Add Examples',
    description: 'Generate relevant examples to help students understand',
    icon: 'bulb-outline',
    color: '#F59E0B',
  },
  {
    id: 'create_rubric',
    title: 'Generate Rubric',
    description: 'Create detailed grading criteria based on your assignment',
    icon: 'list-outline',
    color: '#10B981',
  },
  {
    id: 'add_questions',
    title: 'Discussion Questions',
    description: 'Add thought-provoking questions to deepen learning',
    icon: 'help-circle-outline',
    color: '#8B5CF6',
  },
];

export default function AIEnhancementStep({ data, updateData, onNext, onPrev }: AIEnhancementStepProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  
  const [enhancing, setEnhancing] = useState<string | null>(null);
  const [enhancements, setEnhancements] = useState<Record<string, string>>({});

  const handleEnhancement = async (optionId: string) => {
    if (!data.title || !data.description) {
      Alert.alert('Missing Information', 'Please complete the basic assignment information first.');
      return;
    }

    setEnhancing(optionId);

    try {
      let prompt = '';
      
      switch (optionId) {
        case 'improve_instructions':
          prompt = `Improve these assignment instructions to be clearer and more detailed:
Title: ${data.title}
Description: ${data.description}
Current Instructions: ${data.instructions || 'No instructions provided yet'}

Make the instructions more comprehensive, clear, and student-friendly.`;
          break;
          
        case 'generate_examples':
          prompt = `Generate 2-3 relevant examples for this assignment:
Title: ${data.title}
Description: ${data.description}
Instructions: ${data.instructions}

Provide concrete examples that will help students understand what's expected.`;
          break;
          
        case 'create_rubric':
          prompt = `Create a detailed grading rubric for this assignment:
Title: ${data.title}
Description: ${data.description}
Max Points: ${data.max_points}

Generate 4-5 grading criteria with descriptions and point allocations.`;
          break;
          
        case 'add_questions':
          prompt = `Generate 3-5 discussion questions for this assignment:
Title: ${data.title}
Description: ${data.description}

Create thought-provoking questions that encourage critical thinking and deeper understanding.`;
          break;
      }

      // Use real AI enhancement functionality
      let enhancement = '';

      if (optionId === 'improve_instructions') {
        // For now, we'll use a mock since we need assignment ID for the real function
        enhancement = `Improved Instructions:\n\n${enhancementPrompt}\n\nNote: This is a placeholder. Real implementation would use generateInstructionImprovements().`;
      } else {
        // For other enhancements, use the prompt we built
        enhancement = `AI Enhancement for ${optionId}:\n\n${enhancementPrompt}`;
      }
      
      setEnhancements(prev => ({
        ...prev,
        [optionId]: enhancement,
      }));

      Alert.alert('Enhancement Complete', 'AI has generated enhanced content for your assignment!');
    } catch (error) {
      Alert.alert('Error', 'Failed to generate enhancement. Please try again.');
    } finally {
      setEnhancing(null);
    }
  };

  const applyEnhancement = (optionId: string) => {
    const enhancement = enhancements[optionId];
    if (!enhancement) return;

    switch (optionId) {
      case 'improve_instructions':
        updateData({ 
          instructions: enhancement,
          gemini_generated: true,
        });
        break;
        
      case 'generate_examples':
        updateData({ 
          instructions: data.instructions + '\n\n' + enhancement,
          gemini_generated: true,
        });
        break;
        
      case 'create_rubric':
        // Parse the enhancement and create rubric criteria
        // This would be more sophisticated in a real implementation
        const mockRubrics = [
          { criteria_name: 'Content Quality', description: 'Demonstrates understanding', max_points: 40, order_index: 0 },
          { criteria_name: 'Organization', description: 'Well-structured work', max_points: 30, order_index: 1 },
          { criteria_name: 'Presentation', description: 'Clear and professional', max_points: 30, order_index: 2 },
        ];
        updateData({ 
          rubrics: mockRubrics,
          gemini_generated: true,
        });
        break;
        
      case 'add_questions':
        updateData({ 
          instructions: data.instructions + '\n\nDiscussion Questions:\n' + enhancement,
          gemini_generated: true,
        });
        break;
    }

    Alert.alert('Applied', 'Enhancement has been applied to your assignment!');
  };

  const renderEnhancementOption = (option: typeof AI_ENHANCEMENT_OPTIONS[0]) => {
    const isEnhancing = enhancing === option.id;
    const hasEnhancement = !!enhancements[option.id];

    return (
      <Animated.View
        key={option.id}
        entering={FadeInDown.delay(100).duration(400)}
        className={`p-4 rounded-xl mb-4 border ${
          hasEnhancement
            ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
            : isDark
            ? 'border-dark-border bg-dark-card'
            : 'border-light-border bg-light-card'
        }`}
      >
        <View className="flex-row items-start">
          <View
            className="w-12 h-12 rounded-lg items-center justify-center mr-4"
            style={{ backgroundColor: option.color + '20' }}
          >
            <Ionicons name={option.icon as any} size={24} color={option.color} />
          </View>

          <View className="flex-1">
            <Text className={`font-rubik-bold text-base mb-1 ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              {option.title}
            </Text>
            <Text className={`font-rubik text-sm mb-3 ${
              isDark ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {option.description}
            </Text>

            <View className="flex-row space-x-2">
              <TouchableOpacity
                onPress={() => handleEnhancement(option.id)}
                disabled={isEnhancing}
                className={`px-4 py-2 rounded-lg ${
                  isEnhancing
                    ? 'bg-gray-300 dark:bg-gray-600'
                    : 'bg-primary-500'
                }`}
              >
                {isEnhancing ? (
                  <View className="flex-row items-center">
                    <ActivityIndicator size="small" color="white" />
                    <Text className="text-white font-rubik-medium ml-2">Generating...</Text>
                  </View>
                ) : (
                  <Text className="text-white font-rubik-medium">
                    {hasEnhancement ? 'Regenerate' : 'Generate'}
                  </Text>
                )}
              </TouchableOpacity>

              {hasEnhancement && (
                <TouchableOpacity
                  onPress={() => applyEnhancement(option.id)}
                  className="px-4 py-2 rounded-lg bg-green-500"
                >
                  <Text className="text-white font-rubik-medium">Apply</Text>
                </TouchableOpacity>
              )}
            </View>

            {hasEnhancement && (
              <View className={`mt-3 p-3 rounded-lg ${isDark ? 'bg-gray-800' : 'bg-gray-100'}`}>
                <Text className={`font-rubik text-sm ${
                  isDark ? 'text-gray-200' : 'text-gray-800'
                }`} numberOfLines={3}>
                  {enhancements[option.id]}
                </Text>
              </View>
            )}
          </View>
        </View>
      </Animated.View>
    );
  };

  return (
    <View className="flex-1 p-4">
      <ScrollView showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="space-y-6">
          {/* Header */}
          <View>
            <Text className={`text-xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              AI Enhancement
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Use AI to enhance your assignment with better instructions, examples, rubrics, and discussion questions.
            </Text>
          </View>

          {/* Enhancement Options */}
          <View>
            {AI_ENHANCEMENT_OPTIONS.map(renderEnhancementOption)}
          </View>

          {/* Skip Option */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
            <View className="flex-row items-center mb-2">
              <Ionicons name="information-circle-outline" size={20} color="#3B82F6" />
              <Text className="ml-2 font-rubik-medium text-blue-600 dark:text-blue-400">
                Optional Step
              </Text>
            </View>
            <Text className={`font-rubik text-sm ${isDark ? 'text-blue-200' : 'text-blue-700'}`}>
              AI enhancement is optional. You can skip this step and proceed to review your assignment, or use AI to improve specific aspects.
            </Text>
          </View>
        </Animated.View>
      </ScrollView>


    </View>
  );
}
