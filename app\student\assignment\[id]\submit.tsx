import StudentSubmissionInterface from '@/components/assignment/StudentSubmissionInterface';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function StudentSubmitAssignment() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { clerkUser } = useSupabaseAuth();
  const { fetchAssignmentById, fetchSubmissions } = useAssignmentStore();
  
  const [assignment, setAssignment] = useState(null);
  const [existingSubmission, setExistingSubmission] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadAssignmentAndSubmission = async () => {
      if (id && clerkUser?.id) {
        // Load assignment
        const assignmentData = await fetchAssignmentById(id);
        setAssignment(assignmentData);

        // Check for existing submission
        // Note: This would need to be implemented to fetch student's submission
        // For now, we'll assume no existing submission
        setExistingSubmission(null);
      }
      setLoading(false);
    };

    loadAssignmentAndSubmission();
  }, [id, clerkUser?.id, fetchAssignmentById]);

  const handleSubmissionComplete = () => {
    router.back();
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
        <ActivityIndicator size="large" color="#2196F3" />
        <Text className="mt-2 text-light-text dark:text-dark-text">Loading assignment...</Text>
      </SafeAreaView>
    );
  }

  if (!assignment || !clerkUser?.id) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
        <Text className="text-light-text dark:text-dark-text">Assignment not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-light-background dark:bg-dark-background">
      <StudentSubmissionInterface
        assignment={assignment}
        existingSubmission={existingSubmission}
        studentId={clerkUser.id}
        onSubmissionComplete={handleSubmissionComplete}
      />
    </SafeAreaView>
  );
}
