import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';

interface Assignment {
  id: string;
  title: string;
  description: string;
  due_date: string;
  max_points: number;
  status: 'draft' | 'published' | 'closed';
  submissions_count?: number;
  graded_count?: number;
  created_at: string;
  gemini_generated: boolean;
}

interface AssignmentCardProps {
  assignment: Assignment;
  onPress: () => void;
  isSelected?: boolean;
  onSelect?: () => void;
  showSelection?: boolean;
}

export default function AssignmentCard({
  assignment,
  onPress,
  isSelected = false,
  onSelect,
  showSelection = false,
}: AssignmentCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300';
      case 'draft':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'closed':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return 'checkmark-circle-outline';
      case 'draft':
        return 'document-outline';
      case 'closed':
        return 'lock-closed-outline';
      default:
        return 'document-outline';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else if (diffDays <= 7) {
      return `Due in ${diffDays} days`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      });
    }
  };

  const getDueDateColor = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'text-red-500';
    } else if (diffDays <= 1) {
      return 'text-orange-500';
    } else if (diffDays <= 3) {
      return 'text-yellow-600';
    } else {
      return isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary';
    }
  };

  const getCompletionRate = () => {
    if (!assignment.submissions_count) return 0;
    const total = assignment.submissions_count;
    const graded = assignment.graded_count || 0;
    return total > 0 ? Math.round((graded / total) * 100) : 0;
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`p-4 rounded-xl mb-3 ${
        isSelected
          ? 'border-2 border-primary-500 bg-primary-50 dark:bg-primary-900/20'
          : isDark
          ? 'bg-dark-card'
          : 'bg-light-card'
      }`}
    >
      <View className="flex-row items-start justify-between">
        {/* Selection Checkbox */}
        {showSelection && (
          <TouchableOpacity
            onPress={onSelect}
            className={`w-6 h-6 rounded border-2 mr-3 items-center justify-center ${
              isSelected
                ? 'bg-primary-500 border-primary-500'
                : isDark
                ? 'border-dark-border'
                : 'border-light-border'
            }`}
          >
            {isSelected && (
              <Ionicons name="checkmark" size={16} color="white" />
            )}
          </TouchableOpacity>
        )}

        {/* Main Content */}
        <View className="flex-1">
          {/* Header */}
          <View className="flex-row items-start justify-between mb-2">
            <View className="flex-1 mr-3">
              <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {assignment.title}
              </Text>
              <Text className={`font-rubik text-sm mt-1 ${
                isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
              }`} numberOfLines={2}>
                {assignment.description}
              </Text>
            </View>

            {/* Status Badge */}
            <View className={`px-3 py-1 rounded-full ${getStatusColor(assignment.status)}`}>
              <View className="flex-row items-center">
                <Ionicons
                  name={getStatusIcon(assignment.status) as any}
                  size={12}
                  color="currentColor"
                />
                <Text className="ml-1 font-rubik-medium text-xs capitalize">
                  {assignment.status}
                </Text>
              </View>
            </View>
          </View>

          {/* Assignment Details */}
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <Ionicons
                name="calendar-outline"
                size={16}
                color={getDueDateColor(assignment.due_date)}
              />
              <Text className={`ml-2 font-rubik text-sm ${getDueDateColor(assignment.due_date)}`}>
                {formatDate(assignment.due_date)}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons
                name="trophy-outline"
                size={16}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`ml-2 font-rubik text-sm ${
                isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
              }`}>
                {assignment.max_points} pts
              </Text>
            </View>
          </View>

          {/* Submission Stats */}
          {assignment.status === 'published' && (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons
                  name="people-outline"
                  size={16}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
                <Text className={`ml-2 font-rubik text-sm ${
                  isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
                }`}>
                  {assignment.submissions_count || 0} submission{(assignment.submissions_count || 0) !== 1 ? 's' : ''}
                </Text>
              </View>

              {(assignment.submissions_count || 0) > 0 && (
                <View className="flex-row items-center">
                  <View className={`w-16 h-2 rounded-full mr-2 ${
                    isDark ? 'bg-dark-background' : 'bg-light-background'
                  }`}>
                    <View
                      className="h-full bg-primary-500 rounded-full"
                      style={{ width: `${getCompletionRate()}%` }}
                    />
                  </View>
                  <Text className={`font-rubik text-sm ${
                    isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
                  }`}>
                    {getCompletionRate()}% graded
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* AI Generated Badge */}
          {assignment.gemini_generated && (
            <View className="flex-row items-center mt-2">
              <Ionicons name="sparkles-outline" size={14} color="#8B5CF6" />
              <Text className="ml-1 font-rubik text-xs text-purple-600 dark:text-purple-400">
                AI Enhanced
              </Text>
            </View>
          )}
        </View>

        {/* Action Button */}
        {!showSelection && (
          <TouchableOpacity
            onPress={(e) => {
              e.stopPropagation();
              // Handle quick actions menu
            }}
            className="p-2 ml-2"
          >
            <Ionicons
              name="ellipsis-vertical"
              size={20}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
}
