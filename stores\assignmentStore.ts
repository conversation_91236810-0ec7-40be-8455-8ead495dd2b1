import {
    generateAssignmentWith<PERSON><PERSON><PERSON>,
    generateGradingFeedback,
    generateInstructionImprovements,
    generateRubricCriteria
} from '@/lib/gemini';
import { supabase } from '@/lib/supabase';
import { create } from 'zustand';

interface Assignment {
  id: string;
  title: string;
  description: string;
  instructions?: string;
  due_date: string;
  max_points: number;
  status: 'draft' | 'published' | 'closed';
  class_id: string;
  tenant_id: string;
  teacher_id: string;
  gemini_generated: boolean;
  attachment_urls?: string[];
  created_at: string;
  updated_at: string;
  submissions_count?: number;
  graded_count?: number;
  number_of_questions?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  clerk_user_id?: string;
}

interface MockTest {
  id: string;
  title: string;
  description: string;
  instructions?: string;
  time_limit: number;
  max_points: number;
  question_type: 'multiple_choice' | 'short_answer' | 'essay' | 'mixed';
  auto_graded: boolean;
  status: 'draft' | 'published' | 'closed';
  start_time?: string;
  end_time?: string;
  attempts_allowed: number;
  show_results_immediately: boolean;
  class_id: string;
  tenant_id: string;
  teacher_id: string;
  gemini_generated: boolean;
  created_at: string;
  updated_at: string;
  attempts_count?: number;
  questions_count?: number;
}

interface AssignmentSubmission {
  id: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  attachment_urls?: string[];
  status: 'draft' | 'submitted' | 'graded' | 'returned';
  grade?: number;
  feedback?: string;
  gemini_feedback?: string;
  submitted_at: string;
  graded_at?: string;
  graded_by?: string;
  student_name?: string;
  student_email?: string;
}

interface MockTestAttempt {
  id: string;
  mock_test_id: string;
  student_id: string;
  attempt_number: number;
  answers?: any;
  score?: number;
  time_taken?: number;
  status: 'in_progress' | 'submitted' | 'graded';
  started_at: string;
  submitted_at?: string;
  graded_at?: string;
  feedback?: string;
  gemini_feedback?: string;
  student_name?: string;
  student_email?: string;
}

interface MockTestQuestion {
  id: string;
  mock_test_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'short_answer' | 'essay';
  options?: any;
  correct_answer?: string;
  points: number;
  order_index: number;
  gemini_generated: boolean;
}

interface AssignmentState {
  assignments: Assignment[];
  mockTests: MockTest[];
  submissions: AssignmentSubmission[];
  attempts: MockTestAttempt[];
  questions: MockTestQuestion[];
  loading: boolean;
  error: string | null;
  selectedClassId: string | null;
  selectedAssignment: Assignment | null;
  selectedMockTest: MockTest | null;

  // Actions
  setSelectedClass: (classId: string) => void;
  setSelectedAssignment: (assignment: Assignment | null) => void;
  setSelectedMockTest: (mockTest: MockTest | null) => void;

  // Assignment actions
  createAssignment: (data: Partial<Assignment>) => Promise<Assignment | null>;
  createAssignmentWithRubrics: (data: Partial<Assignment>, rubrics?: {
    criteria_name: string;
    description: string;
    max_points: number;
    order_index: number;
  }[]) => Promise<Assignment | null>;
  updateAssignment: (id: string, data: Partial<Assignment>) => Promise<void>;
  deleteAssignment: (id: string) => Promise<void>;
  publishAssignment: (id: string) => Promise<void>;
  closeAssignment: (id: string) => Promise<void>;
  fetchAssignments: (classId: string) => Promise<void>;
  fetchAssignmentById: (id: string) => Promise<Assignment | null>;

  // Mock test actions
  createMockTest: (data: Partial<MockTest>) => Promise<MockTest | null>;
  updateMockTest: (id: string, data: Partial<MockTest>) => Promise<void>;
  deleteMockTest: (id: string) => Promise<void>;
  publishMockTest: (id: string) => Promise<void>;
  closeMockTest: (id: string) => Promise<void>;
  fetchMockTests: (classId: string) => Promise<void>;
  fetchMockTestById: (id: string) => Promise<MockTest | null>;

  // Submission actions
  fetchSubmissions: (assignmentId: string) => Promise<void>;
  createSubmission: (assignmentId: string, studentId: string, data: {
    content?: string;
    attachment_urls?: string[];
    status?: 'draft' | 'submitted';
  }) => Promise<AssignmentSubmission | null>;
  updateSubmission: (submissionId: string, data: {
    content?: string;
    attachment_urls?: string[];
    status?: 'draft' | 'submitted';
  }) => Promise<void>;
  gradeSubmission: (submissionId: string, grade: number, feedback?: string) => Promise<void>;
  fetchSubmissionById: (submissionId: string) => Promise<AssignmentSubmission | null>;

  // Attempt actions
  fetchAttempts: (mockTestId: string) => Promise<void>;
  gradeAttempt: (attemptId: string, score: number, feedback?: string) => Promise<void>;

  // Question actions
  fetchQuestions: (mockTestId: string) => Promise<void>;
  createQuestion: (data: Partial<MockTestQuestion>) => Promise<void>;
  updateQuestion: (id: string, data: Partial<MockTestQuestion>) => Promise<void>;
  deleteQuestion: (id: string) => Promise<void>;

  // AI actions
  generateAssignmentWithAI: (prompt: string, classId: string, numberOfQuestions: number, difficulty: string, clerkUserId: string) => Promise<Assignment | null>;
  generateMockTestWithAI: (prompt: string, classId: string) => Promise<MockTest | null>;
  generateQuestionsWithAI: (mockTestId: string, count: number, topic: string) => Promise<void>;
  generateAIGradingFeedback: (submissionId: string, assignmentId: string) => Promise<void>;
  generateInstructionImprovements: (assignmentId: string) => Promise<string>;
  generateRubricCriteria: (assignmentId: string) => Promise<void>;

  // Export actions
  exportAssignment: (assignmentId: string, format?: 'csv' | 'json') => Promise<void>;
  exportMultipleAssignments: (assignmentIds: string[], format?: 'csv' | 'json') => Promise<void>;
  exportClassSummary: (classId: string, format?: 'csv' | 'json') => Promise<void>;

  // Bulk operations
  bulkUpdateAssignments: (assignmentIds: string[], updates: Partial<Assignment>) => Promise<void>;
  bulkDeleteAssignments: (assignmentIds: string[]) => Promise<void>;
  bulkPublishAssignments: (assignmentIds: string[]) => Promise<void>;
  bulkCloseAssignments: (assignmentIds: string[]) => Promise<void>;

  // Utility actions
  clearError: () => void;
  reset: () => void;
}

export const useAssignmentStore = create<AssignmentState>((set, get) => ({
  assignments: [],
  mockTests: [],
  submissions: [],
  attempts: [],
  questions: [],
  loading: false,
  error: null,
  selectedClassId: null,
  selectedAssignment: null,
  selectedMockTest: null,

  setSelectedClass: (classId) => {
    set({ selectedClassId: classId });
  },

  setSelectedAssignment: (assignment) => {
    set({ selectedAssignment: assignment });
  },

  setSelectedMockTest: (mockTest) => {
    set({ selectedMockTest: mockTest });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      assignments: [],
      mockTests: [],
      submissions: [],
      attempts: [],
      questions: [],
      loading: false,
      error: null,
      selectedClassId: null,
      selectedAssignment: null,
      selectedMockTest: null,
    });
  },

  createAssignment: async (data) => {
    try {
      set({ loading: true, error: null });

      // Import the helper function dynamically to avoid import issues
      const { getCurrentUserAndTeacher } = await import('@/lib/authHelpers');

      // Get current user and teacher info using our fixed auth helper
      const { user, teacher } = await getCurrentUserAndTeacher();

      if (!user || !teacher) {
        throw new Error('User or teacher not found');
      }

      const assignmentData = {
        ...data,
        teacher_id: teacher.id,
        tenant_id: teacher.tenant_id,
        status: data.status || 'draft',
        max_points: data.max_points || 100,
        number_of_questions: data.number_of_questions || 5,
        difficulty: data.difficulty || 'medium',
      };

      console.log('Creating assignment with data:', assignmentData);

      const { data: newAssignment, error } = await supabase
        .from('assignments')
        .insert([assignmentData])
        .select()
        .single();

      if (error) {
        console.error('Error creating assignment:', error);
        throw new Error(`Failed to create assignment: ${error.message}`);
      }

      console.log('Created new assignment:', newAssignment);

      // Update local state
      set((state) => ({
        assignments: [newAssignment, ...state.assignments],
        loading: false,
      }));

      return newAssignment;
    } catch (error) {
      console.error('Error in createAssignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      set({ error: errorMessage, loading: false });
      throw new Error(`Failed to create assignment: ${errorMessage}`);
    }
  },

  createAssignmentWithRubrics: async (data, rubrics = []) => {
    try {
      set({ loading: true, error: null });

      // First create the assignment
      const assignment = await get().createAssignment(data);

      if (!assignment || rubrics.length === 0) {
        return assignment;
      }

      // Then create the rubrics
      const rubricData = rubrics.map(rubric => ({
        ...rubric,
        assignment_id: assignment.id,
        tenant_id: assignment.tenant_id,
      }));

      const { error: rubricError } = await supabase
        .from('assignment_rubrics')
        .insert(rubricData);

      if (rubricError) {
        console.error('Error creating rubrics:', rubricError);
        // Don't fail the entire operation if rubrics fail
      }

      set({ loading: false });
      return assignment;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  updateAssignment: async (id, data) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('assignments')
        .update(data)
        .eq('id', id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        assignments: state.assignments.map(assignment =>
          assignment.id === id ? { ...assignment, ...data } : assignment
        ),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  deleteAssignment: async (id) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('assignments')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        assignments: state.assignments.filter(assignment => assignment.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  publishAssignment: async (id) => {
    try {
      await get().updateAssignment(id, { status: 'published' });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  closeAssignment: async (id) => {
    try {
      await get().updateAssignment(id, { status: 'closed' });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  fetchAssignmentById: async (id) => {
    try {
      set({ loading: true, error: null });

      const { data, error } = await supabase
        .from('assignments')
        .select(`
          *,
          submissions:assignment_submissions(count)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      const assignment = {
        ...data,
        submissions_count: data.submissions?.[0]?.count || 0,
      };

      set({ loading: false });
      return assignment;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  createMockTest: async (data) => {
    try {
      set({ loading: true, error: null });

      // Get current teacher info
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) throw new Error('User not authenticated');

      // Get teacher ID from users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('clerk_user_id', session.user.id)
        .single();

      if (userError || !userData) throw new Error('Teacher not found');

      // Get teacher record
      const { data: teacherData, error: teacherError } = await supabase
        .from('teachers')
        .select('id, tenant_id')
        .eq('user_id', userData.id)
        .single();

      if (teacherError || !teacherData) throw new Error('Teacher record not found');

      const mockTestData = {
        ...data,
        teacher_id: teacherData.id,
        tenant_id: teacherData.tenant_id,
        status: data.status || 'draft',
        max_points: data.max_points || 100,
        attempts_allowed: data.attempts_allowed || 1,
        show_results_immediately: data.show_results_immediately || false,
      };

      const { data: newMockTest, error } = await supabase
        .from('mock_tests')
        .insert([mockTestData])
        .select()
        .single();

      if (error) throw error;

      // Update local state
      set((state) => ({
        mockTests: [newMockTest, ...state.mockTests],
        loading: false,
      }));

      return newMockTest;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  updateMockTest: async (id, data) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('mock_tests')
        .update(data)
        .eq('id', id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        mockTests: state.mockTests.map(mockTest =>
          mockTest.id === id ? { ...mockTest, ...data } : mockTest
        ),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  deleteMockTest: async (id) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('mock_tests')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        mockTests: state.mockTests.filter(mockTest => mockTest.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  publishMockTest: async (id) => {
    try {
      await get().updateMockTest(id, { status: 'published' });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  closeMockTest: async (id) => {
    try {
      await get().updateMockTest(id, { status: 'closed' });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  fetchMockTestById: async (id) => {
    try {
      set({ loading: true, error: null });

      const { data, error } = await supabase
        .from('mock_tests')
        .select(`
          *,
          attempts:mock_test_attempts(count),
          questions:mock_test_questions(count)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      const mockTest = {
        ...data,
        attempts_count: data.attempts?.[0]?.count || 0,
        questions_count: data.questions?.[0]?.count || 0,
      };

      set({ loading: false });
      return mockTest;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  fetchAssignments: async (classId) => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('assignments')
        .select(`
          *,
          submissions:assignment_submissions(count),
          graded_submissions:assignment_submissions!inner(count)
        `)
        .eq('class_id', classId)
        .eq('graded_submissions.status', 'graded')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const assignments = data.map(assignment => ({
        ...assignment,
        submissions_count: assignment.submissions?.[0]?.count || 0,
        graded_count: assignment.graded_submissions?.[0]?.count || 0,
      }));

      set({ assignments, loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  fetchMockTests: async (classId) => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('mock_tests')
        .select(`
          *,
          attempts:mock_test_attempts(count),
          questions:mock_test_questions(count)
        `)
        .eq('class_id', classId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const mockTests = data.map(mockTest => ({
        ...mockTest,
        attempts_count: mockTest.attempts?.[0]?.count || 0,
        questions_count: mockTest.questions?.[0]?.count || 0,
      }));

      set({ mockTests, loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  // Submission management
  fetchSubmissions: async (assignmentId) => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('assignment_submissions')
        .select(`
          *,
          student:students(name, email)
        `)
        .eq('assignment_id', assignmentId)
        .order('submitted_at', { ascending: false });

      if (error) throw error;

      const submissions = data.map(submission => ({
        ...submission,
        student_name: submission.student?.name,
        student_email: submission.student?.email,
      }));

      set({ submissions, loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  createSubmission: async (assignmentId, studentId, data) => {
    try {
      set({ loading: true, error: null });

      const { data: submission, error } = await supabase
        .from('assignment_submissions')
        .insert({
          assignment_id: assignmentId,
          student_id: studentId,
          content: data.content,
          attachment_urls: data.attachment_urls || [],
          status: data.status || 'draft',
          tenant_id: get().assignments[0]?.tenant_id, // Get tenant_id from existing assignments
        })
        .select(`
          *,
          student:students(name, email)
        `)
        .single();

      if (error) throw error;

      const newSubmission = {
        ...submission,
        student_name: submission.student?.name,
        student_email: submission.student?.email,
      };

      set((state) => ({
        submissions: [...state.submissions, newSubmission],
        loading: false,
      }));

      return newSubmission;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  updateSubmission: async (submissionId, data) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('assignment_submissions')
        .update({
          content: data.content,
          attachment_urls: data.attachment_urls,
          status: data.status,
          submitted_at: data.status === 'submitted' ? new Date().toISOString() : undefined,
        })
        .eq('id', submissionId);

      if (error) throw error;

      // Update local state
      set((state) => ({
        submissions: state.submissions.map(submission =>
          submission.id === submissionId
            ? { ...submission, ...data }
            : submission
        ),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  gradeSubmission: async (submissionId, grade, feedback) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('assignment_submissions')
        .update({
          grade,
          feedback,
          status: 'graded',
          graded_at: new Date().toISOString(),
        })
        .eq('id', submissionId);

      if (error) throw error;

      // Update local state
      set((state) => ({
        submissions: state.submissions.map(submission =>
          submission.id === submissionId
            ? { ...submission, grade, feedback, status: 'graded' as const, graded_at: new Date().toISOString() }
            : submission
        ),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  fetchSubmissionById: async (submissionId) => {
    try {
      set({ loading: true, error: null });

      const { data, error } = await supabase
        .from('assignment_submissions')
        .select(`
          *,
          student:students(name, email),
          assignment:assignments(title, description, max_points)
        `)
        .eq('id', submissionId)
        .single();

      if (error) throw error;

      const submission = {
        ...data,
        student_name: data.student?.name,
        student_email: data.student?.email,
      };

      set({ loading: false });
      return submission;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  // Mock test attempt management
  fetchAttempts: async (mockTestId) => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('mock_test_attempts')
        .select(`
          *,
          student:students(name, email)
        `)
        .eq('mock_test_id', mockTestId)
        .order('started_at', { ascending: false });

      if (error) throw error;

      const attempts = data.map(attempt => ({
        ...attempt,
        student_name: attempt.student?.name,
        student_email: attempt.student?.email,
      }));

      set({ attempts, loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  gradeAttempt: async (attemptId, score, feedback) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('mock_test_attempts')
        .update({
          score,
          feedback,
          status: 'graded',
          graded_at: new Date().toISOString(),
        })
        .eq('id', attemptId);

      if (error) throw error;

      // Update local state
      set((state) => ({
        attempts: state.attempts.map(attempt =>
          attempt.id === attemptId
            ? { ...attempt, score, feedback, status: 'graded' as const }
            : attempt
        ),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  // Question management
  fetchQuestions: async (mockTestId) => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('mock_test_questions')
        .select('*')
        .eq('mock_test_id', mockTestId)
        .order('order_index', { ascending: true });

      if (error) throw error;
      set({ questions: data, loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  createQuestion: async (data) => {
    try {
      set({ loading: true, error: null });

      // Get the current mock test to extract tenant_id
      const { data: mockTestData, error: mockTestError } = await supabase
        .from('mock_tests')
        .select('tenant_id')
        .eq('id', data.mock_test_id)
        .single();

      if (mockTestError || !mockTestData) throw new Error('Mock test not found');

      const questionData = {
        ...data,
        tenant_id: mockTestData.tenant_id,
      };

      const { error } = await supabase
        .from('mock_test_questions')
        .insert([questionData]);

      if (error) throw error;

      // Refresh questions
      if (data.mock_test_id) {
        await get().fetchQuestions(data.mock_test_id);
      }
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  updateQuestion: async (id, data) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('mock_test_questions')
        .update(data)
        .eq('id', id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        questions: state.questions.map(question =>
          question.id === id ? { ...question, ...data } : question
        ),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  deleteQuestion: async (id) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('mock_test_questions')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        questions: state.questions.filter(question => question.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  // AI Integration placeholders (to be implemented with actual AI service)
  generateAssignmentWithAI: async (prompt: string, classId: string, numberOfQuestions: number, difficulty: string, clerkUserId: string) => {
    try {
      set({ loading: true, error: null });
      console.log("Starting AI generation with prompt:", prompt);

      // Generate assignment content using Gemini
      const generated = await generateAssignmentWithGemini(prompt);
      console.log("Generated content:", generated);

      if (!generated.title) {
        throw new Error("Failed to generate a valid assignment title");
      }

      // Create the assignment using the generated content
      const assignment = await get().createAssignment({
        title: generated.title,
        description: generated.description,
        instructions: generated.instructions,
        max_points: generated.maxPoints,
        class_id: classId,
        gemini_generated: true,
        status: 'draft', // Always create as draft to allow review
        number_of_questions: numberOfQuestions,
        difficulty: difficulty,
        clerk_user_id: clerkUserId,
      });

      console.log("Created assignment:", assignment);
      set({ loading: false });
      return assignment;
    } catch (error) {
      console.error("Error in generateAssignmentWithAI:", error);
      set({ error: error instanceof Error ? error.message : "Unknown error", loading: false });
      throw error; // Re-throw to handle in the UI
    }
  },

  generateMockTestWithAI: async (prompt, classId) => {
    try {
      set({ loading: true, error: null });

      // TODO: Implement actual AI integration
      // For now, create a basic mock test
      const mockTestData = {
        title: `AI Generated: ${prompt}`,
        description: `This mock test was generated based on: ${prompt}`,
        class_id: classId,
        time_limit: 60,
        gemini_generated: true,
        status: 'draft' as const,
      };

      const result = await get().createMockTest(mockTestData);
      return result;
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      return null;
    }
  },

  generateQuestionsWithAI: async (mockTestId, count, topic) => {
    try {
      set({ loading: true, error: null });

      // TODO: Implement actual AI integration
      // For now, create sample questions
      const questions = Array.from({ length: count }, (_, index) => ({
        mock_test_id: mockTestId,
        question_text: `AI Generated question ${index + 1} about ${topic}`,
        question_type: 'multiple_choice' as const,
        options: {
          a: 'Option A',
          b: 'Option B',
          c: 'Option C',
          d: 'Option D',
        },
        correct_answer: 'a',
        points: 1,
        order_index: index + 1,
        gemini_generated: true,
      }));

      for (const question of questions) {
        await get().createQuestion(question);
      }
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
    }
  },

  // Export functionality
  exportAssignment: async (assignmentId: string, format: 'csv' | 'json' = 'csv') => {
    try {
      set({ loading: true, error: null });

      // Import export utilities dynamically
      const { exportToFile, generateGradeReport } = await import('@/lib/exportUtils');

      // Get assignment details
      const assignment = await get().fetchAssignmentById(assignmentId);
      if (!assignment) {
        throw new Error('Assignment not found');
      }

      // Get submissions for this assignment
      await get().fetchSubmissions(assignmentId);
      const submissions = get().submissions;

      // Generate export data
      const exportData = generateGradeReport(assignment, submissions);

      // Export to file
      await exportToFile(exportData, format);

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  exportMultipleAssignments: async (assignmentIds: string[], format: 'csv' | 'json' = 'csv') => {
    try {
      set({ loading: true, error: null });

      // Import export utilities dynamically
      const { exportMultipleAssignments } = await import('@/lib/exportUtils');

      // Get all assignments
      const assignments = get().assignments.filter(a => assignmentIds.includes(a.id));
      if (assignments.length === 0) {
        throw new Error('No assignments found');
      }

      // Get all submissions for these assignments
      const allSubmissions: any[] = [];
      for (const assignmentId of assignmentIds) {
        await get().fetchSubmissions(assignmentId);
        const submissions = get().submissions.map(s => ({ ...s, assignment_id: assignmentId }));
        allSubmissions.push(...submissions);
      }

      // Export multiple assignments
      await exportMultipleAssignments(assignments, allSubmissions, format);

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  exportClassSummary: async (classId: string, format: 'csv' | 'json' = 'csv') => {
    try {
      set({ loading: true, error: null });

      // Import export utilities dynamically
      const { exportToFile, generateSummaryReport } = await import('@/lib/exportUtils');

      // Get all assignments for the class
      await get().fetchAssignments(classId);
      const assignments = get().assignments;

      // Get all submissions for all assignments
      const allSubmissions: any[] = [];
      for (const assignment of assignments) {
        await get().fetchSubmissions(assignment.id);
        const submissions = get().submissions.map(s => ({ ...s, assignment_id: assignment.id }));
        allSubmissions.push(...submissions);
      }

      // Generate summary report
      const summaryData = generateSummaryReport(assignments, allSubmissions);

      // Export summary
      const exportData = {
        assignment: {
          id: 'class-summary',
          title: `Class Summary - ${assignments.length} Assignments`,
          description: 'Summary report for all assignments in class',
          max_points: 0,
          due_date: '',
          status: 'summary',
          created_at: new Date().toISOString(),
        },
        submissions: [],
        analytics: summaryData.summary,
        detailed_assignments: summaryData.assignments,
      };

      await exportToFile(exportData as any, format, `class_summary_${new Date().toISOString().split('T')[0]}.${format}`);

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  // Bulk operations
  bulkUpdateAssignments: async (assignmentIds: string[], updates: Partial<Assignment>) => {
    try {
      set({ loading: true, error: null });

      for (const assignmentId of assignmentIds) {
        await get().updateAssignment(assignmentId, updates);
      }

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  bulkDeleteAssignments: async (assignmentIds: string[]) => {
    try {
      set({ loading: true, error: null });

      for (const assignmentId of assignmentIds) {
        await get().deleteAssignment(assignmentId);
      }

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  bulkPublishAssignments: async (assignmentIds: string[]) => {
    try {
      set({ loading: true, error: null });

      for (const assignmentId of assignmentIds) {
        await get().publishAssignment(assignmentId);
      }

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  bulkCloseAssignments: async (assignmentIds: string[]) => {
    try {
      set({ loading: true, error: null });

      for (const assignmentId of assignmentIds) {
        await get().closeAssignment(assignmentId);
      }

      set({ loading: false });
    } catch (error) {
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  // AI-Assisted Grading Implementation
  generateAIGradingFeedback: async (submissionId: string, assignmentId: string) => {
    try {
      set({ loading: true, error: null });

      // Fetch submission and assignment details
      const { data: submission, error: submissionError } = await supabase
        .from('assignment_submissions')
        .select('*')
        .eq('id', submissionId)
        .single();

      if (submissionError || !submission) {
        throw new Error('Submission not found');
      }

      const { data: assignment, error: assignmentError } = await supabase
        .from('assignments')
        .select(`
          *,
          rubrics:assignment_rubrics(*)
        `)
        .eq('id', assignmentId)
        .single();

      if (assignmentError || !assignment) {
        throw new Error('Assignment not found');
      }

      // Generate AI feedback
      const feedback = await generateGradingFeedback(
        submission.content || '',
        assignment.instructions || '',
        assignment.max_points,
        assignment.rubrics
      );

      // Update submission with AI feedback
      const { error: updateError } = await supabase
        .from('assignment_submissions')
        .update({
          gemini_feedback: feedback.feedback,
          grade: feedback.score,
        })
        .eq('id', submissionId);

      if (updateError) {
        throw new Error('Failed to save AI feedback');
      }

      // Update local state
      set((state) => ({
        submissions: state.submissions.map(sub =>
          sub.id === submissionId
            ? { ...sub, gemini_feedback: feedback.feedback, grade: feedback.score }
            : sub
        ),
        loading: false,
      }));

    } catch (error) {
      console.error('Error generating AI grading feedback:', error);
      set({ error: (error as Error).message, loading: false });
    }
  },

  generateInstructionImprovements: async (assignmentId: string) => {
    try {
      set({ loading: true, error: null });

      const { data: assignment, error } = await supabase
        .from('assignments')
        .select('*')
        .eq('id', assignmentId)
        .single();

      if (error || !assignment) {
        throw new Error('Assignment not found');
      }

      const improvedInstructions = await generateInstructionImprovements(
        assignment.instructions || '',
        assignment.title,
        'General', // You might want to add subject field to assignments
        'High School' // You might want to add grade level field
      );

      set({ loading: false });
      return improvedInstructions;
    } catch (error) {
      console.error('Error generating instruction improvements:', error);
      set({ error: (error as Error).message, loading: false });
      throw error;
    }
  },

  generateRubricCriteria: async (assignmentId: string) => {
    try {
      set({ loading: true, error: null });

      const { data: assignment, error } = await supabase
        .from('assignments')
        .select('*')
        .eq('id', assignmentId)
        .single();

      if (error || !assignment) {
        throw new Error('Assignment not found');
      }

      const criteria = await generateRubricCriteria(
        assignment.title,
        assignment.instructions || '',
        assignment.max_points,
        'General' // You might want to add subject field
      );

      // Save generated rubrics to database
      const rubricData = criteria.map((criterion, index) => ({
        assignment_id: assignmentId,
        tenant_id: assignment.tenant_id,
        criteria_name: criterion.criteria_name,
        description: criterion.description,
        max_points: criterion.max_points,
        order_index: index,
      }));

      const { error: insertError } = await supabase
        .from('assignment_rubrics')
        .insert(rubricData);

      if (insertError) {
        throw new Error('Failed to save generated rubrics');
      }

      set({ loading: false });
    } catch (error) {
      console.error('Error generating rubric criteria:', error);
      set({ error: (error as Error).message, loading: false });
    }
  },
}));
