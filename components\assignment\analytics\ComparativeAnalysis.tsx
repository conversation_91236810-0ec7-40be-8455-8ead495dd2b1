import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface Assignment {
  id: string;
  title: string;
  max_points: number;
  due_date: string;
}

interface ComparisonData {
  currentAssignment: {
    averageGrade: number;
    completionRate: number;
    submissionCount: number;
  };
  classAverage: {
    averageGrade: number;
    completionRate: number;
    submissionCount: number;
  };
  previousAssignments: {
    id: string;
    title: string;
    averageGrade: number;
    completionRate: number;
  }[];
  performanceRanking: number;
  totalAssignments: number;
}

interface ComparativeAnalysisProps {
  assignment: Assignment;
  classId: string;
}

export default function ComparativeAnalysis({ assignment, classId }: ComparativeAnalysisProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedComparison, setSelectedComparison] = useState<'class' | 'previous'>('class');

  useEffect(() => {
    fetchComparisonData();
  }, [assignment.id, classId]);

  const fetchComparisonData = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockData: ComparisonData = {
        currentAssignment: {
          averageGrade: 78.5,
          completionRate: 85.7,
          submissionCount: 31,
        },
        classAverage: {
          averageGrade: 82.3,
          completionRate: 88.2,
          submissionCount: 32,
        },
        previousAssignments: [
          { id: '1', title: 'Essay Assignment', averageGrade: 85.2, completionRate: 92.1 },
          { id: '2', title: 'Research Project', averageGrade: 79.8, completionRate: 87.5 },
          { id: '3', title: 'Lab Report', averageGrade: 76.4, completionRate: 83.3 },
          { id: '4', title: 'Presentation', averageGrade: 88.1, completionRate: 95.2 },
        ],
        performanceRanking: 3,
        totalAssignments: 5,
      };

      setComparisonData(mockData);
    } catch (error) {
      console.error('Error fetching comparison data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderComparisonCard = (
    title: string,
    current: number,
    comparison: number,
    unit: string = '',
    isPercentage: boolean = false
  ) => {
    const difference = current - comparison;
    const isPositive = difference > 0;
    const percentageDiff = Math.abs((difference / comparison) * 100);

    return (
      <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {title}
        </Text>
        
        <View className="flex-row items-end justify-between">
          <View>
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {current.toFixed(1)}{unit}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Current
            </Text>
          </View>
          
          <View className="items-end">
            <View className={`flex-row items-center px-2 py-1 rounded ${
              isPositive ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
            }`}>
              <Ionicons 
                name={isPositive ? 'trending-up' : 'trending-down'} 
                size={14} 
                color={isPositive ? '#10B981' : '#EF4444'} 
              />
              <Text className={`ml-1 font-rubik-bold text-sm ${
                isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
              }`}>
                {percentageDiff.toFixed(1)}%
              </Text>
            </View>
            <Text className={`font-rubik text-xs mt-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              vs {comparison.toFixed(1)}{unit}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderClassComparison = () => {
    if (!comparisonData) return null;

    return (
      <View className="space-y-4">
        <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          vs Class Average
        </Text>

        <View className="grid grid-cols-1 gap-4">
          {renderComparisonCard(
            'Average Grade',
            comparisonData.currentAssignment.averageGrade,
            comparisonData.classAverage.averageGrade
          )}
          
          {renderComparisonCard(
            'Completion Rate',
            comparisonData.currentAssignment.completionRate,
            comparisonData.classAverage.completionRate,
            '%'
          )}
          
          {renderComparisonCard(
            'Submissions',
            comparisonData.currentAssignment.submissionCount,
            comparisonData.classAverage.submissionCount
          )}
        </View>

        {/* Performance Ranking */}
        <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Assignment Ranking
          </Text>
          
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className={`w-12 h-12 rounded-full items-center justify-center ${
                comparisonData.performanceRanking <= 2 ? 'bg-green-500' :
                comparisonData.performanceRanking <= 4 ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                <Text className="text-white font-rubik-bold text-lg">
                  #{comparisonData.performanceRanking}
                </Text>
              </View>
              <View className="ml-3">
                <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Rank {comparisonData.performanceRanking} of {comparisonData.totalAssignments}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  Based on overall performance
                </Text>
              </View>
            </View>
            
            <Ionicons 
              name={comparisonData.performanceRanking <= 2 ? 'trophy' : 'medal'} 
              size={24} 
              color={comparisonData.performanceRanking <= 2 ? '#F59E0B' : '#6B7280'} 
            />
          </View>
        </View>
      </View>
    );
  };

  const renderPreviousComparison = () => {
    if (!comparisonData) return null;

    return (
      <View className="space-y-4">
        <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          vs Previous Assignments
        </Text>

        <View className="space-y-3">
          {comparisonData.previousAssignments.map((prevAssignment, index) => (
            <View key={prevAssignment.id} className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <View className="flex-row items-center justify-between mb-2">
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {prevAssignment.title}
                </Text>
                <View className="flex-row space-x-2">
                  <View className={`px-2 py-1 rounded ${
                    comparisonData.currentAssignment.averageGrade > prevAssignment.averageGrade
                      ? 'bg-green-100 dark:bg-green-900/30'
                      : 'bg-red-100 dark:bg-red-900/30'
                  }`}>
                    <Text className={`font-rubik-bold text-xs ${
                      comparisonData.currentAssignment.averageGrade > prevAssignment.averageGrade
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      Grade
                    </Text>
                  </View>
                  <View className={`px-2 py-1 rounded ${
                    comparisonData.currentAssignment.completionRate > prevAssignment.completionRate
                      ? 'bg-green-100 dark:bg-green-900/30'
                      : 'bg-red-100 dark:bg-red-900/30'
                  }`}>
                    <Text className={`font-rubik-bold text-xs ${
                      comparisonData.currentAssignment.completionRate > prevAssignment.completionRate
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      Rate
                    </Text>
                  </View>
                </View>
              </View>
              
              <View className="flex-row justify-between">
                <View>
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                    Avg Grade: {prevAssignment.averageGrade.toFixed(1)}
                  </Text>
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                    Completion: {prevAssignment.completionRate.toFixed(1)}%
                  </Text>
                </View>
                <View className="items-end">
                  <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {(comparisonData.currentAssignment.averageGrade - prevAssignment.averageGrade).toFixed(1)}
                  </Text>
                  <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {(comparisonData.currentAssignment.completionRate - prevAssignment.completionRate).toFixed(1)}%
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View className="items-center justify-center py-8">
        <ActivityIndicator size="large" color="#007AFF" />
        <Text className={`mt-4 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Loading comparison data...
        </Text>
      </View>
    );
  }

  return (
    <View className="space-y-6">
      {/* Toggle Buttons */}
      <View className="flex-row space-x-2">
        <TouchableOpacity
          onPress={() => setSelectedComparison('class')}
          className={`flex-1 py-3 px-4 rounded-xl ${
            selectedComparison === 'class'
              ? 'bg-primary-500'
              : isDark
              ? 'bg-dark-card'
              : 'bg-light-card'
          }`}
        >
          <Text
            className={`text-center font-rubik-medium ${
              selectedComparison === 'class'
                ? 'text-white'
                : isDark
                ? 'text-dark-text'
                : 'text-light-text'
            }`}
          >
            Class Average
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setSelectedComparison('previous')}
          className={`flex-1 py-3 px-4 rounded-xl ${
            selectedComparison === 'previous'
              ? 'bg-primary-500'
              : isDark
              ? 'bg-dark-card'
              : 'bg-light-card'
          }`}
        >
          <Text
            className={`text-center font-rubik-medium ${
              selectedComparison === 'previous'
                ? 'text-white'
                : isDark
                ? 'text-dark-text'
                : 'text-light-text'
            }`}
          >
            Previous Assignments
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <Animated.View entering={FadeInDown.duration(400)}>
        {selectedComparison === 'class' ? renderClassComparison() : renderPreviousComparison()}
      </Animated.View>
    </View>
  );
}
