import { CACHE_CONFIG, cacheManager } from '@/lib/cache';
import { supabaseAdmin } from '@/lib/supabase';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types
export interface AttendanceSession {
  id: string;
  tenant_id: string;
  class_id: string;
  teacher_id: string;
  subject?: string;
  session_date: string;
  session_time: string;
  session_type: 'regular' | 'exam' | 'lab' | 'assembly';
  status: 'active' | 'completed' | 'cancelled';
  total_students: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  excused_count: number;
  notes?: string;
  created_at: string;
  updated_at?: string;
  class?: {
    id: string;
    name: string;
    grade?: string;
    section?: string;
  };
}

export interface AttendanceRecord {
  id: string;
  tenant_id: string;
  session_id: string;
  student_id: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  marked_at: string;
  marked_by: string;
  notes?: string;
  created_at: string;
  updated_at?: string;
  student?: {
    id: string;
    name: string;
    email: string;
    student_id?: string;
    roll_number?: string;
  };
}

export interface TeacherAttendance {
  id: string;
  tenant_id: string;
  teacher_id: string;
  attendance_date: string;
  check_in_time?: string;
  check_out_time?: string;
  status: 'present' | 'absent' | 'late' | 'half_day';
  facial_recognition_confidence?: number;
  location_lat?: number;
  location_lng?: number;
  device_info?: any;
  notes?: string;
  created_at: string;
  updated_at?: string;
}

export interface AttendanceStats {
  total_sessions: number;
  total_students: number;
  present_percentage: number;
  absent_percentage?: number;
  late_percentage?: number;
  excused_percentage?: number;
}

export interface StudentWithAttendance {
  id: string;
  name: string;
  email: string;
  student_id?: string;
  roll_number?: string;
  attendance_record?: AttendanceRecord;
}

interface AttendanceState {
  // Current session data
  currentSession: AttendanceSession | null;
  attendanceRecords: AttendanceRecord[];
  studentsWithAttendance: StudentWithAttendance[];

  // Historical data
  sessions: AttendanceSession[];
  teacherAttendance: TeacherAttendance[];
  attendanceStats: AttendanceStats | null;

  // UI state
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  refreshing: boolean;

  // Actions
  loadSessions: (teacherId: string, classId?: string, dateRange?: { start: string; end: string }) => Promise<void>;
  createSession: (sessionData: Omit<AttendanceSession, 'id' | 'tenant_id' | 'created_at' | 'updated_at' | 'present_count' | 'absent_count' | 'late_count' | 'excused_count'>) => Promise<AttendanceSession | null>;
  loadSessionWithStudents: (sessionId: string) => Promise<void>;
  markAttendance: (studentId: string, status: AttendanceRecord['status'], notes?: string) => Promise<void>;
  bulkMarkAttendance: (records: { studentId: string; status: AttendanceRecord['status']; notes?: string }[]) => Promise<void>;
  updateSession: (sessionId: string, updates: Partial<AttendanceSession>) => Promise<void>;
  completeSession: (sessionId: string) => Promise<void>;
  loadAttendanceStats: (teacherId: string, classId?: string, dateRange?: { start: string; end: string }) => Promise<void>;
  markTeacherAttendance: (attendanceData: Omit<TeacherAttendance, 'id' | 'tenant_id' | 'created_at' | 'updated_at'>) => Promise<TeacherAttendance | null>;
  refreshData: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export const useAttendanceStore = create<AttendanceState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      currentSession: null,
      attendanceRecords: [],
      studentsWithAttendance: [],
      sessions: [],
      teacherAttendance: [],
      attendanceStats: null,
      isLoading: false,
      isSaving: false,
      error: null,
      refreshing: false,

      // Load attendance sessions for a teacher
      loadSessions: async (teacherId: string, classId?: string, dateRange?: { start: string; end: string }) => {
        const cacheKey = `attendance_sessions_${teacherId}_${classId || 'all'}_${dateRange?.start || 'all'}_${dateRange?.end || 'all'}`;

        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const sessions = await cacheManager.get<AttendanceSession[]>(
            cacheKey,
            async () => {
              let query = supabaseAdmin
                .from('attendance_sessions')
                .select(`
                  *,
                  class:classes(id, name, grade, section),
                  attendance_records(id, student_id, status, notes, marked_at)
                `)
                .eq('teacher_id', teacherId)
                .order('session_date', { ascending: false })
                .order('session_time', { ascending: false });

              if (classId) {
                query = query.eq('class_id', classId);
              }

              if (dateRange) {
                query = query.gte('session_date', dateRange.start).lte('session_date', dateRange.end);
              }

              const { data, error } = await query;

              if (error) throw new Error(`Failed to load sessions: ${error.message}`);
              return data as AttendanceSession[];
            },
            CACHE_CONFIG.ATTENDANCE
          );

          set((state) => {
            state.sessions = sessions || [];
            state.isLoading = false;
          });
        } catch (error) {
          console.error('Error loading sessions:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load sessions';
            state.isLoading = false;
          });
        }
      },

      // Create a new attendance session
      createSession: async (sessionData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher from enrollment store or pass it in
          // Get current teacher from enrollment store
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { data, error } = await supabaseAdmin
            .from('attendance_sessions')
            .insert({
              ...sessionData,
              tenant_id: currentTeacher.tenant_id,
            })
            .select(`
              *,
              class:classes(id, name, grade, section)
            `)
            .single();

          if (error) throw new Error(`Failed to create session: ${error.message}`);

          const newSession = data as AttendanceSession;

          set((state) => {
            state.sessions.unshift(newSession);
            state.currentSession = newSession;
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`attendance_sessions_${sessionData.teacher_id}`);

          return newSession;
        } catch (error) {
          console.error('Error creating session:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create session';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Load session with students for attendance marking
      loadSessionWithStudents: async (sessionId: string) => {
        const cacheKey = `attendance_session_students_${sessionId}`;

        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          // Get session details
          const { data: session, error: sessionError } = await supabaseAdmin
            .from('attendance_sessions')
            .select(`
              *,
              class:classes(id, name, grade, section)
            `)
            .eq('id', sessionId)
            .single();

          if (sessionError) throw new Error(`Failed to load session: ${sessionError.message}`);

          // Get students in the class with their attendance records
          const studentsWithAttendance = await cacheManager.get<StudentWithAttendance[]>(
            cacheKey,
            async () => {
              const { data: classStudents, error: studentsError } = await supabaseAdmin
                .from('class_students')
                .select(`
                  student:students(id, name, email, student_id, roll_number)
                `)
                .eq('class_id', session.class_id)
                .eq('status', 'active');

              if (studentsError) throw new Error(`Failed to load students: ${studentsError.message}`);

              // Get existing attendance records for this session
              const { data: attendanceRecords, error: recordsError } = await supabaseAdmin
                .from('attendance_records')
                .select('*')
                .eq('session_id', sessionId);

              if (recordsError) throw new Error(`Failed to load attendance records: ${recordsError.message}`);

              // Combine students with their attendance records
              const studentsWithAttendance: StudentWithAttendance[] = (classStudents || []).map((cs: any) => {
                const student = cs.student;
                const attendanceRecord = attendanceRecords?.find(record => record.student_id === student.id);

                return {
                  ...student,
                  attendance_record: attendanceRecord
                };
              });

              return studentsWithAttendance;
            },
            CACHE_CONFIG.STUDENTS
          );

          set((state) => {
            state.currentSession = session as AttendanceSession;
            state.studentsWithAttendance = studentsWithAttendance || [];
            state.attendanceRecords = studentsWithAttendance?.map(s => s.attendance_record).filter((record): record is AttendanceRecord => record !== undefined) || [];
            state.isLoading = false;
          });
        } catch (error) {
          console.error('Error loading session with students:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load session data';
            state.isLoading = false;
          });
        }
      },

      // Mark attendance for a single student
      markAttendance: async (studentId: string, status: AttendanceRecord['status'], notes?: string) => {
        try {
          const currentSession = get().currentSession;
          if (!currentSession) {
            throw new Error('No active session');
          }

          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher for tenant_id
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const { data, error } = await supabaseAdmin
            .from('attendance_records')
            .upsert({
              session_id: currentSession.id,
              student_id: studentId,
              status,
              notes,
              marked_by: currentSession.teacher_id,
              tenant_id: currentTeacher.tenant_id,
            })
            .select()
            .single();

          if (error) throw new Error(`Failed to mark attendance: ${error.message}`);

          const attendanceRecord = data as AttendanceRecord;

          set((state) => {
            // Update the student's attendance record
            const studentIndex = state.studentsWithAttendance.findIndex(s => s.id === studentId);
            if (studentIndex !== -1) {
              state.studentsWithAttendance[studentIndex].attendance_record = attendanceRecord;
            }

            // Update the records array
            const recordIndex = state.attendanceRecords.findIndex(r => r.student_id === studentId);
            if (recordIndex !== -1) {
              state.attendanceRecords[recordIndex] = attendanceRecord;
            } else {
              state.attendanceRecords.push(attendanceRecord);
            }

            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`attendance_session_students_${currentSession.id}`);
        } catch (error) {
          console.error('Error marking attendance:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to mark attendance';
            state.isSaving = false;
          });
        }
      },

      // Bulk mark attendance for multiple students
      bulkMarkAttendance: async (records) => {
        try {
          const currentSession = get().currentSession;
          if (!currentSession) {
            throw new Error('No active session');
          }

          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Get current teacher for tenant_id
          const { currentTeacher } = await import('@/stores/enrollmentStore').then(m => m.useEnrollmentStore.getState());

          if (!currentTeacher) {
            throw new Error('Teacher information not found');
          }

          const attendanceRecords = records.map(record => ({
            session_id: currentSession.id,
            student_id: record.studentId,
            status: record.status,
            notes: record.notes,
            marked_by: currentSession.teacher_id,
            tenant_id: currentTeacher.tenant_id,
          }));

          const { data, error } = await supabaseAdmin
            .from('attendance_records')
            .upsert(attendanceRecords, {
              onConflict: 'session_id,student_id',
              ignoreDuplicates: false
            })
            .select();

          if (error) throw new Error(`Failed to bulk mark attendance: ${error.message}`);

          const newRecords = data as AttendanceRecord[];

          set((state) => {
            // Update students with their new attendance records
            newRecords.forEach(record => {
              const studentIndex = state.studentsWithAttendance.findIndex(s => s.id === record.student_id);
              if (studentIndex !== -1) {
                state.studentsWithAttendance[studentIndex].attendance_record = record;
              }
            });

            // Update the records array
            state.attendanceRecords = newRecords;
            state.isSaving = false;
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`attendance_session_students_${currentSession.id}`);
        } catch (error) {
          console.error('Error bulk marking attendance:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to bulk mark attendance';
            state.isSaving = false;
          });
        }
      },

      // Update session details
      updateSession: async (sessionId: string, updates: Partial<AttendanceSession>) => {
        try {
          const { error } = await supabaseAdmin
            .from('attendance_sessions')
            .update(updates)
            .eq('id', sessionId);

          if (error) throw new Error(`Failed to update session: ${error.message}`);

          set((state) => {
            if (state.currentSession?.id === sessionId) {
              state.currentSession = { ...state.currentSession, ...updates };
            }

            const sessionIndex = state.sessions.findIndex(s => s.id === sessionId);
            if (sessionIndex !== -1) {
              state.sessions[sessionIndex] = { ...state.sessions[sessionIndex], ...updates };
            }
          });

          // Invalidate cache
          await cacheManager.invalidatePattern(`attendance_sessions_`);
        } catch (error) {
          console.error('Error updating session:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update session';
          });
        }
      },

      // Complete a session
      completeSession: async (sessionId: string) => {
        await get().updateSession(sessionId, { status: 'completed' });
      },

      // Resume a session
      resumeSession: async (sessionId: string) => {
        await get().updateSession(sessionId, { status: 'active' });
      },

      // Delete a session
      deleteSession: async (sessionId: string) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          // Delete attendance records first (cascade should handle this, but being explicit)
          const { error: attendanceError } = await supabaseAdmin
            .from('attendance_records')
            .delete()
            .eq('session_id', sessionId);

          if (attendanceError) {
            console.warn('Error deleting attendance records:', attendanceError);
            // Continue with session deletion even if attendance deletion fails
          }

          // Delete the session
          const { error } = await supabaseAdmin
            .from('attendance_sessions')
            .delete()
            .eq('id', sessionId);

          if (error) throw new Error(`Failed to delete session: ${error.message}`);

          // Update local state
          set((state) => {
            // Clear current session if it's the one being deleted
            if (state.currentSession?.id === sessionId) {
              state.currentSession = null;
              state.studentsWithAttendance = [];
              state.attendanceRecords = [];
            }

            // Remove from sessions list
            state.sessions = state.sessions.filter(s => s.id !== sessionId);

            state.isSaving = false;
          });

          // Clear cache to ensure fresh data
          await cacheManager.invalidatePattern('attendance_');
        } catch (error) {
          console.error('Error deleting session:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete session';
            state.isSaving = false;
          });
          throw error;
        }
      },

      // Load attendance statistics
      loadAttendanceStats: async (teacherId: string, classId?: string, dateRange?: { start: string; end: string }) => {
        try {
          // Use the simpler overview function for dashboard
          const { data, error } = await supabaseAdmin
            .rpc('get_attendance_overview', {
              p_teacher_id: teacherId,
              p_class_id: classId || null,
              p_start_date: dateRange?.start || null,
              p_end_date: dateRange?.end || null,
            });

          if (error) throw new Error(`Failed to load stats: ${error.message}`);

          // Transform the data to match the expected interface
          const stats = data?.[0];
          if (stats) {
            set((state) => {
              state.attendanceStats = {
                total_sessions: stats.total_sessions || 0,
                total_students: stats.total_students || 0,
                present_percentage: stats.present_percentage || 0,
                absent_percentage: 0,
                late_percentage: 0,
                excused_percentage: 0,
              };
            });
          } else {
            // Set default stats if no data
            set((state) => {
              state.attendanceStats = {
                total_sessions: 0,
                total_students: 0,
                present_percentage: 0,
                absent_percentage: 0,
                late_percentage: 0,
                excused_percentage: 0,
              };
            });
          }
        } catch (error) {
          console.error('Error loading attendance stats:', error);
          // Set default stats on error to prevent UI issues
          set((state) => {
            state.attendanceStats = {
              total_sessions: 0,
              total_students: 0,
              present_percentage: 0,
              absent_percentage: 0,
              late_percentage: 0,
              excused_percentage: 0,
            };
            state.error = error instanceof Error ? error.message : 'Failed to load statistics';
          });
        }
      },

      // Mark teacher attendance (facial recognition)
      markTeacherAttendance: async (attendanceData) => {
        try {
          set((state) => {
            state.isSaving = true;
            state.error = null;
          });

          const { data, error } = await supabaseAdmin
            .from('teacher_attendance')
            .upsert(attendanceData)
            .select()
            .single();

          if (error) throw new Error(`Failed to mark teacher attendance: ${error.message}`);

          const teacherAttendance = data as TeacherAttendance;

          set((state) => {
            const existingIndex = state.teacherAttendance.findIndex(
              ta => ta.teacher_id === attendanceData.teacher_id && ta.attendance_date === attendanceData.attendance_date
            );

            if (existingIndex !== -1) {
              state.teacherAttendance[existingIndex] = teacherAttendance;
            } else {
              state.teacherAttendance.unshift(teacherAttendance);
            }

            state.isSaving = false;
          });

          return teacherAttendance;
        } catch (error) {
          console.error('Error marking teacher attendance:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to mark teacher attendance';
            state.isSaving = false;
          });
          return null;
        }
      },

      // Refresh all data
      refreshData: async () => {
        try {
          set((state) => {
            state.refreshing = true;
            state.error = null;
          });

          // Invalidate all attendance caches
          await cacheManager.invalidatePattern('attendance_');

          set((state) => {
            state.refreshing = false;
          });
        } catch (error) {
          console.error('Error refreshing data:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to refresh data';
            state.refreshing = false;
          });
        }
      },

      // Clear error
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      // Reset store
      reset: () => {
        set((state) => {
          state.currentSession = null;
          state.attendanceRecords = [];
          state.studentsWithAttendance = [];
          state.sessions = [];
          state.teacherAttendance = [];
          state.attendanceStats = null;
          state.isLoading = false;
          state.isSaving = false;
          state.error = null;
          state.refreshing = false;
        });
      },
    }))
  )
);
