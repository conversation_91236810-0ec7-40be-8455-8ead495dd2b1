import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInRight } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';

// Import step components
import AIEnhancementStep from './steps/AIEnhancementStep';
import AttachmentsStep from './steps/AttachmentsStep';
import BasicInfoStep from './steps/BasicInfoStep';
import InstructionsStep from './steps/InstructionsStep';
import PreviewStep from './steps/PreviewStep';
import RubricsStep from './steps/RubricsStep';

export interface AssignmentData {
  // Basic Info
  title: string;
  description: string;
  due_date: Date;
  max_points: number;
  class_id: string;
  
  // Instructions
  instructions: string;
  
  // Attachments
  attachment_urls: string[];
  
  // Rubrics
  rubrics: Array<{
    criteria_name: string;
    description: string;
    max_points: number;
    order_index: number;
  }>;
  
  // AI Enhancement
  gemini_generated: boolean;
  ai_enhanced_content?: string;
  
  // Settings
  status: 'draft' | 'published';
  allow_late_submissions: boolean;
  show_grades_immediately: boolean;
}

const STEPS = [
  { id: 'basic', title: 'Basic Info', icon: 'information-circle-outline' },
  { id: 'instructions', title: 'Instructions', icon: 'document-text-outline' },
  { id: 'attachments', title: 'Attachments', icon: 'attach-outline' },
  { id: 'rubrics', title: 'Rubrics', icon: 'list-outline' },
  { id: 'ai', title: 'AI Enhance', icon: 'sparkles-outline' },
  { id: 'preview', title: 'Preview', icon: 'eye-outline' },
];

interface AssignmentCreationWizardProps {
  onComplete: (data: AssignmentData) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<AssignmentData>;
}

export default function AssignmentCreationWizard({
  onComplete,
  onCancel,
  initialData,
}: AssignmentCreationWizardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [assignmentData, setAssignmentData] = useState<AssignmentData>({
    title: '',
    description: '',
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    max_points: 100,
    class_id: '',
    instructions: '',
    attachment_urls: [],
    rubrics: [],
    gemini_generated: false,
    status: 'draft',
    allow_late_submissions: false,
    show_grades_immediately: false,
    ...initialData,
  });

  const scrollViewRef = useRef<ScrollView>(null);

  const updateAssignmentData = (updates: Partial<AssignmentData>) => {
    setAssignmentData(prev => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }
  };

  const handleComplete = async () => {
    try {
      setLoading(true);
      await onComplete(assignmentData);
    } catch (error) {
      Alert.alert('Error', 'Failed to create assignment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <View className="flex-row justify-between items-center px-4 py-3 border-b border-gray-200 dark:border-gray-700">
      {STEPS.map((step, index) => (
        <View key={step.id} className="flex-1 items-center">
          {/* Step Number */}
          <View
            className={`w-8 h-8 rounded-full items-center justify-center border-2 ${
              index <= currentStep
                ? 'bg-primary-500 border-primary-500'
                : isDark
                ? 'bg-gray-700 border-gray-600'
                : 'bg-gray-100 border-gray-300'
            }`}
          >
            {index < currentStep ? (
              <Ionicons name="checkmark" size={16} color="white" />
            ) : (
              <Text
                className={`text-sm font-rubik-bold ${
                  index <= currentStep
                    ? 'text-white'
                    : isDark
                    ? 'text-gray-300'
                    : 'text-gray-600'
                }`}
              >
                {index + 1}
              </Text>
            )}
          </View>

          {/* Step Title - Below Number */}
          <Text
            className={`text-xs font-rubik-bold mt-1 text-center ${
              index <= currentStep
                ? isDark ? 'text-white' : 'text-gray-900'
                : isDark ? 'text-gray-500' : 'text-gray-400'
            }`}
            numberOfLines={1}
            adjustsFontSizeToFit
          >
            {step.title}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    const stepProps = {
      data: assignmentData,
      updateData: updateAssignmentData,
      onNext: nextStep,
      onPrev: prevStep,
    };

    switch (STEPS[currentStep].id) {
      case 'basic':
        return <BasicInfoStep {...stepProps} />;
      case 'instructions':
        return <InstructionsStep {...stepProps} />;
      case 'attachments':
        return <AttachmentsStep {...stepProps} />;
      case 'rubrics':
        return <RubricsStep {...stepProps} />;
      case 'ai':
        return <AIEnhancementStep {...stepProps} />;
      case 'preview':
        return <PreviewStep {...stepProps} onComplete={handleComplete} loading={loading} />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >


        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Step Content */}
        <ScrollView
          ref={scrollViewRef}
          className="flex-1"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Animated.View
            key={currentStep}
            entering={FadeInRight.duration(300)}
            className="flex-1"
          >
            {renderCurrentStep()}
          </Animated.View>
        </ScrollView>

        {/* Navigation Buttons */}
        <View className="flex-row justify-between items-center p-4 border-t border-gray-200 dark:border-gray-700">
          <TouchableOpacity
            onPress={prevStep}
            disabled={currentStep === 0}
            className={`px-6 py-3 rounded-xl ${
              currentStep === 0
                ? 'opacity-50'
                : isDark
                ? 'bg-dark-card'
                : 'bg-light-card'
            }`}
          >
            <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Previous
            </Text>
          </TouchableOpacity>

          {/* Page Counter - Horizontally Centered */}
          <View className={`px-4 py-2 rounded-full ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <Text className={`font-rubik-bold text-sm ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
              {currentStep + 1} of {STEPS.length}
            </Text>
          </View>

          {currentStep === STEPS.length - 1 ? (
            <TouchableOpacity
              onPress={handleComplete}
              disabled={loading}
              className="bg-primary-500 px-6 py-3 rounded-xl"
            >
              <Text className="text-white font-rubik-medium">
                {loading ? 'Creating...' : 'Create Assignment'}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPress={nextStep}
              className="bg-primary-500 px-6 py-3 rounded-xl"
            >
              <Text className="text-white font-rubik-medium">Next</Text>
            </TouchableOpacity>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
