import { supabase } from '@/lib/supabase';
import { generateRubricCriteria } from '@/lib/gemini';

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id: assignmentId } = params;

    // Fetch assignment details
    const { data: assignment, error: assignmentError } = await supabase
      .from('assignments')
      .select('*')
      .eq('id', assignmentId)
      .single();

    if (assignmentError || !assignment) {
      return Response.json({ error: 'Assignment not found' }, { status: 404 });
    }

    // Generate rubric criteria
    const criteria = await generateRubricCriteria(
      assignment.title,
      assignment.instructions || '',
      assignment.max_points,
      'General' // You might want to add subject field
    );

    // Save generated rubrics to database
    const rubricData = criteria.map((criterion, index) => ({
      assignment_id: assignmentId,
      tenant_id: assignment.tenant_id,
      criteria_name: criterion.criteria_name,
      description: criterion.description,
      max_points: criterion.max_points,
      order_index: index,
    }));

    const { data: savedRubrics, error: insertError } = await supabase
      .from('assignment_rubrics')
      .insert(rubricData)
      .select();

    if (insertError) {
      return Response.json({ error: 'Failed to save generated rubrics' }, { status: 500 });
    }

    return Response.json({
      success: true,
      rubrics: savedRubrics,
      generatedCriteria: criteria,
    });

  } catch (error) {
    console.error('Error generating rubric criteria:', error);
    return Response.json(
      { error: 'Failed to generate rubric criteria' },
      { status: 500 }
    );
  }
}
