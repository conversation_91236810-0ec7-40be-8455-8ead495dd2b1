import { useAuth } from '@clerk/clerk-expo';
import { useSupabase } from '@/components/ClerkSupabaseProvider';
import { useSecurityStore } from '@/stores/securityStore';
import { useAuthStore } from '@/stores/authStore';

// Input sanitization utilities
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential XSS characters
    .replace(/['";]/g, '') // Remove SQL injection characters
    .substring(0, 1000); // Limit length
};

export const validateUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

// Rate limiting utility
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    return true;
  }
}

const rateLimiter = new RateLimiter();

// Secure API wrapper
export class SecureApiClient {
  private supabase: any;
  private getToken: () => Promise<string | null>;
  private userId: string;

  constructor(supabase: any, getToken: () => Promise<string | null>, userId: string) {
    this.supabase = supabase;
    this.getToken = getToken;
    this.userId = userId;
  }

  private async validateRequest(operation: string): Promise<boolean> {
    // Check rate limiting
    if (!rateLimiter.isAllowed(this.userId)) {
      useSecurityStore.getState().flagSuspiciousActivity('Rate limit exceeded');
      throw new Error('Too many requests. Please try again later.');
    }

    // Check security status
    const securityStatus = useSecurityStore.getState().getSecurityStatus();
    if (!securityStatus.isSecure) {
      throw new Error('Security check failed. Please re-authenticate.');
    }

    // Update activity
    useAuthStore.getState().updateActivity();

    return true;
  }

  private async executeQuery(query: any, operation: string) {
    await this.validateRequest(operation);

    try {
      const result = await query;
      
      if (result.error) {
        console.error(`API Error in ${operation}:`, result.error);
        
        // Check for authentication errors
        if (result.error.code === 'PGRST301' || result.error.message?.includes('JWT')) {
          useSecurityStore.getState().setRequiresReauth(true);
          throw new Error('Authentication expired. Please sign in again.');
        }
        
        throw new Error(result.error.message || 'Operation failed');
      }

      return result.data;
    } catch (error) {
      console.error(`Error in ${operation}:`, error);
      throw error;
    }
  }

  // Secure data fetching methods
  async getTeacherClasses() {
    const query = this.supabase
      .from('classes')
      .select(`
        id,
        name,
        subject,
        grade,
        student_count:students(count)
      `)
      .eq('teacher_id', this.userId);

    return this.executeQuery(query, 'getTeacherClasses');
  }

  async getTeacherAssignments() {
    const query = this.supabase
      .from('assignments')
      .select(`
        id,
        title,
        due_date,
        status,
        submissions_count:submissions(count),
        total_students:classes!inner(student_count)
      `)
      .eq('teacher_id', this.userId)
      .order('due_date', { ascending: true })
      .limit(10);

    return this.executeQuery(query, 'getTeacherAssignments');
  }

  async getRecentAttendance() {
    const query = this.supabase
      .from('attendance')
      .select(`
        id,
        date,
        class_id,
        present_count,
        total_students
      `)
      .eq('teacher_id', this.userId)
      .order('date', { ascending: false })
      .limit(7);

    return this.executeQuery(query, 'getRecentAttendance');
  }

  async getTeacherStats() {
    // Get multiple stats in parallel with proper error handling
    const [classesResult, assignmentsResult, attendanceResult] = await Promise.allSettled([
      this.getTeacherClasses(),
      this.getTeacherAssignments(),
      this.getRecentAttendance()
    ]);

    const stats = {
      totalClasses: 0,
      totalStudents: 0,
      pendingAssignments: 0,
      todayAttendance: 0,
      upcomingTests: 0,
    };

    if (classesResult.status === 'fulfilled') {
      stats.totalClasses = classesResult.value?.length || 0;
      stats.totalStudents = classesResult.value?.reduce((sum: number, cls: any) => 
        sum + (cls.student_count || 0), 0) || 0;
    }

    if (assignmentsResult.status === 'fulfilled') {
      stats.pendingAssignments = assignmentsResult.value?.filter((a: any) => 
        a.status === 'published').length || 0;
    }

    if (attendanceResult.status === 'fulfilled') {
      const today = new Date().toISOString().split('T')[0];
      const todayAttendance = attendanceResult.value?.find((a: any) => a.date === today);
      stats.todayAttendance = todayAttendance?.present_count || 0;
    }

    return stats;
  }
}

// Hook to create secure API client
export const useSecureApi = () => {
  const { getToken, userId } = useAuth();
  const supabase = useSupabase();

  if (!userId) {
    throw new Error('User not authenticated');
  }

  return new SecureApiClient(supabase, getToken, userId);
};
