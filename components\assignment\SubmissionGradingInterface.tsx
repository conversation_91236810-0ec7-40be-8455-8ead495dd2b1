import { useColorScheme } from '@/hooks/useColorScheme';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface Assignment {
  id: string;
  title: string;
  description: string;
  max_points: number;
}

interface Submission {
  id: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  attachment_urls?: string[];
  status: 'draft' | 'submitted' | 'graded' | 'returned';
  grade?: number;
  feedback?: string;
  gemini_feedback?: string;
  submitted_at: string;
  graded_at?: string;
  student_name?: string;
  student_email?: string;
}

interface Rubric {
  id: string;
  criteria_name: string;
  description: string;
  max_points: number;
  order_index: number;
}

interface SubmissionGradingInterfaceProps {
  assignment: Assignment;
  submission: Submission;
  rubrics?: Rubric[];
  onGradingComplete: () => void;
}

export default function SubmissionGradingInterface({
  assignment,
  submission,
  rubrics = [],
  onGradingComplete,
}: SubmissionGradingInterfaceProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { gradeSubmission, generateAIGradingFeedback, fetchSubmissionById } = useAssignmentStore();

  const [grade, setGrade] = useState(submission.grade?.toString() || '');
  const [feedback, setFeedback] = useState(submission.feedback || '');
  const [rubricGrades, setRubricGrades] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);

  const calculateTotalFromRubrics = () => {
    return Object.values(rubricGrades).reduce((sum, points) => sum + points, 0);
  };

  const handleRubricGrade = (rubricId: string, points: number) => {
    setRubricGrades(prev => ({
      ...prev,
      [rubricId]: points,
    }));

    // Auto-update total grade if using rubrics
    if (rubrics.length > 0) {
      const newTotal = { ...rubricGrades, [rubricId]: points };
      const total = Object.values(newTotal).reduce((sum, p) => sum + p, 0);
      setGrade(total.toString());
    }
  };

  const generateAIFeedback = async () => {
    if (!submission.content?.trim()) {
      Alert.alert('No Content', 'Cannot generate feedback for empty submission.');
      return;
    }

    setAiGenerating(true);
    try {
      // Use real AI grading functionality
      await generateAIGradingFeedback(submission.id, submission.assignment_id);

      // Refresh submission data to get the AI feedback
      const updatedSubmission = await fetchSubmissionById(submission.id);
      if (updatedSubmission) {
        setFeedback(updatedSubmission.gemini_feedback || '');
        setGrade(updatedSubmission.grade?.toString() || '');
      }

      Alert.alert('AI Feedback Generated', 'AI has analyzed the submission and provided feedback and a suggested grade.');
    } catch (error) {
      console.error('Error generating AI feedback:', error);
      Alert.alert('Error', 'Failed to generate AI feedback. Please try again.');
    } finally {
      setAiGenerating(false);
    }
  };

  const handleSaveGrade = async () => {
    const gradeValue = parseFloat(grade);
    
    if (isNaN(gradeValue) || gradeValue < 0 || gradeValue > assignment.max_points) {
      Alert.alert(
        'Invalid Grade',
        `Please enter a valid grade between 0 and ${assignment.max_points}.`
      );
      return;
    }

    setLoading(true);
    try {
      await gradeSubmission(submission.id, gradeValue, feedback);
      Alert.alert(
        'Grade Saved',
        'The grade and feedback have been saved successfully.',
        [{ text: 'OK', onPress: onGradingComplete }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save grade');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getGradeColor = (currentGrade: number, maxPoints: number) => {
    const percentage = (currentGrade / maxPoints) * 100;
    if (percentage >= 90) return 'text-green-500';
    if (percentage >= 80) return 'text-blue-500';
    if (percentage >= 70) return 'text-yellow-500';
    if (percentage >= 60) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
        </TouchableOpacity>
        
        <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Grade Submission
        </Text>
        
        <View className={`px-3 py-1 rounded-full ${
          submission.status === 'graded' 
            ? 'bg-green-100 dark:bg-green-900/30' 
            : 'bg-blue-100 dark:bg-blue-900/30'
        }`}>
          <Text className={`font-rubik-medium text-sm ${
            submission.status === 'graded'
              ? 'text-green-700 dark:text-green-300'
              : 'text-blue-700 dark:text-blue-300'
          }`}>
            {submission.status === 'graded' ? 'Graded' : 'Pending'}
          </Text>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="p-4 space-y-6">
          {/* Student and Assignment Info */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <View className="flex-row items-start justify-between mb-3">
              <View className="flex-1">
                <Text className={`text-xl font-rubik-bold mb-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {submission.student_name}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {submission.student_email}
                </Text>
              </View>
              
              <View className="items-end">
                <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {assignment.title}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  Max: {assignment.max_points} points
                </Text>
              </View>
            </View>
            
            <View className="flex-row items-center">
              <Ionicons name="time-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Submitted {formatDate(submission.submitted_at)}
              </Text>
            </View>
          </View>

          {/* Submission Content */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Student Submission
            </Text>
            
            {submission.content ? (
              <View className={`p-4 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {submission.content}
                </Text>
              </View>
            ) : (
              <Text className={`font-rubik italic ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                No written content submitted
              </Text>
            )}

            {/* Attachments */}
            {submission.attachment_urls && submission.attachment_urls.length > 0 && (
              <View className="mt-4">
                <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Attachments ({submission.attachment_urls.length})
                </Text>
                {submission.attachment_urls.map((url, index) => (
                  <TouchableOpacity
                    key={index}
                    className="flex-row items-center p-3 rounded-lg bg-gray-100 dark:bg-gray-800 mb-2"
                  >
                    <Ionicons name="document-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
                    <Text className={`ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      Attachment {index + 1}
                    </Text>
                    <Ionicons name="download-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} className="ml-auto" />
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          {/* Rubric Grading */}
          {rubrics.length > 0 && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <Text className={`font-rubik-bold text-base mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Grading Rubric
              </Text>
              
              {rubrics.map((rubric) => (
                <View key={rubric.id} className="mb-4 last:mb-0">
                  <View className="flex-row items-start justify-between mb-2">
                    <View className="flex-1 mr-3">
                      <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                        {rubric.criteria_name}
                      </Text>
                      <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                        {rubric.description}
                      </Text>
                    </View>
                    <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {rubric.max_points} pts
                    </Text>
                  </View>
                  
                  <View className="flex-row items-center space-x-2">
                    {[...Array(rubric.max_points + 1)].map((_, points) => (
                      <TouchableOpacity
                        key={points}
                        onPress={() => handleRubricGrade(rubric.id, points)}
                        className={`w-10 h-10 rounded-lg items-center justify-center ${
                          rubricGrades[rubric.id] === points
                            ? 'bg-primary-500'
                            : isDark
                            ? 'bg-dark-background'
                            : 'bg-light-background'
                        }`}
                      >
                        <Text className={`font-rubik-bold ${
                          rubricGrades[rubric.id] === points
                            ? 'text-white'
                            : isDark ? 'text-dark-text' : 'text-light-text'
                        }`}>
                          {points}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              ))}
              
              <View className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
                <View className="flex-row items-center justify-between">
                  <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Total from Rubric:
                  </Text>
                  <Text className={`font-rubik-bold text-lg ${getGradeColor(calculateTotalFromRubrics(), assignment.max_points)}`}>
                    {calculateTotalFromRubrics()}/{assignment.max_points}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Grade Input */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Final Grade
            </Text>
            
            <View className="flex-row items-center space-x-3">
              <View className="flex-1">
                <TextInput
                  value={grade}
                  onChangeText={setGrade}
                  placeholder="Enter grade"
                  placeholderTextColor={isDark ? '#666' : '#999'}
                  keyboardType="numeric"
                  className={`p-4 rounded-lg border text-center text-xl font-rubik-bold ${
                    isDark ? 'bg-dark-background text-dark-text border-dark-border' : 'bg-light-background text-light-text border-light-border'
                  }`}
                />
              </View>
              
              <Text className={`font-rubik-bold text-xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                / {assignment.max_points}
              </Text>
              
              {grade && !isNaN(parseFloat(grade)) && (
                <View className={`px-3 py-2 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
                  <Text className={`font-rubik-bold ${getGradeColor(parseFloat(grade), assignment.max_points)}`}>
                    {Math.round((parseFloat(grade) / assignment.max_points) * 100)}%
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Feedback */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <View className="flex-row items-center justify-between mb-3">
              <Text className={`font-rubik-bold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Feedback
              </Text>
              
              <TouchableOpacity
                onPress={generateAIFeedback}
                disabled={aiGenerating}
                className="flex-row items-center px-3 py-2 rounded-lg bg-purple-500"
              >
                {aiGenerating ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="sparkles-outline" size={16} color="white" />
                )}
                <Text className="text-white font-rubik-medium ml-2">
                  {aiGenerating ? 'Generating...' : 'AI Feedback'}
                </Text>
              </TouchableOpacity>
            </View>
            
            <TextInput
              value={feedback}
              onChangeText={setFeedback}
              placeholder="Provide feedback to help the student improve..."
              placeholderTextColor={isDark ? '#666' : '#999'}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
              className={`p-4 rounded-lg border ${
                isDark ? 'bg-dark-background text-dark-text border-dark-border' : 'bg-light-background text-light-text border-light-border'
              }`}
              style={{ minHeight: 120 }}
            />
            
            <Text className={`text-right mt-2 text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {feedback.length} characters
            </Text>
          </View>
        </Animated.View>
      </ScrollView>

      {/* Action Buttons */}
      <View className="flex-row space-x-3 p-4 border-t border-gray-200 dark:border-gray-700">
        <TouchableOpacity
          onPress={() => router.back()}
          disabled={loading}
          className={`flex-1 py-3 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}
        >
          <Text className={`text-center font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Cancel
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleSaveGrade}
          disabled={loading || !grade.trim() || isNaN(parseFloat(grade))}
          className={`flex-1 py-3 rounded-xl ${
            loading || !grade.trim() || isNaN(parseFloat(grade))
              ? 'bg-gray-400' 
              : 'bg-primary-500'
          }`}
        >
          <Text className="text-white text-center font-rubik-medium">
            {loading ? 'Saving...' : 'Save Grade'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
