import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Alert } from 'react-native';

export interface ExportData {
  assignment: {
    id: string;
    title: string;
    description: string;
    max_points: number;
    due_date: string;
    status: string;
    created_at: string;
  };
  submissions: Array<{
    id: string;
    student_name: string;
    student_email: string;
    content?: string;
    status: string;
    grade?: number;
    feedback?: string;
    submitted_at?: string;
    graded_at?: string;
    graded_by?: string;
  }>;
  analytics?: {
    total_submissions: number;
    average_grade: number;
    completion_rate: number;
    grade_distribution: Record<string, number>;
  };
}

// Generate CSV content from export data
export const generateCSV = (data: ExportData): string => {
  const headers = [
    'Student Name',
    'Student Email',
    'Status',
    'Grade',
    'Max Points',
    'Percentage',
    'Submitted At',
    'Graded At',
    'Graded By',
    'Feedback'
  ];

  const rows = data.submissions.map(submission => [
    submission.student_name || '',
    submission.student_email || '',
    submission.status || '',
    submission.grade?.toString() || '',
    data.assignment.max_points.toString(),
    submission.grade ? Math.round((submission.grade / data.assignment.max_points) * 100) + '%' : '',
    submission.submitted_at || '',
    submission.graded_at || '',
    submission.graded_by || '',
    (submission.feedback || '').replace(/"/g, '""') // Escape quotes
  ]);

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');

  return csvContent;
};

// Generate JSON content from export data
export const generateJSON = (data: ExportData): string => {
  return JSON.stringify(data, null, 2);
};

// Export data to file and share
export const exportToFile = async (
  data: ExportData,
  format: 'csv' | 'json' = 'csv',
  filename?: string
): Promise<void> => {
  try {
    const assignmentTitle = data.assignment.title.replace(/[^a-zA-Z0-9]/g, '_');
    const timestamp = new Date().toISOString().split('T')[0];
    const defaultFilename = `${assignmentTitle}_export_${timestamp}.${format}`;
    const finalFilename = filename || defaultFilename;

    let content: string;
    let mimeType: string;

    if (format === 'csv') {
      content = generateCSV(data);
      mimeType = 'text/csv';
    } else {
      content = generateJSON(data);
      mimeType = 'application/json';
    }

    // Create file in document directory
    const fileUri = FileSystem.documentDirectory + finalFilename;
    await FileSystem.writeAsStringAsync(fileUri, content, {
      encoding: FileSystem.EncodingType.UTF8,
    });

    // Check if sharing is available
    const isAvailable = await Sharing.isAvailableAsync();
    if (isAvailable) {
      await Sharing.shareAsync(fileUri, {
        mimeType,
        dialogTitle: `Export ${data.assignment.title}`,
        UTI: format === 'csv' ? 'public.comma-separated-values-text' : 'public.json',
      });
    } else {
      Alert.alert(
        'Export Complete',
        `File saved to: ${fileUri}`,
        [{ text: 'OK' }]
      );
    }
  } catch (error) {
    console.error('Error exporting file:', error);
    Alert.alert(
      'Export Failed',
      'Failed to export the file. Please try again.',
      [{ text: 'OK' }]
    );
    throw error;
  }
};

// Generate grade report data
export const generateGradeReport = (
  assignment: any,
  submissions: any[]
): ExportData => {
  const formattedSubmissions = submissions.map(submission => ({
    id: submission.id,
    student_name: submission.student_name || 'Unknown Student',
    student_email: submission.student_email || '',
    content: submission.content,
    status: submission.status,
    grade: submission.grade,
    feedback: submission.feedback,
    submitted_at: submission.submitted_at,
    graded_at: submission.graded_at,
    graded_by: submission.graded_by,
  }));

  // Calculate analytics
  const gradedSubmissions = submissions.filter(s => s.grade !== null && s.grade !== undefined);
  const totalSubmissions = submissions.length;
  const submittedCount = submissions.filter(s => s.status === 'submitted' || s.status === 'graded').length;

  let analytics = undefined;
  if (gradedSubmissions.length > 0) {
    const grades = gradedSubmissions.map(s => s.grade);
    const averageGrade = grades.reduce((sum, grade) => sum + grade, 0) / grades.length;
    
    // Grade distribution
    const gradeDistribution: Record<string, number> = {
      'A (90-100%)': 0,
      'B (80-89%)': 0,
      'C (70-79%)': 0,
      'D (60-69%)': 0,
      'F (0-59%)': 0,
    };

    gradedSubmissions.forEach(submission => {
      const percentage = (submission.grade / assignment.max_points) * 100;
      if (percentage >= 90) gradeDistribution['A (90-100%)']++;
      else if (percentage >= 80) gradeDistribution['B (80-89%)']++;
      else if (percentage >= 70) gradeDistribution['C (70-79%)']++;
      else if (percentage >= 60) gradeDistribution['D (60-69%)']++;
      else gradeDistribution['F (0-59%)']++;
    });

    analytics = {
      total_submissions: totalSubmissions,
      average_grade: Math.round(averageGrade * 100) / 100,
      completion_rate: totalSubmissions > 0 ? Math.round((submittedCount / totalSubmissions) * 100) : 0,
      grade_distribution: gradeDistribution,
    };
  }

  return {
    assignment: {
      id: assignment.id,
      title: assignment.title,
      description: assignment.description,
      max_points: assignment.max_points,
      due_date: assignment.due_date,
      status: assignment.status,
      created_at: assignment.created_at,
    },
    submissions: formattedSubmissions,
    analytics,
  };
};

// Bulk export multiple assignments
export const exportMultipleAssignments = async (
  assignments: any[],
  allSubmissions: any[],
  format: 'csv' | 'json' = 'csv'
): Promise<void> => {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `bulk_assignments_export_${timestamp}.${format}`;

    const exportData = assignments.map(assignment => {
      const assignmentSubmissions = allSubmissions.filter(
        s => s.assignment_id === assignment.id
      );
      return generateGradeReport(assignment, assignmentSubmissions);
    });

    let content: string;
    let mimeType: string;

    if (format === 'csv') {
      // For CSV, combine all assignments into one file
      const allRows: string[] = [];
      
      exportData.forEach((data, index) => {
        if (index > 0) allRows.push(''); // Add empty row between assignments
        allRows.push(`Assignment: ${data.assignment.title}`);
        allRows.push(generateCSV(data));
      });
      
      content = allRows.join('\n');
      mimeType = 'text/csv';
    } else {
      content = JSON.stringify(exportData, null, 2);
      mimeType = 'application/json';
    }

    const fileUri = FileSystem.documentDirectory + filename;
    await FileSystem.writeAsStringAsync(fileUri, content, {
      encoding: FileSystem.EncodingType.UTF8,
    });

    const isAvailable = await Sharing.isAvailableAsync();
    if (isAvailable) {
      await Sharing.shareAsync(fileUri, {
        mimeType,
        dialogTitle: 'Export Multiple Assignments',
        UTI: format === 'csv' ? 'public.comma-separated-values-text' : 'public.json',
      });
    } else {
      Alert.alert(
        'Export Complete',
        `File saved to: ${fileUri}`,
        [{ text: 'OK' }]
      );
    }
  } catch (error) {
    console.error('Error exporting multiple assignments:', error);
    Alert.alert(
      'Export Failed',
      'Failed to export assignments. Please try again.',
      [{ text: 'OK' }]
    );
    throw error;
  }
};

// Generate summary report
export const generateSummaryReport = (assignments: any[], submissions: any[]) => {
  const totalAssignments = assignments.length;
  const publishedAssignments = assignments.filter(a => a.status === 'published').length;
  const draftAssignments = assignments.filter(a => a.status === 'draft').length;
  const closedAssignments = assignments.filter(a => a.status === 'closed').length;

  const totalSubmissions = submissions.length;
  const gradedSubmissions = submissions.filter(s => s.status === 'graded').length;
  const pendingSubmissions = submissions.filter(s => s.status === 'submitted').length;

  const allGrades = submissions.filter(s => s.grade !== null).map(s => s.grade);
  const averageGrade = allGrades.length > 0 
    ? allGrades.reduce((sum, grade) => sum + grade, 0) / allGrades.length 
    : 0;

  return {
    summary: {
      total_assignments: totalAssignments,
      published_assignments: publishedAssignments,
      draft_assignments: draftAssignments,
      closed_assignments: closedAssignments,
      total_submissions: totalSubmissions,
      graded_submissions: gradedSubmissions,
      pending_submissions: pendingSubmissions,
      overall_average_grade: Math.round(averageGrade * 100) / 100,
    },
    assignments: assignments.map(assignment => {
      const assignmentSubmissions = submissions.filter(s => s.assignment_id === assignment.id);
      const assignmentGrades = assignmentSubmissions.filter(s => s.grade !== null);
      
      return {
        id: assignment.id,
        title: assignment.title,
        status: assignment.status,
        max_points: assignment.max_points,
        due_date: assignment.due_date,
        submission_count: assignmentSubmissions.length,
        graded_count: assignmentGrades.length,
        average_grade: assignmentGrades.length > 0 
          ? Math.round((assignmentGrades.reduce((sum, s) => sum + s.grade, 0) / assignmentGrades.length) * 100) / 100
          : 0,
      };
    }),
  };
};
