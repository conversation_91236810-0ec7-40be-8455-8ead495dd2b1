import { useColorScheme } from '@/hooks/useColorScheme';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { useEnrollmentStore } from '@/stores/enrollmentStore';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Platform,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { AssignmentData } from '../AssignmentCreationWizard';

interface BasicInfoStepProps {
  data: AssignmentData;
  updateData: (updates: Partial<AssignmentData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function BasicInfoStep({ data, updateData, onNext }: BasicInfoStepProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const { clerkUser, supabaseUser } = useSupabaseAuth();
  const { availableClasses, loadAvailableClasses, currentTeacher, loadTeacherData } = useEnrollmentStore();

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const loadData = async () => {
      if (clerkUser?.id && !currentTeacher) {
        await loadTeacherData(clerkUser.id);
      } else if (currentTeacher?.id && availableClasses.length === 0) {
        await loadAvailableClasses(currentTeacher.id);
      }
    };

    loadData();
  }, [clerkUser?.id, currentTeacher, availableClasses.length, loadTeacherData, loadAvailableClasses]);

  const validateStep = () => {
    const newErrors: Record<string, string> = {};

    if (!data.title.trim()) {
      newErrors.title = 'Assignment title is required';
    }

    if (!data.description.trim()) {
      newErrors.description = 'Assignment description is required';
    }

    if (!data.class_id) {
      newErrors.class_id = 'Please select a class';
    }

    if (data.due_date <= new Date()) {
      newErrors.due_date = 'Due date must be in the future';
    }

    if (data.max_points <= 0) {
      newErrors.max_points = 'Max points must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      onNext();
    } else {
      Alert.alert('Validation Error', 'Please fix the errors before continuing.');
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      updateData({ due_date: selectedDate });
    }
  };

  return (
    <View className="flex-1 p-4">
      <Animated.View entering={FadeInDown.delay(100).duration(400)} className="space-y-6">
        {/* Title */}
        <View className="space-y-3">
          <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Assignment Title *
          </Text>
          <View className={`rounded-xl overflow-hidden shadow-sm ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          } ${errors.title ? 'border-2 border-red-500' : ''}`}>
            <TextInput
              value={data.title}
              onChangeText={(text) => updateData({ title: text })}
              placeholder="Enter assignment title"
              placeholderTextColor={isDark ? '#666' : '#999'}
              className={`px-4 py-3.5 font-rubik ${
                isDark ? 'text-dark-text' : 'text-light-text'
              }`}
            />
          </View>
          {errors.title && (
            <Text className="text-red-500 text-sm font-rubik-medium">{errors.title}</Text>
          )}
        </View>

        {/* Description */}
        <View className="space-y-3">
          <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Description *
          </Text>
          <View className={`rounded-xl overflow-hidden shadow-sm ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          } ${errors.description ? 'border-2 border-red-500' : ''}`}>
            <TextInput
              value={data.description}
              onChangeText={(text) => updateData({ description: text })}
              placeholder="Describe what students need to do"
              placeholderTextColor={isDark ? '#666' : '#999'}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              className={`px-4 py-3.5 font-rubik ${
                isDark ? 'text-dark-text' : 'text-light-text'
              }`}
            />
          </View>
          {errors.description && (
            <Text className="text-red-500 text-sm font-rubik-medium">{errors.description}</Text>
          )}
        </View>

        {/* Class Selection */}
        <View className="space-y-3">
          <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Select Class *
          </Text>
          <View className={`rounded-xl overflow-hidden shadow-sm ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          } ${errors.class_id ? 'border-2 border-red-500' : ''}`}>
            <Picker
              selectedValue={data.class_id}
              onValueChange={(value) => updateData({ class_id: value })}
              style={{
                color: isDark ? '#FFFFFF' : '#000000',
                backgroundColor: 'transparent',
              }}
            >
              <Picker.Item label="Select a class..." value="" />
              {availableClasses.map((cls) => (
                <Picker.Item
                  key={cls.id}
                  label={`${cls.name} (${cls.grade} - ${cls.section})`}
                  value={cls.id}
                />
              ))}
            </Picker>
          </View>
          {errors.class_id && (
            <Text className="text-red-500 text-sm font-rubik-medium">{errors.class_id}</Text>
          )}
        </View>

        {/* Due Date */}
        <View className="space-y-3">
          <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Due Date *
          </Text>
          <TouchableOpacity
            onPress={() => setShowDatePicker(true)}
            className={`rounded-xl p-4 flex-row items-center justify-between shadow-sm ${
              isDark ? 'bg-dark-card' : 'bg-light-card'
            } ${errors.due_date ? 'border-2 border-red-500' : ''}`}
          >
            <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {data.due_date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
            <Ionicons
              name="calendar-outline"
              size={20}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          </TouchableOpacity>
          {errors.due_date && (
            <Text className="text-red-500 text-sm font-rubik-medium">{errors.due_date}</Text>
          )}
        </View>

        {/* Max Points */}
        <View className="space-y-3">
          <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Maximum Points *
          </Text>
          <View className={`rounded-xl overflow-hidden shadow-sm ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          } ${errors.max_points ? 'border-2 border-red-500' : ''}`}>
            <TextInput
              value={data.max_points.toString()}
              onChangeText={(text) => {
                const points = parseInt(text) || 0;
                updateData({ max_points: points });
              }}
              placeholder="100"
              placeholderTextColor={isDark ? '#666' : '#999'}
              keyboardType="numeric"
              className={`px-4 py-3.5 font-rubik ${
                isDark ? 'text-dark-text' : 'text-light-text'
              }`}
            />
          </View>
          {errors.max_points && (
            <Text className="text-red-500 text-sm font-rubik-medium">{errors.max_points}</Text>
          )}
        </View>

        {/* Settings */}
        <View className="space-y-6">
          <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Assignment Settings
          </Text>

          <TouchableOpacity
            onPress={() => updateData({ allow_late_submissions: !data.allow_late_submissions })}
            className="flex-row items-center justify-between py-2"
          >
            <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Allow Late Submissions
            </Text>
            <View className={`w-12 h-6 rounded-full ${
              data.allow_late_submissions ? 'bg-primary-500' : 'bg-gray-300'
            }`}>
              <View className={`w-5 h-5 rounded-full bg-white mt-0.5 ${
                data.allow_late_submissions ? 'ml-6' : 'ml-0.5'
              }`} />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => updateData({ show_grades_immediately: !data.show_grades_immediately })}
            className="flex-row items-center justify-between py-2"
          >
            <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Show Grades Immediately
            </Text>
            <View className={`w-12 h-6 rounded-full ${
              data.show_grades_immediately ? 'bg-primary-500' : 'bg-gray-300'
            }`}>
              <View className={`w-5 h-5 rounded-full bg-white mt-0.5 ${
                data.show_grades_immediately ? 'ml-6' : 'ml-0.5'
              }`} />
            </View>
          </TouchableOpacity>
        </View>


      </Animated.View>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={data.due_date}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}
    </View>
  );
}
