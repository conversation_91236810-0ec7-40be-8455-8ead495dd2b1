import { supabase } from './supabase';
import { generateUUID } from './utils';

/**
 * Directly creates a user in Supabase using the existing users table
 * 
 * @param name User's full name
 * @param email User's email address
 * @param role User's role (admin, teacher, student, parent)
 * @param tenantId The tenant ID
 * @returns The API response
 */
export const createUserDirectly = async (
  name: string,
  email: string,
  role: string,
  tenantId: string
): Promise<any> => {
  try {
    // Generate a random UUID for the user
    const userId = generateUUID();

    console.log('Creating user with data:', {
      id: userId,
      name,
      email,
      role,
      tenant_id: tenantId,
      clerk_user_id: `temp_${userId}`,
    });

    // Using the existing users table
    console.log('Inserting into users table');

    // Insert directly into the users table
    const { data, error } = await supabase
      .from('users')
      .insert({
        id: userId,
        name,
        email,
        role,
        tenant_id: tenantId,
        clerk_user_id: `temp_${userId}`,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error(`Error inserting into users table:`, error);
      throw error;
    }

    console.log(`Successfully created ${role}:`, data);
    return {
      success: true,
      message: `${role.charAt(0).toUpperCase() + role.slice(1)} created successfully`,
      user: data,
    };
  } catch (error: any) {
    console.error(`Error creating user directly:`, error);
    throw error;
  }
};
