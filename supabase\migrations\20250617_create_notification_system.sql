-- Create assignment notifications system

-- Assignment notifications table
CREATE TABLE IF NOT EXISTS assignment_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  type VA<PERSON>HA<PERSON>(50) NOT NULL CHECK (type IN ('deadline_reminder', 'submission_received', 'grade_released', 'late_submission', 'missing_submission')),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  assignment_id UUID NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
  assignment_title VARCHAR(255) NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  student_name VARCHAR(255),
  is_read BOOLEAN DEFAULT FALSE,
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  action_required BOOLEAN DEFAULT FALSE,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
  preferences JSONB NOT NULL DEFAULT '{
    "deadlineReminders": {
      "enabled": true,
      "timing": "2hours"
    },
    "submissionNotifications": {
      "enabled": true,
      "immediate": true,
      "digest": false
    },
    "gradeNotifications": {
      "enabled": true,
      "immediate": true
    },
    "lateSubmissionAlerts": {
      "enabled": true,
      "teacherOnly": true
    },
    "missingSubmissionReminders": {
      "enabled": true,
      "frequency": "daily"
    },
    "emailNotifications": {
      "enabled": false,
      "types": []
    },
    "pushNotifications": {
      "enabled": true,
      "quiet_hours": {
        "enabled": true,
        "start": "22:00",
        "end": "07:00"
      }
    }
  }'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification delivery log table
CREATE TABLE IF NOT EXISTS notification_delivery_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID NOT NULL REFERENCES assignment_notifications(id) ON DELETE CASCADE,
  delivery_method VARCHAR(50) NOT NULL CHECK (delivery_method IN ('push', 'email', 'in_app')),
  status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'failed')),
  error_message TEXT,
  delivered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_user_id ON assignment_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_assignment_id ON assignment_notifications(assignment_id);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_type ON assignment_notifications(type);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_is_read ON assignment_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_created_at ON assignment_notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_scheduled_for ON assignment_notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_notification_id ON notification_delivery_log(notification_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_assignment_notifications_updated_at 
  BEFORE UPDATE ON assignment_notifications 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at 
  BEFORE UPDATE ON notification_preferences 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies for assignment_notifications
ALTER TABLE assignment_notifications ENABLE ROW LEVEL SECURITY;

-- Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON assignment_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = assignment_notifications.user_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Users can insert notifications (for system-generated notifications)
CREATE POLICY "System can create notifications" ON assignment_notifications
  FOR INSERT WITH CHECK (true);

-- Users can update their own notifications (mark as read)
CREATE POLICY "Users can update their own notifications" ON assignment_notifications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = assignment_notifications.user_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Users can delete their own notifications
CREATE POLICY "Users can delete their own notifications" ON assignment_notifications
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = assignment_notifications.user_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- RLS Policies for notification_preferences
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

-- Users can view their own preferences
CREATE POLICY "Users can view their own preferences" ON notification_preferences
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = notification_preferences.user_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Users can insert their own preferences
CREATE POLICY "Users can create their own preferences" ON notification_preferences
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = notification_preferences.user_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Users can update their own preferences
CREATE POLICY "Users can update their own preferences" ON notification_preferences
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = notification_preferences.user_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- RLS Policies for notification_delivery_log
ALTER TABLE notification_delivery_log ENABLE ROW LEVEL SECURITY;

-- System can manage delivery logs
CREATE POLICY "System can manage delivery logs" ON notification_delivery_log
  FOR ALL USING (true);

-- Function to automatically create notification preferences for new users
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO notification_preferences (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create default preferences for new users
CREATE TRIGGER create_user_notification_preferences
  AFTER INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION create_default_notification_preferences();

-- Function to clean up old notifications (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
  DELETE FROM assignment_notifications 
  WHERE created_at < NOW() - INTERVAL '30 days'
  AND is_read = true;
  
  DELETE FROM notification_delivery_log 
  WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Function to get unread notification count for a user
CREATE OR REPLACE FUNCTION get_unread_notification_count(user_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM assignment_notifications
    WHERE user_id = user_uuid
    AND is_read = false
  );
END;
$$ LANGUAGE plpgsql;

-- Function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_read(user_uuid UUID)
RETURNS void AS $$
BEGIN
  UPDATE assignment_notifications
  SET is_read = true, updated_at = NOW()
  WHERE user_id = user_uuid
  AND is_read = false;
END;
$$ LANGUAGE plpgsql;
