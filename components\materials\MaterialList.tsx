import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Material } from '@/stores/materialStore';
import React from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface MaterialListProps {
  materials: Material[];
  onSelectMaterial: (materialId: string) => void;
  refreshControl?: React.ReactElement;
}

export function MaterialList({ materials, onSelectMaterial, refreshControl }: MaterialListProps) {
  const isDark = useColorScheme() === 'dark';

  const renderMaterialItem = React.useCallback(({ item, index }: { item: Material; index: number }) => (
    <Animated.View
      entering={FadeInDown.duration(600).delay(index * 100)}
      className={`p-4 rounded-xl mb-3 ${
        isDark ? 'bg-dark-surface' : 'bg-light-surface'
      }`}
      style={{
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}
    >
      <TouchableOpacity onPress={() => onSelectMaterial(item.id)}>
        <View className="flex-row items-start justify-between mb-2">
          <View className="flex-1 mr-3">
            <Text className={`font-rubik-bold text-lg ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`} numberOfLines={2}>
              {item.title}
            </Text>
            {item.description && (
              <Text className={`font-rubik text-sm mt-1 ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`} numberOfLines={2}>
                {item.description}
              </Text>
            )}
          </View>
          <View className="flex-row items-center">
            {item.gemini_generated && (
              <View className="bg-purple-100 px-2 py-1 rounded-full mr-2">
                <Text className="text-purple-700 text-xs font-rubik-medium">AI</Text>
              </View>
            )}
            <View className={`px-2 py-1 rounded-full ${
              item.material_type === 'lesson_plan' ? 'bg-blue-100' :
              item.material_type === 'worksheet' ? 'bg-green-100' :
              item.material_type === 'quiz' ? 'bg-orange-100' :
              item.material_type === 'resource' ? 'bg-gray-100' :
              item.material_type === 'assignment' ? 'bg-red-100' :
              'bg-indigo-100'
            }`}>
              <Text className={`text-xs font-rubik-medium ${
                item.material_type === 'lesson_plan' ? 'text-blue-700' :
                item.material_type === 'worksheet' ? 'text-green-700' :
                item.material_type === 'quiz' ? 'text-orange-700' :
                item.material_type === 'resource' ? 'text-gray-700' :
                item.material_type === 'assignment' ? 'text-red-700' :
                'text-indigo-700'
              }`}>
                {item.material_type.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
          </View>
        </View>

        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            {item.subject && (
              <Text className={`text-sm font-rubik-medium mr-4 ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                {item.subject}
              </Text>
            )}
            {item.grade_level && (
              <Text className={`text-sm font-rubik-medium mr-4 ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                {item.grade_level}
              </Text>
            )}
            <View className="flex-row items-center mr-4">
              <IconSymbol name="eye.fill" size={14} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`text-sm font-rubik ml-1 ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                {item.view_count}
              </Text>
            </View>
            <View className="flex-row items-center">
              <IconSymbol name="arrow.down.circle.fill" size={14} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`text-sm font-rubik ml-1 ${
                isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
              }`}>
                {item.download_count}
              </Text>
            </View>
          </View>
        </View>

        {item.uploaded_by_name && (
          <View className="mt-2 pt-2 border-t border-gray-200">
            <Text className={`text-xs font-rubik ${
              isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
            }`}>
              Shared by {item.uploaded_by_name}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  ), [isDark, onSelectMaterial]);

  return (
    <FlatList
      data={materials}
      renderItem={renderMaterialItem}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      refreshControl={refreshControl}
      contentContainerClassName="pb-4"
    />
  );
}