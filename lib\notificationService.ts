import { supabase } from './supabase';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface AssignmentNotification {
  id: string;
  type: 'deadline_reminder' | 'submission_received' | 'grade_released' | 'late_submission' | 'missing_submission';
  title: string;
  message: string;
  assignmentId: string;
  assignmentTitle: string;
  userId: string;
  studentId?: string;
  studentName?: string;
  createdAt: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  actionRequired?: boolean;
  scheduledFor?: string;
}

export interface NotificationPreferences {
  deadlineReminders: {
    enabled: boolean;
    timing: '1hour' | '2hours' | '1day' | '2days';
  };
  submissionNotifications: {
    enabled: boolean;
    immediate: boolean;
    digest: boolean;
  };
  gradeNotifications: {
    enabled: boolean;
    immediate: boolean;
  };
  lateSubmissionAlerts: {
    enabled: boolean;
    teacherOnly: boolean;
  };
  missingSubmissionReminders: {
    enabled: boolean;
    frequency: 'daily' | 'weekly';
  };
  emailNotifications: {
    enabled: boolean;
    types: string[];
  };
  pushNotifications: {
    enabled: boolean;
    quiet_hours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
}

class NotificationService {
  private static instance: NotificationService;

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      return finalStatus === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  // Schedule a local notification
  async scheduleLocalNotification(
    title: string,
    body: string,
    data: any,
    scheduledDate?: Date
  ): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.warn('Notification permissions not granted');
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: true,
        },
        trigger: scheduledDate ? { date: scheduledDate } : null,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  }

  // Create assignment deadline reminder
  async createDeadlineReminder(
    assignmentId: string,
    assignmentTitle: string,
    dueDate: string,
    userId: string,
    preferences: NotificationPreferences
  ): Promise<void> {
    try {
      if (!preferences.deadlineReminders.enabled) return;

      const dueDateObj = new Date(dueDate);
      const now = new Date();

      // Calculate reminder time based on preferences
      let reminderTime = new Date(dueDateObj);
      switch (preferences.deadlineReminders.timing) {
        case '1hour':
          reminderTime.setHours(reminderTime.getHours() - 1);
          break;
        case '2hours':
          reminderTime.setHours(reminderTime.getHours() - 2);
          break;
        case '1day':
          reminderTime.setDate(reminderTime.getDate() - 1);
          break;
        case '2days':
          reminderTime.setDate(reminderTime.getDate() - 2);
          break;
      }

      // Only schedule if reminder time is in the future
      if (reminderTime > now) {
        // Create notification record
        const notification: Partial<AssignmentNotification> = {
          type: 'deadline_reminder',
          title: 'Assignment Due Soon',
          message: `${assignmentTitle} is due soon`,
          assignmentId,
          assignmentTitle,
          userId,
          priority: 'high',
          actionRequired: true,
          scheduledFor: reminderTime.toISOString(),
          isRead: false,
          createdAt: new Date().toISOString(),
        };

        // Save to database
        await this.saveNotification(notification);

        // Schedule local notification
        if (preferences.pushNotifications.enabled) {
          await this.scheduleLocalNotification(
            notification.title!,
            notification.message!,
            { assignmentId, type: 'deadline_reminder' },
            reminderTime
          );
        }
      }
    } catch (error) {
      console.error('Error creating deadline reminder:', error);
    }
  }

  // Create submission notification for teachers
  async createSubmissionNotification(
    assignmentId: string,
    assignmentTitle: string,
    studentId: string,
    studentName: string,
    teacherId: string,
    isLate: boolean = false
  ): Promise<void> {
    try {
      const preferences = await this.getUserPreferences(teacherId);
      
      if (!preferences.submissionNotifications.enabled) return;

      const notification: Partial<AssignmentNotification> = {
        type: isLate ? 'late_submission' : 'submission_received',
        title: isLate ? 'Late Submission Received' : 'New Submission Received',
        message: `${studentName} ${isLate ? 'submitted late' : 'submitted'} ${assignmentTitle}`,
        assignmentId,
        assignmentTitle,
        userId: teacherId,
        studentId,
        studentName,
        priority: isLate ? 'high' : 'medium',
        isRead: false,
        createdAt: new Date().toISOString(),
      };

      // Save to database
      await this.saveNotification(notification);

      // Send immediate notification if enabled
      if (preferences.submissionNotifications.immediate && preferences.pushNotifications.enabled) {
        await this.scheduleLocalNotification(
          notification.title!,
          notification.message!,
          { assignmentId, studentId, type: notification.type }
        );
      }
    } catch (error) {
      console.error('Error creating submission notification:', error);
    }
  }

  // Create grade release notification for students
  async createGradeNotification(
    assignmentId: string,
    assignmentTitle: string,
    studentId: string,
    grade: number,
    maxPoints: number
  ): Promise<void> {
    try {
      const preferences = await this.getUserPreferences(studentId);
      
      if (!preferences.gradeNotifications.enabled) return;

      const percentage = Math.round((grade / maxPoints) * 100);
      
      const notification: Partial<AssignmentNotification> = {
        type: 'grade_released',
        title: 'Grade Available',
        message: `Your grade for ${assignmentTitle} is now available (${percentage}%)`,
        assignmentId,
        assignmentTitle,
        userId: studentId,
        priority: 'low',
        isRead: false,
        createdAt: new Date().toISOString(),
      };

      // Save to database
      await this.saveNotification(notification);

      // Send immediate notification if enabled
      if (preferences.gradeNotifications.immediate && preferences.pushNotifications.enabled) {
        await this.scheduleLocalNotification(
          notification.title!,
          notification.message!,
          { assignmentId, grade, type: 'grade_released' }
        );
      }
    } catch (error) {
      console.error('Error creating grade notification:', error);
    }
  }

  // Create missing submission reminder
  async createMissingSubmissionReminder(
    assignmentId: string,
    assignmentTitle: string,
    teacherId: string,
    missingCount: number
  ): Promise<void> {
    try {
      const preferences = await this.getUserPreferences(teacherId);
      
      if (!preferences.missingSubmissionReminders.enabled) return;

      const notification: Partial<AssignmentNotification> = {
        type: 'missing_submission',
        title: 'Missing Submissions',
        message: `${missingCount} students have not submitted ${assignmentTitle}`,
        assignmentId,
        assignmentTitle,
        userId: teacherId,
        priority: 'high',
        actionRequired: true,
        isRead: false,
        createdAt: new Date().toISOString(),
      };

      // Save to database
      await this.saveNotification(notification);

      // Send notification
      if (preferences.pushNotifications.enabled) {
        await this.scheduleLocalNotification(
          notification.title!,
          notification.message!,
          { assignmentId, missingCount, type: 'missing_submission' }
        );
      }
    } catch (error) {
      console.error('Error creating missing submission reminder:', error);
    }
  }

  // Save notification to database
  private async saveNotification(notification: Partial<AssignmentNotification>): Promise<void> {
    try {
      const { error } = await supabase
        .from('assignment_notifications')
        .insert([notification]);

      if (error) {
        console.error('Error saving notification to database:', error);
      }
    } catch (error) {
      console.error('Error saving notification:', error);
    }
  }

  // Get user notification preferences
  private async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('preferences')
        .eq('user_id', userId)
        .single();

      if (error || !data) {
        // Return default preferences
        return this.getDefaultPreferences();
      }

      return data.preferences;
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  // Get default notification preferences
  private getDefaultPreferences(): NotificationPreferences {
    return {
      deadlineReminders: {
        enabled: true,
        timing: '2hours',
      },
      submissionNotifications: {
        enabled: true,
        immediate: true,
        digest: false,
      },
      gradeNotifications: {
        enabled: true,
        immediate: true,
      },
      lateSubmissionAlerts: {
        enabled: true,
        teacherOnly: true,
      },
      missingSubmissionReminders: {
        enabled: true,
        frequency: 'daily',
      },
      emailNotifications: {
        enabled: false,
        types: [],
      },
      pushNotifications: {
        enabled: true,
        quiet_hours: {
          enabled: true,
          start: '22:00',
          end: '07:00',
        },
      },
    };
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('assignment_notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // Get notifications for user
  async getNotifications(userId: string, limit: number = 50): Promise<AssignmentNotification[]> {
    try {
      const { data, error } = await supabase
        .from('assignment_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }
}

export default NotificationService.getInstance();
