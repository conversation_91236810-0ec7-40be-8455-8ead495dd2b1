import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, View } from 'react-native';
import { StatsCard } from './StatsCard';

interface TeacherStats {
  totalClasses: number;
  totalStudents: number;
  pendingAssignments: number;
  todayAttendance: number;
}

interface StatsOverviewProps {
  stats: TeacherStats;
  onNavigate: (route: string) => void;
}

export const StatsOverview: React.FC<StatsOverviewProps> = ({ 
  stats, 
  onNavigate 
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <View className="mb-6">
      {/* Section Title */}
      <Text 
        className={`
          text-lg font-rubik-semibold mb-4
          ${isDark ? 'text-dark-text' : 'text-light-text'}
        `}
      >
        Today&apos;s Overview
      </Text>
      
      {/* Stats Grid */}
      <View className="flex-row -mx-1">
        <StatsCard
          title="Students"
          value={stats.totalStudents}
          icon="person.2.fill"
          onPress={() => onNavigate('/enrollment')}
        />
        <StatsCard
          title="Present Today"
          value={stats.todayAttendance}
          icon="checkmark.circle.fill"
          onPress={() => onNavigate('/attendance')}
        />
      </View>
      
      <View className="flex-row -mx-1 mt-2">
        <StatsCard
          title="Classes"
          value={stats.totalClasses}
          icon="book.fill"
          onPress={() => onNavigate('/notices')}
        />
        <StatsCard
          title="Notifications"
          value={stats.pendingAssignments}
          icon="megaphone.fill"
          onPress={() => onNavigate('/notices')}
        />
      </View>
    </View>
  );
};
