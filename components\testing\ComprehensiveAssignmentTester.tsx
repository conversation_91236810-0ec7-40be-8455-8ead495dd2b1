import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
    AssignmentTestRunner,
    TestSuite,
    cleanupTestData,
    createTestAssignment,
    createTestClass,
    createTestRubric,
    createTestStudent,
    createTestSubmission,
    generateTestReport,
    testDatabaseConnection,
    testTeacherRecord,
    testUserAuthentication,
} from '@/lib/testUtils';
import { useAssignmentStore } from '@/stores/assignmentStore';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function ComprehensiveAssignmentTester() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [testResults, setTestResults] = useState<TestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  const {
    createAssignment,
    updateAssignment,
    deleteAssignment,
    publishAssignment,
    closeAssignment,
    fetchAssignments,
    fetchSubmissions,
    gradeSubmission,
    generateAIFeedback,
    exportAssignment,
    bulkPublishAssignments,
  } = useAssignmentStore();

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    const runner = new AssignmentTestRunner();
    const cleanupIds: any = {
      classIds: [],
      assignmentIds: [],
      studentIds: [],
      userIds: [],
      submissionIds: [],
      rubricIds: [],
    };

    try {
      // Test 1: Database and Authentication
      setCurrentTest('Testing Database and Authentication...');
      runner.startSuite('Database and Authentication');
      
      await runner.addTest('Database Connection', testDatabaseConnection);
      await runner.addTest('User Authentication', testUserAuthentication);
      
      let teacher: any;
      await runner.addTest('Teacher Record', async () => {
        teacher = await testTeacherRecord();
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 2: Class Management
      setCurrentTest('Testing Class Management...');
      runner.startSuite('Class Management');
      
      let testClass: any;
      await runner.addTest('Create Test Class', async () => {
        testClass = await createTestClass(teacher.id, teacher.tenant_id);
        cleanupIds.classIds.push(testClass.id);
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 3: Assignment CRUD Operations
      setCurrentTest('Testing Assignment CRUD Operations...');
      runner.startSuite('Assignment CRUD Operations');
      
      let testAssignment: any;
      await runner.addTest('Create Assignment via Store', async () => {
        const assignmentData = {
          title: `Test Assignment ${Date.now()}`,
          description: 'Test assignment for system testing',
          instructions: 'Complete this test assignment',
          class_id: testClass.id,
          max_points: 100,
          due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          assignment_type: 'essay',
          difficulty: 'medium',
          number_of_questions: 5,
        };
        
        testAssignment = await createAssignment(assignmentData);
        cleanupIds.assignmentIds.push(testAssignment.id);
      });

      await runner.addTest('Update Assignment', async () => {
        const updates = {
          title: `Updated Test Assignment ${Date.now()}`,
          description: 'Updated description for testing',
        };
        await updateAssignment(testAssignment.id, updates);
      });

      await runner.addTest('Fetch Assignments', async () => {
        await fetchAssignments(testClass.id);
      });

      await runner.addTest('Publish Assignment', async () => {
        await publishAssignment(testAssignment.id);
      });

      await runner.addTest('Close Assignment', async () => {
        await closeAssignment(testAssignment.id);
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 4: Student and Submission Management
      setCurrentTest('Testing Student and Submission Management...');
      runner.startSuite('Student and Submission Management');
      
      let testStudent: any;
      let testSubmission: any;
      
      await runner.addTest('Create Test Student', async () => {
        testStudent = await createTestStudent(testClass.id, teacher.tenant_id);
        cleanupIds.studentIds.push(testStudent.student.id);
        cleanupIds.userIds.push(testStudent.user.id);
      });

      await runner.addTest('Create Test Submission', async () => {
        testSubmission = await createTestSubmission(
          testAssignment.id,
          testStudent.student.id,
          teacher.tenant_id
        );
        cleanupIds.submissionIds.push(testSubmission.id);
      });

      await runner.addTest('Fetch Submissions', async () => {
        await fetchSubmissions(testAssignment.id);
      });

      await runner.addTest('Grade Submission', async () => {
        await gradeSubmission(testSubmission.id, {
          grade: 85,
          feedback: 'Good work! This is test feedback.',
        });
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 5: Rubrics System
      setCurrentTest('Testing Rubrics System...');
      runner.startSuite('Rubrics System');
      
      let testRubric: any;
      await runner.addTest('Create Test Rubric', async () => {
        testRubric = await createTestRubric(testAssignment.id, teacher.tenant_id);
        cleanupIds.rubricIds.push(testRubric.id);
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 6: AI Features (if API key available)
      setCurrentTest('Testing AI Features...');
      runner.startSuite('AI Features');
      
      await runner.addTest('Generate AI Feedback', async () => {
        try {
          await generateAIFeedback(testSubmission.id);
        } catch (error) {
          // If no API key, this is expected to fail
          if (error.message.includes('API key')) {
            console.log('AI test skipped - no API key configured');
            return; // Pass the test
          }
          throw error;
        }
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 7: Export Functionality
      setCurrentTest('Testing Export Functionality...');
      runner.startSuite('Export Functionality');
      
      await runner.addTest('Export Assignment Data', async () => {
        await exportAssignment(testAssignment.id, 'json');
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 8: Bulk Operations
      setCurrentTest('Testing Bulk Operations...');
      runner.startSuite('Bulk Operations');
      
      await runner.addTest('Bulk Publish Assignments', async () => {
        // Create another test assignment for bulk operations
        const bulkAssignment = await createTestAssignment(
          testClass.id,
          teacher.id,
          teacher.tenant_id
        );
        cleanupIds.assignmentIds.push(bulkAssignment.id);
        
        await bulkPublishAssignments([bulkAssignment.id]);
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      // Test 9: Data Cleanup
      setCurrentTest('Cleaning up test data...');
      runner.startSuite('Data Cleanup');
      
      await runner.addTest('Delete Test Assignment', async () => {
        await deleteAssignment(testAssignment.id);
      });

      await runner.addTest('Cleanup Test Data', async () => {
        await cleanupTestData(cleanupIds);
      });
      
      setTestResults(prev => [...prev, runner.finishSuite()]);

      setCurrentTest('All tests completed!');
      
      // Generate and show report
      const allResults = [...testResults, runner.finishSuite()];
      const report = generateTestReport(allResults);
      console.log('Test Report:', report);
      
      Alert.alert(
        'Tests Completed',
        `All tests have been completed. Check the results below.`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('Test execution error:', error);
      Alert.alert(
        'Test Error',
        `An error occurred during testing: ${error.message}`,
        [{ text: 'OK' }]
      );
      
      // Attempt cleanup even if tests failed
      try {
        await cleanupTestData(cleanupIds);
      } catch (cleanupError) {
        console.warn('Cleanup error:', cleanupError);
      }
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return { name: 'checkmark.circle.fill' as const, color: '#10B981' };
      case 'fail':
        return { name: 'xmark.circle.fill' as const, color: '#EF4444' };
      case 'running':
        return { name: 'clock.fill' as const, color: '#F59E0B' };
      default:
        return { name: 'circle' as const, color: '#6B7280' };
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return isDark ? 'text-green-400' : 'text-green-600';
      case 'fail':
        return isDark ? 'text-red-400' : 'text-red-600';
      case 'running':
        return isDark ? 'text-yellow-400' : 'text-yellow-600';
      default:
        return isDark ? 'text-gray-400' : 'text-gray-600';
    }
  };

  return (
    <ScrollView className={`flex-1 p-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <Text className={`text-2xl font-rubik-bold mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
        Comprehensive Assignment System Tester
      </Text>

      <Text className={`text-sm font-rubik mb-6 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
        Complete testing of all assignment system features including CRUD operations,
        grading, AI features, exports, and bulk operations.
      </Text>

      {/* Run Tests Button */}
      <TouchableOpacity
        onPress={runAllTests}
        disabled={isRunning}
        className={`p-4 rounded-xl mb-6 ${
          isRunning ? 'bg-gray-400' : 'bg-primary-500'
        }`}
      >
        {isRunning ? (
          <View className="flex-row items-center justify-center">
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text className="text-white font-rubik-bold ml-2">Running Tests...</Text>
          </View>
        ) : (
          <Text className="text-white font-rubik-bold text-center">Run All Tests</Text>
        )}
      </TouchableOpacity>

      {/* Current Test Status */}
      {isRunning && currentTest && (
        <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
          <Text className={`font-rubik-medium ${isDark ? 'text-blue-300' : 'text-blue-800'}`}>
            {currentTest}
          </Text>
        </View>
      )}

      {/* Test Results */}
      {testResults.map((suite, index) => (
        <View
          key={index}
          className={`mb-4 rounded-xl border ${isDark ? 'border-gray-700 bg-dark-card' : 'border-gray-200 bg-white'}`}
        >
          <View className="p-4 border-b border-gray-200 dark:border-gray-700">
            <View className="flex-row items-center justify-between">
              <Text className={`font-rubik-bold text-lg ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {suite.name}
              </Text>
              <View className="flex-row items-center">
                <IconSymbol
                  name={getStatusIcon(suite.status).name}
                  size={20}
                  color={getStatusIcon(suite.status).color}
                />
                <Text className={`font-rubik-medium ml-2 ${getStatusColor(suite.status)}`}>
                  {suite.status.toUpperCase()}
                </Text>
              </View>
            </View>
            {suite.duration && (
              <Text className={`text-sm font-rubik mt-1 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Duration: {suite.duration}ms
              </Text>
            )}
          </View>

          <View className="p-4">
            {suite.tests.map((test, testIndex) => (
              <View key={testIndex} className="flex-row items-start mb-3 last:mb-0">
                <IconSymbol
                  name={getStatusIcon(test.status).name}
                  size={16}
                  color={getStatusIcon(test.status).color}
                  style={{ marginTop: 2, marginRight: 8 }}
                />
                <View className="flex-1">
                  <Text className={`font-rubik-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    {test.name}
                  </Text>
                  <Text className={`text-sm font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    {test.message}
                  </Text>
                  {test.error && (
                    <Text className={`text-sm font-rubik mt-1 ${isDark ? 'text-red-400' : 'text-red-600'}`}>
                      Error: {test.error}
                    </Text>
                  )}
                  {test.duration && (
                    <Text className={`text-xs font-rubik mt-1 ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>
                      {test.duration}ms
                    </Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </View>
      ))}

      {/* Test Summary */}
      {testResults.length > 0 && !isRunning && (
        <View className={`p-4 rounded-xl ${isDark ? 'bg-gray-800' : 'bg-gray-100'}`}>
          <Text className={`font-rubik-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
            Test Summary
          </Text>
          <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Total Suites: {testResults.length}
          </Text>
          <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Passed: {testResults.filter(s => s.status === 'pass').length}
          </Text>
          <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Failed: {testResults.filter(s => s.status === 'fail').length}
          </Text>
          <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Total Tests: {testResults.reduce((sum, suite) => sum + suite.tests.length, 0)}
          </Text>
        </View>
      )}
    </ScrollView>
  );
}
