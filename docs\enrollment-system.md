# Student Enrollment System

## Overview
The Student Enrollment System allows class teachers to enroll students into their classes. Only teachers with `is_class_teacher = TRUE` can access this functionality.

## Features

### 🔐 **Access Control**
- Only class teachers can enroll students
- Proper RLS (Row Level Security) policies
- Secure API endpoints with validation

### 📝 **Enrollment Methods**
1. **Immediate Enrollment** - Direct student enrollment
2. **Enrollment Requests** - Create requests for approval workflow

### 🎯 **Core Functionality**
- Create and manage enrollment requests
- Approve/reject enrollment requests
- Direct student enrollment
- View enrolled students by class
- Remove students from classes
- Transfer students between classes

### 📱 **User Interface**
- **Enroll Tab** - Create new enrollments
- **Requests Tab** - Manage enrollment requests
- **Students Tab** - View enrolled students

## Database Schema

### New Tables Created

#### `enrollment_requests`
```sql
CREATE TABLE enrollment_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  teacher_id UUID NOT NULL REFERENCES teachers(id),
  class_id UUID NOT NULL REFERENCES classes(id),
  student_name VARCHAR(255) NOT NULL,
  student_email VARCHAR(255) NOT NULL,
  student_grade VARCHAR(50),
  student_section VARCHAR(50),
  roll_number VARCHAR(50),
  date_of_birth DATE,
  parent_name VARCHAR(255),
  parent_email VARCHAR(255),
  parent_phone VARCHAR(20),
  enrollment_code VARCHAR(50) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);
```

#### `class_students`
```sql
CREATE TABLE class_students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  class_id UUID NOT NULL REFERENCES classes(id),
  student_id UUID NOT NULL REFERENCES students(id),
  enrollment_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  UNIQUE(class_id, student_id)
);
```

### Modified Tables

#### `teachers`
- Added `is_class_teacher BOOLEAN DEFAULT FALSE`

#### `students`
- Added `enrollment_code VARCHAR(50) UNIQUE`

#### `classes`
- Added `grade VARCHAR(50)`
- Added `section VARCHAR(50)`

## Components Architecture

### 📁 **Component Structure**
```
components/enrollment/
├── EnrollmentForm.tsx          # Student enrollment form
├── EnrollmentRequestList.tsx   # List of enrollment requests
├── ClassSelector.tsx           # Class selection modal
├── StudentList.tsx             # List of enrolled students
└── index.ts                    # Barrel exports
```

### 🏪 **State Management (Zustand)**
```typescript
// stores/enrollmentStore.ts
interface EnrollmentState {
  enrollmentRequests: EnrollmentRequest[];
  classStudents: ClassStudent[];
  availableClasses: Class[];
  currentTeacher: Teacher | null;
  isLoading: boolean;
  error: string | null;
  refreshing: boolean;
  
  // Actions
  loadTeacherData: (clerkUserId: string) => Promise<void>;
  createEnrollmentRequest: (request) => Promise<EnrollmentRequest | null>;
  approveEnrollmentRequest: (requestId: string) => Promise<void>;
  enrollStudent: (classId: string, studentData) => Promise<Student | null>;
  // ... more actions
}
```

### 💾 **Caching System**
- **Memory Cache** - Fast access to frequently used data
- **AsyncStorage Cache** - Persistent storage across app sessions
- **Query Deduplication** - Prevents duplicate API calls
- **Smart Invalidation** - Automatic cache updates

#### Cache Configuration
```typescript
ENROLLMENT_REQUESTS: 2 * 60 * 1000, // 2 minutes
CLASS_STUDENTS: 5 * 60 * 1000,      // 5 minutes
```

## Security Features

### 🔒 **Row Level Security (RLS)**
- Teachers can only access their own classes
- Students can only be enrolled by class teachers
- Proper tenant isolation

### 🛡️ **Access Control**
```sql
-- Only class teachers can insert enrollment requests
CREATE POLICY "Class teachers can insert enrollment requests" 
ON enrollment_requests FOR INSERT WITH CHECK (
  teacher_id = (
    SELECT id FROM teachers 
    WHERE clerk_user_id = current_setting('app.clerk_user_id')::TEXT
    AND is_class_teacher = TRUE
  )
);
```

### 🔐 **API Security**
- Secure API wrapper with rate limiting
- Input validation and sanitization
- Error handling and logging

## Usage Workflow

### 1. **Teacher Access Check**
```typescript
// Only class teachers can access enrollment
if (currentTeacher && !currentTeacher.is_class_teacher) {
  // Show access restricted message
}
```

### 2. **Class Selection**
```typescript
// Select class for enrollment
<ClassSelector
  selectedClass={selectedClass}
  onClassSelect={handleClassSelect}
  onCancel={() => setModalType(null)}
/>
```

### 3. **Student Enrollment**
```typescript
// Two enrollment methods
if (enrollmentType === 'immediate') {
  // Direct enrollment
  const student = await enrollStudent(selectedClass.id, studentData);
} else {
  // Create enrollment request
  const request = await createEnrollmentRequest(requestData);
}
```

### 4. **Request Management**
```typescript
// Approve enrollment request
await approveEnrollmentRequest(requestId);

// Reject enrollment request
await rejectEnrollmentRequest(requestId, reason);
```

## API Endpoints

### Enrollment Requests
- `GET /enrollment_requests` - List requests
- `POST /enrollment_requests` - Create request
- `PATCH /enrollment_requests/:id` - Update request

### Students
- `POST /students` - Create student
- `GET /students` - List students
- `PATCH /students/:id` - Update student

### Class Students
- `GET /class_students` - List class students
- `POST /class_students` - Add student to class
- `PATCH /class_students/:id` - Update enrollment status

## Error Handling

### 🚨 **Error Types**
- **Access Denied** - Non-class teachers
- **Validation Errors** - Invalid form data
- **Database Errors** - Connection/query issues
- **Network Errors** - API call failures

### 🔄 **Recovery Mechanisms**
- Automatic retry for network errors
- Cache fallback for offline scenarios
- User-friendly error messages
- Detailed error logging

## Performance Optimizations

### ⚡ **Caching Strategy**
- Multi-level caching (memory + storage)
- Smart cache invalidation
- Background data refresh
- Query deduplication

### 📊 **Data Loading**
- Lazy loading of student lists
- Pagination for large datasets
- Optimistic updates for better UX
- Background sync

## Testing Considerations

### 🧪 **Test Cases**
1. **Access Control**
   - Non-class teachers cannot access enrollment
   - Class teachers can access all features

2. **Enrollment Flow**
   - Immediate enrollment works correctly
   - Request-based enrollment creates proper requests
   - Approval workflow functions properly

3. **Data Validation**
   - Form validation prevents invalid submissions
   - Email format validation
   - Required field validation

4. **Error Scenarios**
   - Network failures are handled gracefully
   - Invalid data submissions show proper errors
   - Cache failures don't break the app

## Future Enhancements

### 🚀 **Planned Features**
- Bulk student enrollment via CSV import
- Student transfer between schools
- Parent notification system
- Enrollment analytics and reporting
- QR code generation for enrollment codes
- Integration with student information systems

### 📈 **Scalability Improvements**
- Database indexing optimization
- API response pagination
- Real-time updates via WebSockets
- Advanced caching strategies

## Deployment Notes

### 📋 **Migration Steps**
1. Run database migration: `20250119_add_enrollment_system.sql`
2. Update teacher records to set `is_class_teacher` flag
3. Deploy new app version with enrollment features
4. Test enrollment workflow in staging environment

### ⚙️ **Configuration**
- Ensure RLS policies are enabled
- Configure cache TTL values appropriately
- Set up proper error monitoring
- Configure rate limiting for API endpoints

## Conclusion

The Student Enrollment System provides a comprehensive solution for managing student enrollments with proper security, caching, and user experience considerations. The modular component architecture ensures maintainability and extensibility for future enhancements.
