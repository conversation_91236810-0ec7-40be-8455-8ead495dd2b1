import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';

// Stores
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

interface FormData {
  class_id: string;
  subject: string;
  session_date: string;
  session_time: string;
  session_type: 'regular' | 'exam' | 'lab' | 'assembly';
  notes: string;
}

interface FormErrors {
  class_id?: string;
  subject?: string;
  session_date?: string;
  session_time?: string;
}

const CreateSessionScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const { createSession, isSaving, error, clearError } = useAttendanceStore();
  const { currentTeacher, availableClasses, loadAvailableClasses } = useEnrollmentStore();

  const [formData, setFormData] = useState<FormData>({
    class_id: '',
    subject: '',
    session_date: new Date().toISOString().split('T')[0], // Today's date
    session_time: new Date().toTimeString().slice(0, 5), // Current time
    session_type: 'regular',
    notes: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Load classes when component mounts
  useEffect(() => {
    if (currentTeacher?.id && (!availableClasses || availableClasses.length === 0)) {
      loadAvailableClasses(currentTeacher.id);
    }
  }, [currentTeacher?.id, availableClasses, loadAvailableClasses]);

  // Auto-select first class if only one available
  useEffect(() => {
    if (availableClasses && availableClasses.length === 1 && !formData.class_id) {
      setFormData(prev => ({ ...prev, class_id: availableClasses[0].id }));
    }
  }, [availableClasses, formData.class_id]);

  const updateFormData = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.class_id) {
      newErrors.class_id = 'Please select a class';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.session_date) {
      newErrors.session_date = 'Session date is required';
    }

    if (!formData.session_time) {
      newErrors.session_time = 'Session time is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (!currentTeacher?.id) {
      Alert.alert('Error', 'Teacher information not found');
      return;
    }

    try {
      clearError();

      // Get selected class to determine total students
      const selectedClass = availableClasses?.find(c => c.id === formData.class_id);
      const totalStudents = selectedClass?.student_count || 0;

      const session = await createSession({
        class_id: formData.class_id,
        teacher_id: currentTeacher.id,
        subject: formData.subject.trim(),
        session_date: formData.session_date,
        session_time: formData.session_time,
        session_type: formData.session_type,
        status: 'active',
        total_students: totalStudents,
        notes: formData.notes.trim() || undefined,
      });

      if (session) {
        Alert.alert(
          'Session Created',
          'Attendance session has been created successfully.',
          [
            {
              text: 'Take Attendance',
              onPress: () => {
                router.replace(`/attendance/take-attendance?sessionId=${session.id}`);
              }
            },
            {
              text: 'Back to Attendance',
              onPress: () => router.back(),
              style: 'cancel'
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error creating session:', error);
      Alert.alert('Error', 'Failed to create session. Please try again.');
    }
  };

  const sessionTypes = [
    { value: 'regular', label: 'Regular Class' },
    { value: 'exam', label: 'Examination' },
    { value: 'lab', label: 'Laboratory' },
    { value: 'assembly', label: 'Assembly' },
  ];

  if (!currentTeacher) {
    return <LoadingScreen message="Loading teacher data..." />;
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {/* Class Selection */}
        <View className="mb-6">
          <Text className={`font-rubik-semibold mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Session Details
          </Text>

          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Class *
            </Text>
            {availableClasses && availableClasses.length > 0 ? (
              <View>
                {availableClasses.map((classItem) => (
                  <TouchableOpacity
                    key={classItem.id}
                    onPress={() => updateFormData('class_id', classItem.id)}
                    className={`p-4 rounded-lg mb-2 border ${
                      formData.class_id === classItem.id
                        ? 'border-primary-500 bg-primary-50'
                        : errors.class_id
                        ? 'border-error'
                        : isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
                    }`}
                  >
                    <View className="flex-row items-center justify-between">
                      <View>
                        <Text className={`font-rubik-semibold ${
                          formData.class_id === classItem.id ? 'text-primary-600' : isDark ? 'text-dark-text' : 'text-light-text'
                        }`}>
                          {classItem.name}
                        </Text>
                        <Text className={`font-rubik text-sm ${
                          formData.class_id === classItem.id ? 'text-primary-500' : isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                        }`}>
                          Grade: {classItem.grade} | Section: {classItem.section}
                        </Text>
                        <Text className={`font-rubik text-sm ${
                          formData.class_id === classItem.id ? 'text-primary-500' : isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                        }`}>
                          Students: {classItem.student_count || 0}
                        </Text>
                      </View>
                      {formData.class_id === classItem.id && (
                        <IconSymbol name="checkmark.circle.fill" size={24} color="#2563EB" />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <View className={`p-4 rounded-lg border ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'}`}>
                <Text className={`text-center font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  No classes available
                </Text>
              </View>
            )}
            {errors.class_id && (
              <Text className="text-error text-sm font-rubik mt-1">{errors.class_id}</Text>
            )}
          </View>

          {/* Subject */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Subject *
            </Text>
            <TextInput
              value={formData.subject}
              onChangeText={(value) => updateFormData('subject', value)}
              placeholder="Enter subject name"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              className={`p-4 rounded-lg font-rubik border ${
                errors.subject
                  ? 'border-error'
                  : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
            {errors.subject && (
              <Text className="text-error text-sm font-rubik mt-1">{errors.subject}</Text>
            )}
          </View>

          {/* Date and Time */}
          <View className="flex-row mb-4">
            <View className="flex-1 mr-2">
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Date *
              </Text>
              <TextInput
                value={formData.session_date}
                onChangeText={(value) => updateFormData('session_date', value)}
                placeholder="YYYY-MM-DD"
                placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                className={`p-4 rounded-lg font-rubik border ${
                  errors.session_date
                    ? 'border-error'
                    : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
                }`}
              />
              {errors.session_date && (
                <Text className="text-error text-sm font-rubik mt-1">{errors.session_date}</Text>
              )}
            </View>
            <View className="flex-1 ml-2">
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Time *
              </Text>
              <TextInput
                value={formData.session_time}
                onChangeText={(value) => updateFormData('session_time', value)}
                placeholder="HH:MM"
                placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                className={`p-4 rounded-lg font-rubik border ${
                  errors.session_time
                    ? 'border-error'
                    : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
                }`}
              />
              {errors.session_time && (
                <Text className="text-error text-sm font-rubik mt-1">{errors.session_time}</Text>
              )}
            </View>
          </View>

          {/* Session Type */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Session Type
            </Text>
            <View className="flex-row flex-wrap">
              {sessionTypes.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  onPress={() => updateFormData('session_type', type.value as FormData['session_type'])}
                  className={`px-4 py-2 rounded-lg mr-2 mb-2 border ${
                    formData.session_type === type.value
                      ? 'border-primary-500 bg-primary-50'
                      : isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
                  }`}
                >
                  <Text className={`font-rubik-medium ${
                    formData.session_type === type.value ? 'text-primary-600' : isDark ? 'text-dark-text' : 'text-light-text'
                  }`}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Notes */}
          <View className="mb-6">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Notes (Optional)
            </Text>
            <TextInput
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
              placeholder="Add any notes about this session..."
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
              className={`p-4 rounded-lg font-rubik border ${
                isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
          </View>
        </View>

        {/* Error Display */}
        {error && (
          <View className="bg-error/10 border border-error rounded-lg p-4 mb-4">
            <Text className="text-error font-rubik">{error}</Text>
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          onPress={handleSubmit}
          disabled={isSaving}
          className={`p-4 rounded-lg flex-row items-center justify-center ${
            isSaving ? 'bg-gray-400' : 'bg-primary-500'
          }`}
        >
          {isSaving ? (
            <IconSymbol name="arrow.clockwise" size={20} color="#FFFFFF" />
          ) : (
            <IconSymbol name="plus.circle.fill" size={20} color="#FFFFFF" />
          )}
          <Text className="text-white font-rubik-semibold text-base ml-2">
            {isSaving ? 'Creating Session...' : 'Create Session'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CreateSessionScreen;
