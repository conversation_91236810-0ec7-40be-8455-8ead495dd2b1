import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import * as WebBrowser from 'expo-web-browser';
import React, { useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { IconSymbol } from './ui/IconSymbol';

interface PDFViewerProps {
  fileUrl: string;
  onClose?: () => void;
}

const PDFViewer: React.FC<PDFViewerProps> = ({ fileUrl, onClose }) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Handle WebView loading state
  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  // Create a Google Docs viewer URL for the PDF
  // This is a workaround for viewing PDFs in WebView
  const getViewerUrl = (url: string) => {
    console.log('Trying to view PDF at URL:', url);

    // First try to use the direct URL with PDF.js viewer
    if (url.startsWith('https://')) {
      // Use Mozilla's PDF.js viewer which is more reliable than direct viewing
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(url)}`;
    }

    // If that doesn't work, try using Google Docs viewer as a fallback
    return `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
  };

  return (
    <View style={[styles.container, isDark ? styles.darkContainer : styles.lightContainer]}>
      {onClose && (
        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
          accessibilityLabel="Close PDF viewer"
        >
          <IconSymbol
            name="xmark.circle.fill"
            size={28}
            color={isDark ? Colors.dark.text : Colors.light.text}
          />
        </TouchableOpacity>
      )}

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDark ? Colors.dark.primary : Colors.light.primary} />
          <Text style={[styles.loadingText, isDark ? styles.darkText : styles.lightText]}>
            Loading PDF...
          </Text>
        </View>
      )}

      {hasError && (
        <View style={styles.errorContainer}>
          <IconSymbol
            name="exclamationmark.triangle.fill"
            size={48}
            color={isDark ? Colors.dark.error : Colors.light.error}
          />
          <Text style={[styles.errorText, isDark ? styles.darkText : styles.lightText]}>
            Failed to load PDF
          </Text>
          <Text style={[styles.errorSubtext, isDark ? styles.darkTextSecondary : styles.lightTextSecondary]}>
            The file may be corrupted or unavailable
          </Text>
          <Text style={[styles.errorUrl, isDark ? styles.darkTextSecondary : styles.lightTextSecondary]}>
            URL: {fileUrl.substring(0, 50)}...
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.retryButton, isDark ? styles.darkRetryButton : styles.lightRetryButton]}
              onPress={() => {
                setHasError(false);
                setIsLoading(true);
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.alternateButton, isDark ? styles.darkAlternateButton : styles.lightAlternateButton]}
              onPress={() => {
                // Try alternate viewer
                WebBrowser.openBrowserAsync(fileUrl);
              }}
            >
              <Text style={styles.alternateButtonText}>Open in Browser</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {!hasError && (
        <WebView
          source={{ uri: getViewerUrl(fileUrl) }}
          style={styles.webView}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
          startInLoadingState={true}
          renderLoading={() => null} // We handle loading state ourselves
          allowFileAccess={true}
          allowUniversalAccessFromFileURLs={true}
          allowFileAccessFromFileURLs={true}
          originWhitelist={['*']}
          javaScriptEnabled={true}
          domStorageEnabled={true}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  darkContainer: {
    backgroundColor: Colors.dark.background,
  },
  lightContainer: {
    backgroundColor: Colors.light.background,
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  errorSubtext: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
    maxWidth: '80%',
  },
  errorUrl: {
    marginTop: 8,
    fontSize: 12,
    textAlign: 'center',
    maxWidth: '90%',
    opacity: 0.7,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'center',
    width: '100%',
    gap: 12,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  darkRetryButton: {
    backgroundColor: Colors.dark.primary,
  },
  lightRetryButton: {
    backgroundColor: Colors.light.primary,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  alternateButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  darkAlternateButton: {
    backgroundColor: Colors.dark.secondary,
  },
  lightAlternateButton: {
    backgroundColor: Colors.light.secondary,
  },
  alternateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
    padding: 4,
  },
  darkText: {
    color: Colors.dark.text,
  },
  lightText: {
    color: Colors.light.text,
  },
  darkTextSecondary: {
    color: Colors.dark.textSecondary,
  },
  lightTextSecondary: {
    color: Colors.light.textSecondary,
  },
});

export default PDFViewer;
