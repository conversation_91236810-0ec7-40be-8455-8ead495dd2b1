import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Notice } from '@/stores/noticeStore';
import React, { memo } from 'react';
import {
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface NoticeDetailsProps {
  notice: Notice;
  onClose: () => void;
  onViewFile: (fileUrl: string) => void;
}

const NoticeDetails: React.FC<NoticeDetailsProps> = memo(({
  notice,
  onClose,
  onViewFile,
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Extract file name from URL
  const getFileNameFromUrl = (url: string): string => {
    try {
      // Parse the URL
      const urlObj = new URL(url);
      // Get the path parts
      const pathParts = urlObj.pathname.split("/");
      // Get the filename (last part of the path)
      const fileName = pathParts[pathParts.length - 1];

      // Remove timestamp prefix (format: 1234567890_filename.pdf)
      const fileNameWithExt = fileName.replace(/^\d+_/, "");
      // Replace underscores with spaces for better readability
      return fileNameWithExt.replace(/_/g, " ");
    } catch (error) {
      console.error("Error parsing file URL:", error);
      return "Document";
    }
  };

  return (
    <View
      className={`absolute inset-0 z-10 ${
        isDark ? "bg-dark-background" : "bg-light-background"
      }`}
    >
      <SafeAreaView className="flex-1">
        <ScrollView className="flex-1 p-4">
          {/* Header */}
          <View className="flex-row items-center mb-6">
            <TouchableOpacity
              onPress={onClose}
              className="mr-4"
            >
              <IconSymbol
                name="arrow.left"
                size={24}
                color={isDark ? Colors.dark.text : Colors.light.text}
              />
            </TouchableOpacity>
            <Text
              className={`text-2xl font-rubik-bold ${
                isDark ? "text-dark-text" : "text-light-text"
              }`}
              numberOfLines={1}
            >
              Notice Details
            </Text>
          </View>

          {/* Notice Content */}
          <View
            className={`p-4 rounded-xl mb-6 ${
              isDark ? "bg-dark-surface" : "bg-light-surface"
            }`}
            style={{
              elevation: 2,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            }}
          >
            <Text
              className={`text-xl font-rubik-bold mb-2 ${
                isDark ? "text-dark-text" : "text-light-text"
              }`}
            >
              {notice.title}
            </Text>
            <Text
              className={`mb-4 ${
                isDark
                  ? "text-dark-textSecondary"
                  : "text-light-textSecondary"
              }`}
            >
              {formatDate(notice.created_at)} •{" "}
              {notice.created_by_name}
            </Text>
            {notice.content && (
              <Text
                className={`mb-4 ${
                  isDark ? "text-dark-text" : "text-light-text"
                }`}
                style={{ lineHeight: 22 }}
              >
                {notice.content}
              </Text>
            )}
            {notice.file_url && (
              <TouchableOpacity
                className={`flex-row items-center p-3 rounded-lg ${
                  isDark ? "bg-dark-background" : "bg-light-background"
                }`}
                onPress={() => onViewFile(notice.file_url!)}
              >
                <IconSymbol
                  name="doc.text.fill"
                  size={24}
                  color={isDark ? Colors.dark.primary : Colors.light.primary}
                  style={{ marginRight: 10 }}
                />
                <View className="flex-1">
                  <Text
                    className="font-rubik-medium text-white"
                    numberOfLines={1}
                    style={{ color: isDark ? "#60A5FA" : "#3B82F6" }}
                  >
                    {getFileNameFromUrl(notice.file_url)}
                  </Text>
                  <Text
                    className={`${
                      isDark
                        ? "text-dark-textSecondary"
                        : "text-light-textSecondary"
                    } text-xs`}
                    numberOfLines={1}
                  >
                    Click to view document
                  </Text>
                </View>
                <IconSymbol
                  name="square.and.arrow.up"
                  size={20}
                  color={isDark ? Colors.dark.primary : Colors.light.primary}
                />
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
});

NoticeDetails.displayName = 'NoticeDetails';

export default NoticeDetails;
