import { CACHE_CONFIG, CacheHelpers, cacheManager } from '@/lib/cache';
import { supabase, supabaseAdmin } from "@/lib/supabase";
import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { Alert } from "react-native";
import { create } from "zustand";
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Define types
export interface Notice {
  id: string;
  title: string;
  content: string;
  file_url: string | null;
  created_at: string;
  created_by: string;
  tenant_id: string;
  created_by_name?: string;
}

export interface NoticeFormData {
  title: string;
  content: string;
  file?: DocumentPicker.DocumentPickerResult;
}

export interface FormErrors {
  title: string;
  content: string;
  file: string;
}

interface NoticeState {
  // Notice data
  notices: Notice[];
  isLoading: boolean;
  error: string | null;
  refreshing: boolean;
  lastUpdated: number;

  // Form state
  formData: NoticeFormData;
  formErrors: FormErrors;
  isSubmitting: boolean;
  selectedFileName: string | null;
  uploadProgress: number;

  // Actions
  fetchNotices: (tenantId: string, forceRefresh?: boolean) => Promise<void>;
  refreshData: (tenantId: string) => Promise<void>;
  createNotice: (userId: string, tenantId: string) => Promise<boolean>;
  setFormData: (field: keyof NoticeFormData, value: string) => void;
  resetForm: () => void;
  selectFile: () => Promise<void>;
  validateForm: () => boolean;
  uploadFile: () => Promise<string | null>;
  clearError: () => void;
  clearData: () => void;
}

// Create the store
export const useNoticeStore = create<NoticeState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Notice data
      notices: [],
      isLoading: false,
      error: null,
      refreshing: false,
      lastUpdated: 0,

      // Form state
      formData: {
        title: "",
        content: "",
      },
      formErrors: {
        title: "",
        content: "",
        file: "",
      },
      isSubmitting: false,
      selectedFileName: null,
      uploadProgress: 0,

      // Actions
      fetchNotices: async (tenantId: string, forceRefresh: boolean = false) => {
        const cacheKey = CacheHelpers.noticesKey(tenantId);

        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          // Clear cache if force refresh is requested
          if (forceRefresh) {
            await cacheManager.delete(cacheKey);
          }

          const notices = await cacheManager.get<Notice[]>(
            cacheKey,
            async () => {
              console.log('📢 Fetching notices from API for tenant:', tenantId);

              // Debug: Check current auth session
              const { data: session } = await supabase.auth.getSession();
              console.log('🔐 Current auth session:', session?.session?.user?.id || 'No session');

              // Get all notices for this tenant directly from the notices table using admin client to bypass RLS
              const { data: noticesData, error: noticesError } = await supabaseAdmin
                .from("notices")
                .select("*")
                .eq("tenant_id", tenantId)
                .order("created_at", { ascending: false });

              console.log('📊 Query result:', {
                data: noticesData,
                error: noticesError,
                dataLength: noticesData?.length || 0
              });

              if (noticesError) {
                throw new Error(`Failed to fetch notices: ${noticesError.message}`);
              }

              // Get creator information for each notice
              const noticesWithCreatorInfo = await Promise.all(
                (noticesData || []).map(async (notice: any) => {
                  // Get creator info using admin client
                  const { data: userData } = await supabaseAdmin
                    .from("users")
                    .select("name")
                    .eq("id", notice.created_by)
                    .single();

                  return {
                    ...notice,
                    created_by_name: userData?.name || "Unknown User",
                  };
                })
              );

              return noticesWithCreatorInfo as Notice[];
            },
            CACHE_CONFIG.NOTICES
          );

          set((state) => {
            state.notices = notices || [];
            state.isLoading = false;
            state.lastUpdated = Date.now();
          });

        } catch (error) {
          console.error('Error fetching notices:', error);
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch notices';
            state.isLoading = false;
          });
        }
      },

      refreshData: async (tenantId: string) => {
        set((state) => {
          state.refreshing = true;
          state.error = null;
        });

        try {
          await get().fetchNotices(tenantId, true); // Force refresh
        } finally {
          set((state) => {
            state.refreshing = false;
          });
        }
      },

      createNotice: async (userId: string, tenantId: string) => {
        const state = get();

        if (!state.validateForm()) {
          return false;
        }

        set((state) => {
          state.isSubmitting = true;
        });

        try {
          let fileUrl = null;

          // Upload file if selected
          if (state.formData.file && !state.formData.file.canceled) {
            fileUrl = await get().uploadFile();
          }

          // Insert notice directly into the notices table
          console.log(
            "Inserting notice with tenant_id:",
            tenantId,
            "created_by:",
            userId
          );
          const { error } = await supabase
            .from("notices")
            .insert({
              tenant_id: tenantId,
              created_by: userId,
              title: state.formData.title,
              content: state.formData.content,
              file_url: fileUrl,
            });

          if (error) throw error;

          // Clear cache to ensure fresh data on next fetch
          const cacheKey = CacheHelpers.noticesKey(tenantId);
          await cacheManager.delete(cacheKey);

          // Reset form and fetch updated notices
          get().resetForm();
          await get().fetchNotices(tenantId, true);

          set((state) => {
            state.isSubmitting = false;
          });
          return true;
        } catch (error) {
          console.error("Error creating notice:", error);
          set((state) => {
            state.error = error instanceof Error ? error.message : "Failed to create notice";
            state.isSubmitting = false;
          });
          return false;
        }
      },

      setFormData: (field: keyof NoticeFormData, value: string) => {
        set((state) => {
          state.formData = { ...state.formData, [field]: value };
          state.formErrors = {
            ...state.formErrors,
            [field]: "", // Clear error when user types
          };
        });
      },

      resetForm: () => {
        set((state) => {
          state.formData = {
            title: "",
            content: "",
          };
          state.formErrors = {
            title: "",
            content: "",
            file: "",
          };
          state.selectedFileName = null;
          state.uploadProgress = 0;
        });
      },

      selectFile: async () => {
        try {
          const result = await DocumentPicker.getDocumentAsync({
            type: ["application/pdf"],
            copyToCacheDirectory: true,
          });

          if (result.canceled) {
            return;
          }

          // Check file size (limit to 10MB)
          const fileInfo = await FileSystem.getInfoAsync(result.assets[0].uri);
          const fileSize = fileInfo.exists && "size" in fileInfo ? fileInfo.size : 0;
          if (fileSize > 10 * 1024 * 1024) {
            set((state) => {
              state.formErrors = {
                ...state.formErrors,
                file: "File size exceeds 10MB limit",
              };
            });
            return;
          }

          set((state) => {
            state.formData = { ...state.formData, file: result };
            state.selectedFileName = result.assets[0].name;
            state.formErrors = { ...state.formErrors, file: "" };
          });
        } catch (error) {
          console.error("Error selecting file:", error);
          Alert.alert("Error", "Failed to select file");
        }
      },

      validateForm: () => {
        const { formData } = get();
        const errors = {
          title: "",
          content: "",
          file: "",
        };
        let isValid = true;

        if (!formData.title.trim()) {
          errors.title = "Title is required";
          isValid = false;
        }

        if (!formData.content.trim() && !formData.file) {
          errors.content = "Either content or a file is required";
          isValid = false;
        }

        set((state) => {
          state.formErrors = errors;
        });
        return isValid;
      },

      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      clearData: () => {
        set((state) => {
          state.notices = [];
          state.error = null;
          state.lastUpdated = 0;
        });
      },

      // Helper method for file upload
      uploadFile: async (): Promise<string | null> => {
        const { formData } = get();

        if (!formData.file || formData.file.canceled) {
          return null;
        }

        try {
          const file = formData.file.assets[0];

          // Clean the filename to remove spaces and special characters
          const cleanFileName = file.name.replace(/[^a-zA-Z0-9.]/g, "_");
          const filePath = `${Date.now()}_${cleanFileName}`;

          console.log(
            "Uploading file:",
            file.name,
            "mime type:",
            file.mimeType,
            "to path:",
            filePath
          );

          // Read file as base64
          const fileContent = await FileSystem.readAsStringAsync(file.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          // Decode Base64 to binary (Uint8Array)
          const binaryData = Uint8Array.from(atob(fileContent), (c) =>
            c.charCodeAt(0)
          );

          // Upload to Supabase Storage with binary data
          const { data, error } = await supabase.storage
            .from("notices")
            .upload(filePath, binaryData, {
              contentType: "application/pdf",
              upsert: true,
            });

          if (error) {
            console.error("Error uploading file to storage:", error);
            throw error;
          }

          console.log("File uploaded successfully:", data);

          // Get public URL
          const { data: urlData } = supabase.storage
            .from("notices")
            .getPublicUrl(filePath);
          console.log("File public URL:", urlData.publicUrl);

          return urlData.publicUrl;
        } catch (error) {
          console.error("Error uploading file:", error);
          throw error;
        }
      },
    }))
  )
);
