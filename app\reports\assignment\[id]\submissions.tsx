import SubmissionManagement from '@/components/assignment/SubmissionManagement';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { useLocalSearchParams } from 'expo-router';
import React from 'react';
import { Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function AssignmentSubmissions() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { selectedAssignment } = useAssignmentStore();

  if (!selectedAssignment || selectedAssignment.id !== id) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
        <Text className="text-light-text dark:text-dark-text">Assignment not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-light-background dark:bg-dark-background">
      <SubmissionManagement assignment={selectedAssignment} />
    </SafeAreaView>
  );
}
