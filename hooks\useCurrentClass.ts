import { supabase } from '@/lib/supabase';
import { useUser } from '@clerk/clerk-expo';
import { useEffect, useState } from 'react';

export function useCurrentClass() {
  const { user } = useUser();
  const [currentClassId, setCurrentClassId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCurrentClass() {
      if (!user) return;

      try {
        const { data: teacherData, error: teacherError } = await supabase
          .from('teachers')
          .select('current_class_id')
          .eq('user_id', user.id)
          .single();

        if (teacherError) throw teacherError;
        setCurrentClassId(teacherData?.current_class_id);
      } catch (err) {
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    }

    fetchCurrentClass();
  }, [user]);

  return {
    currentClassId,
    tenantId: user?.id,
    loading,
    error,
  };
}
