import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface AssignmentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  subject: string;
  gradeLevel: string;
  templateData: {
    title: string;
    description: string;
    instructions: string;
    maxPoints: number;
    rubrics?: any[];
    attachments?: string[];
  };
  isPublic: boolean;
  usageCount: number;
  createdBy: string;
  createdByName: string;
  createdAt: string;
  tags: string[];
}

interface AssignmentTemplateLibraryProps {
  onSelectTemplate: (template: AssignmentTemplate) => void;
  onCreateFromTemplate?: (template: AssignmentTemplate) => void;
}

export default function AssignmentTemplateLibrary({
  onSelectTemplate,
  onCreateFromTemplate,
}: AssignmentTemplateLibraryProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const [templates, setTemplates] = useState<AssignmentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubject, setSelectedSubject] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'essay', name: 'Essays' },
    { id: 'research', name: 'Research Projects' },
    { id: 'lab', name: 'Lab Reports' },
    { id: 'presentation', name: 'Presentations' },
    { id: 'quiz', name: 'Quizzes' },
    { id: 'homework', name: 'Homework' },
    { id: 'project', name: 'Projects' },
  ];

  const subjects = [
    { id: 'all', name: 'All Subjects' },
    { id: 'math', name: 'Mathematics' },
    { id: 'science', name: 'Science' },
    { id: 'english', name: 'English' },
    { id: 'history', name: 'History' },
    { id: 'art', name: 'Art' },
    { id: 'music', name: 'Music' },
    { id: 'pe', name: 'Physical Education' },
  ];

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockTemplates: AssignmentTemplate[] = [
        {
          id: '1',
          name: 'Research Paper Template',
          description: 'A comprehensive template for research papers with proper citation guidelines',
          category: 'research',
          subject: 'english',
          gradeLevel: 'High School',
          templateData: {
            title: 'Research Paper Assignment',
            description: 'Write a comprehensive research paper on your chosen topic',
            instructions: `Instructions for Research Paper:

1. Choose a topic from the approved list
2. Conduct thorough research using at least 5 credible sources
3. Create an outline before writing
4. Write a 5-7 page paper with proper citations
5. Include a bibliography in MLA format

Grading Criteria:
- Research quality (25%)
- Writing clarity (25%)
- Organization (25%)
- Citations and format (25%)`,
            maxPoints: 100,
            rubrics: [
              { criteria_name: 'Research Quality', description: 'Use of credible sources and depth of research', max_points: 25, order_index: 0 },
              { criteria_name: 'Writing Clarity', description: 'Clear, coherent writing style', max_points: 25, order_index: 1 },
              { criteria_name: 'Organization', description: 'Logical structure and flow', max_points: 25, order_index: 2 },
              { criteria_name: 'Citations & Format', description: 'Proper MLA format and citations', max_points: 25, order_index: 3 },
            ],
          },
          isPublic: true,
          usageCount: 45,
          createdBy: 'teacher1',
          createdByName: 'Ms. Johnson',
          createdAt: '2024-01-15T10:00:00Z',
          tags: ['research', 'writing', 'mla', 'citations'],
        },
        {
          id: '2',
          name: 'Science Lab Report',
          description: 'Standard template for science laboratory reports',
          category: 'lab',
          subject: 'science',
          gradeLevel: 'High School',
          templateData: {
            title: 'Lab Report Assignment',
            description: 'Document your laboratory experiment with proper scientific methodology',
            instructions: `Lab Report Format:

1. Title Page
2. Abstract (150 words max)
3. Introduction and Hypothesis
4. Materials and Methods
5. Results (with data tables/graphs)
6. Discussion and Analysis
7. Conclusion
8. References

Submit your report within one week of completing the lab.`,
            maxPoints: 80,
            rubrics: [
              { criteria_name: 'Methodology', description: 'Clear description of experimental procedure', max_points: 20, order_index: 0 },
              { criteria_name: 'Data Collection', description: 'Accurate recording and presentation of data', max_points: 20, order_index: 1 },
              { criteria_name: 'Analysis', description: 'Thoughtful interpretation of results', max_points: 20, order_index: 2 },
              { criteria_name: 'Scientific Writing', description: 'Proper scientific format and language', max_points: 20, order_index: 3 },
            ],
          },
          isPublic: true,
          usageCount: 32,
          createdBy: 'teacher2',
          createdByName: 'Dr. Smith',
          createdAt: '2024-01-10T14:30:00Z',
          tags: ['lab', 'science', 'experiment', 'data'],
        },
        {
          id: '3',
          name: 'Math Problem Set',
          description: 'Template for mathematics homework assignments',
          category: 'homework',
          subject: 'math',
          gradeLevel: 'High School',
          templateData: {
            title: 'Mathematics Problem Set',
            description: 'Solve the assigned problems showing all work',
            instructions: `Problem Set Instructions:

1. Show all work for each problem
2. Box your final answers
3. Use proper mathematical notation
4. Check your answers when possible
5. Ask questions if you need help

Remember: Partial credit is given for correct methodology even if the final answer is incorrect.`,
            maxPoints: 50,
            rubrics: [
              { criteria_name: 'Problem Solving', description: 'Correct approach and methodology', max_points: 25, order_index: 0 },
              { criteria_name: 'Mathematical Communication', description: 'Clear work shown and notation', max_points: 15, order_index: 1 },
              { criteria_name: 'Accuracy', description: 'Correct final answers', max_points: 10, order_index: 2 },
            ],
          },
          isPublic: true,
          usageCount: 67,
          createdBy: 'teacher3',
          createdByName: 'Mr. Davis',
          createdAt: '2024-01-08T09:15:00Z',
          tags: ['math', 'homework', 'problems', 'algebra'],
        },
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTemplates();
    setRefreshing(false);
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSubject = selectedSubject === 'all' || template.subject === selectedSubject;
    
    return matchesSearch && matchesCategory && matchesSubject;
  });

  const handleUseTemplate = (template: AssignmentTemplate) => {
    Alert.alert(
      'Use Template',
      `Create a new assignment using "${template.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Use Template',
          onPress: () => {
            onCreateFromTemplate?.(template);
            onSelectTemplate(template);
          },
        },
      ]
    );
  };

  const renderTemplate = (template: AssignmentTemplate, index: number) => (
    <Animated.View
      key={template.id}
      entering={FadeInDown.delay(index * 100).duration(400)}
    >
      <TouchableOpacity
        onPress={() => handleUseTemplate(template)}
        className={`p-4 mb-3 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}
      >
        <View className="flex-row items-start justify-between mb-2">
          <View className="flex-1 mr-3">
            <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {template.name}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              by {template.createdByName}
            </Text>
          </View>
          
          <View className="items-end">
            <View className="flex-row items-center mb-1">
              <Ionicons name="people-outline" size={14} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-1 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {template.usageCount}
              </Text>
            </View>
            {template.isPublic && (
              <View className="px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded">
                <Text className="text-green-600 dark:text-green-300 text-xs font-rubik-medium">
                  Public
                </Text>
              </View>
            )}
          </View>
        </View>

        <Text className={`font-rubik mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {template.description}
        </Text>

        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center space-x-3">
            <View className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded">
              <Text className="text-blue-600 dark:text-blue-300 text-xs font-rubik-medium">
                {categories.find(c => c.id === template.category)?.name || template.category}
              </Text>
            </View>
            <View className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 rounded">
              <Text className="text-purple-600 dark:text-purple-300 text-xs font-rubik-medium">
                {subjects.find(s => s.id === template.subject)?.name || template.subject}
              </Text>
            </View>
          </View>
          
          <Text className={`font-rubik-bold text-primary-500`}>
            {template.templateData.maxPoints} pts
          </Text>
        </View>

        <View className="flex-row flex-wrap gap-1">
          {template.tags.slice(0, 3).map((tag, tagIndex) => (
            <View key={tagIndex} className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">
              <Text className={`text-xs font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                #{tag}
              </Text>
            </View>
          ))}
          {template.tags.length > 3 && (
            <View className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">
              <Text className={`text-xs font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                +{template.tags.length - 3}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="p-4 border-b border-gray-200 dark:border-gray-700">
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Assignment Templates
        </Text>
        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          Choose from pre-made templates to create assignments quickly
        </Text>
      </View>

      {/* Search and Filters */}
      <View className="p-4 space-y-3">
        {/* Search Bar */}
        <View className={`flex-row items-center px-4 py-3 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Ionicons name="search-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search templates..."
            placeholderTextColor={isDark ? '#666' : '#999'}
            className={`flex-1 ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}
          />
        </View>

        {/* Filter Tabs */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="space-x-2">
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              onPress={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg mr-2 ${
                selectedCategory === category.id
                  ? 'bg-primary-500'
                  : isDark
                  ? 'bg-dark-card'
                  : 'bg-light-card'
              }`}
            >
              <Text
                className={`font-rubik-medium text-sm ${
                  selectedCategory === category.id
                    ? 'text-white'
                    : isDark
                    ? 'text-dark-text'
                    : 'text-light-text'
                }`}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="space-x-2">
          {subjects.map((subject) => (
            <TouchableOpacity
              key={subject.id}
              onPress={() => setSelectedSubject(subject.id)}
              className={`px-4 py-2 rounded-lg mr-2 ${
                selectedSubject === subject.id
                  ? 'bg-secondary-500'
                  : isDark
                  ? 'bg-dark-card'
                  : 'bg-light-card'
              }`}
            >
              <Text
                className={`font-rubik-medium text-sm ${
                  selectedSubject === subject.id
                    ? 'text-white'
                    : isDark
                    ? 'text-dark-text'
                    : 'text-light-text'
                }`}
              >
                {subject.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Templates List */}
      <ScrollView
        className="flex-1 p-4"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredTemplates.length > 0 ? (
          filteredTemplates.map((template, index) => renderTemplate(template, index))
        ) : (
          <View className="items-center justify-center py-12">
            <Ionicons
              name="document-outline"
              size={48}
              color={isDark ? '#666' : '#999'}
            />
            <Text className={`mt-4 font-rubik text-lg ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              No templates found
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Try adjusting your search or filters
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
