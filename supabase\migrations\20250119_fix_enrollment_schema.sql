-- Fix enrollment system schema issues

-- Ensure is_class_teacher column exists in teachers table
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS is_class_teacher BOOLEAN DEFAULT FALSE;

-- Update existing teachers to be class teachers (for testing)
-- In production, this should be done selectively
UPDATE teachers SET is_class_teacher = TRUE WHERE is_class_teacher IS NULL OR is_class_teacher = FALSE;

-- Ensure enrollment_code column exists in students table
ALTER TABLE students ADD COLUMN IF NOT EXISTS enrollment_code VARCHAR(50) UNIQUE;

-- Ensure grade and section columns exist in classes table
ALTER TABLE classes ADD COLUMN IF NOT EXISTS grade VARCHAR(50);
ALTER TABLE classes ADD COLUMN IF NOT EXISTS section VARCHAR(50);

-- Create enrollment_requests table if it doesn't exist
CREATE TABLE IF NOT EXISTS enrollment_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  teacher_id UUID NOT NULL REFERENCES teachers(id),
  class_id UUID NOT NULL REFERENCES classes(id),
  student_name VARCHAR(255) NOT NULL,
  student_email VARCHAR(255) NOT NULL,
  student_grade VARCHAR(50),
  student_section VARCHAR(50),
  roll_number VARCHAR(50),
  date_of_birth DATE,
  parent_name VARCHAR(255),
  parent_email VARCHAR(255),
  parent_phone VARCHAR(20),
  enrollment_code VARCHAR(50) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Create class_students table if it doesn't exist
CREATE TABLE IF NOT EXISTS class_students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  class_id UUID NOT NULL REFERENCES classes(id),
  student_id UUID NOT NULL REFERENCES students(id),
  enrollment_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'transferred')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  UNIQUE(class_id, student_id)
);

-- Enable RLS on new tables
ALTER TABLE enrollment_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE class_students ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for enrollment_requests
DROP POLICY IF EXISTS "enrollment_requests_select_policy" ON enrollment_requests;
CREATE POLICY "enrollment_requests_select_policy" ON enrollment_requests
  FOR SELECT USING (
    tenant_id = (current_setting('app.tenant_id', true))::UUID OR
    teacher_id IN (
      SELECT id FROM teachers 
      WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      OR id IN (
        SELECT t.id FROM teachers t
        JOIN users u ON u.id = t.user_id
        WHERE u.clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      )
    )
  );

DROP POLICY IF EXISTS "enrollment_requests_insert_policy" ON enrollment_requests;
CREATE POLICY "enrollment_requests_insert_policy" ON enrollment_requests
  FOR INSERT WITH CHECK (
    teacher_id IN (
      SELECT id FROM teachers 
      WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      AND is_class_teacher = TRUE
      OR id IN (
        SELECT t.id FROM teachers t
        JOIN users u ON u.id = t.user_id
        WHERE u.clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        AND t.is_class_teacher = TRUE
      )
    )
  );

DROP POLICY IF EXISTS "enrollment_requests_update_policy" ON enrollment_requests;
CREATE POLICY "enrollment_requests_update_policy" ON enrollment_requests
  FOR UPDATE USING (
    teacher_id IN (
      SELECT id FROM teachers 
      WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      AND is_class_teacher = TRUE
      OR id IN (
        SELECT t.id FROM teachers t
        JOIN users u ON u.id = t.user_id
        WHERE u.clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        AND t.is_class_teacher = TRUE
      )
    )
  );

-- Create RLS policies for class_students
DROP POLICY IF EXISTS "class_students_select_policy" ON class_students;
CREATE POLICY "class_students_select_policy" ON class_students
  FOR SELECT USING (
    tenant_id = (current_setting('app.tenant_id', true))::UUID OR
    class_id IN (
      SELECT id FROM classes 
      WHERE teacher_id IN (
        SELECT id FROM teachers 
        WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        OR id IN (
          SELECT t.id FROM teachers t
          JOIN users u ON u.id = t.user_id
          WHERE u.clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        )
      )
    )
  );

DROP POLICY IF EXISTS "class_students_insert_policy" ON class_students;
CREATE POLICY "class_students_insert_policy" ON class_students
  FOR INSERT WITH CHECK (
    class_id IN (
      SELECT id FROM classes 
      WHERE teacher_id IN (
        SELECT id FROM teachers 
        WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        AND is_class_teacher = TRUE
        OR id IN (
          SELECT t.id FROM teachers t
          JOIN users u ON u.id = t.user_id
          WHERE u.clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
          AND t.is_class_teacher = TRUE
        )
      )
    )
  );

DROP POLICY IF EXISTS "class_students_update_policy" ON class_students;
CREATE POLICY "class_students_update_policy" ON class_students
  FOR UPDATE USING (
    class_id IN (
      SELECT id FROM classes 
      WHERE teacher_id IN (
        SELECT id FROM teachers 
        WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        AND is_class_teacher = TRUE
        OR id IN (
          SELECT t.id FROM teachers t
          JOIN users u ON u.id = t.user_id
          WHERE u.clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
          AND t.is_class_teacher = TRUE
        )
      )
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_teachers_clerk_user_id ON teachers(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_teachers_is_class_teacher ON teachers(is_class_teacher);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_teacher_id ON enrollment_requests(teacher_id);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_class_id ON enrollment_requests(class_id);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_status ON enrollment_requests(status);
CREATE INDEX IF NOT EXISTS idx_class_students_class_id ON class_students(class_id);
CREATE INDEX IF NOT EXISTS idx_class_students_student_id ON class_students(student_id);

-- Function to generate unique enrollment code
CREATE OR REPLACE FUNCTION generate_enrollment_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  exists_check INTEGER;
BEGIN
  LOOP
    -- Generate a 8-character alphanumeric code
    code := upper(substring(md5(random()::text) from 1 for 8));
    
    -- Check if code already exists
    SELECT COUNT(*) INTO exists_check 
    FROM (
      SELECT enrollment_code FROM enrollment_requests WHERE enrollment_code = code
      UNION
      SELECT enrollment_code FROM students WHERE enrollment_code = code
    ) AS existing_codes;
    
    -- If code doesn't exist, return it
    IF exists_check = 0 THEN
      RETURN code;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate enrollment code
CREATE OR REPLACE FUNCTION set_enrollment_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.enrollment_code IS NULL OR NEW.enrollment_code = '' THEN
    NEW.enrollment_code := generate_enrollment_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_set_enrollment_code ON enrollment_requests;
CREATE TRIGGER trigger_set_enrollment_code
  BEFORE INSERT ON enrollment_requests
  FOR EACH ROW
  EXECUTE FUNCTION set_enrollment_code();

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_enrollment_requests_updated_at ON enrollment_requests;
CREATE TRIGGER update_enrollment_requests_updated_at
  BEFORE UPDATE ON enrollment_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_class_students_updated_at ON class_students;
CREATE TRIGGER update_class_students_updated_at
  BEFORE UPDATE ON class_students
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
