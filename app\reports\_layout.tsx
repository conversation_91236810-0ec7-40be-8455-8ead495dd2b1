import { useColorScheme } from '@/hooks/useColorScheme';
import { Stack } from 'expo-router';
import React from 'react';

export default function ReportsLayout() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: isDark ? '#1a1b1e' : '#ffffff',
        },
        headerTintColor: isDark ? '#ffffff' : '#000000',
        headerTitleStyle: {
          fontFamily: 'Rubik-Medium',
        },
      }}
    >
      <Stack.Screen 
        name="index"
        options={{
          title: 'Reports',
        }}
      />
      <Stack.Screen 
        name="detailed" 
        options={{
          title: 'Detailed Report',
        }}
      />
      <Stack.Screen 
        name="summary"
        options={{
          title: 'Summary Report',
        }}
      />
      <Stack.Screen 
        name="export"
        options={{
          title: 'Export Data',
        }}
      />
      <Stack.Screen 
        name="trends"
        options={{
          title: 'Trends Analysis',
        }}
      />
      <Stack.Screen 
        name="analytics"
        options={{
          title: 'Analytics',
        }}
      />
      <Stack.Screen
        name="assignment/create"
        options={{
          title: 'Create Assignment',
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="assignment/create-enhanced"
        options={{
          title: 'Create Assignment',
          presentation: 'modal',
        }}
      />
      <Stack.Screen 
        name="mock-test/create"
        options={{
          title: 'Create Mock Test',
          presentation: 'modal',
        }}
      />
      <Stack.Screen 
        name="grades"
        options={{
          title: 'Grade Submissions',
        }}
      />
    </Stack>
  );
}
