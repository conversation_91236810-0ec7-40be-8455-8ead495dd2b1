import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { RefreshControl, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

interface ClassSummary {
  classId: string;
  className: string;
  totalSessions: number;
  totalStudents: number;
  averageAttendance: number;
  presentCount: number;
  absentCount: number;
  lateCount: number;
  excusedCount: number;
}

const SummaryReportsScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { userId: clerkUserId } = useAuth();

  const [classSummaries, setClassSummaries] = useState<ClassSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const { sessions, attendanceStats, loadSessions, loadAttendanceStats } = useAttendanceStore();
  const { currentTeacher, availableClasses, loadTeacherData, loadAvailableClasses } = useEnrollmentStore();

  // Load data
  useEffect(() => {
    const initializeData = async () => {
      if (clerkUserId && !currentTeacher) {
        await loadTeacherData(clerkUserId);
      }
    };
    initializeData();
  }, [clerkUserId, currentTeacher, loadTeacherData]);

  useEffect(() => {
    const loadReportData = async () => {
      if (currentTeacher?.id) {
        try {
          setIsLoading(true);
          setError(null);

          // Load classes and sessions
          await loadAvailableClasses(currentTeacher.id);

          const today = new Date().toISOString().split('T')[0];
          const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

          await loadSessions(currentTeacher.id, undefined, { start: monthAgo, end: today });
          await loadAttendanceStats(currentTeacher.id);

        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to load report data');
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadReportData();
  }, [currentTeacher?.id, loadAvailableClasses, loadSessions, loadAttendanceStats]);

  // Calculate class summaries
  useEffect(() => {
    if (availableClasses.length > 0 && sessions.length > 0) {
      const summaries: ClassSummary[] = availableClasses.map(classItem => {
        const classSessions = sessions.filter(session => session.class_id === classItem.id);

        let totalPresent = 0;
        let totalAbsent = 0;
        let totalLate = 0;
        let totalExcused = 0;
        let totalRecords = 0;

        classSessions.forEach(session => {
          if (session.attendance_records) {
            session.attendance_records.forEach(record => {
              totalRecords++;
              switch (record.status) {
                case 'present':
                  totalPresent++;
                  break;
                case 'absent':
                  totalAbsent++;
                  break;
                case 'late':
                  totalLate++;
                  break;
                case 'excused':
                  totalExcused++;
                  break;
              }
            });
          }
        });

        const averageAttendance = totalRecords > 0 ? (totalPresent / totalRecords) * 100 : 0;

        return {
          classId: classItem.id,
          className: classItem.name,
          totalSessions: classSessions.length,
          totalStudents: classItem.student_count || 0,
          averageAttendance,
          presentCount: totalPresent,
          absentCount: totalAbsent,
          lateCount: totalLate,
          excusedCount: totalExcused,
        };
      });

      setClassSummaries(summaries);
    }
  }, [availableClasses, sessions]);

  const handleRefresh = async () => {
    if (currentTeacher?.id) {
      setRefreshing(true);
      try {
        await loadAvailableClasses(currentTeacher.id);

        const today = new Date().toISOString().split('T')[0];
        const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        await loadSessions(currentTeacher.id, undefined, { start: monthAgo, end: today });
        await loadAttendanceStats(currentTeacher.id);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to refresh data');
      } finally {
        setRefreshing(false);
      }
    }
  };

  const getAttendanceColor = (percentage: number) => {
    if (percentage >= 90) return 'text-success';
    if (percentage >= 75) return 'text-warning';
    return 'text-error';
  };

  const getAttendanceBadgeColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-success';
    if (percentage >= 75) return 'bg-warning';
    return 'bg-error';
  };

  if (isLoading) {
    return <LoadingScreen message="Loading summary reports..." />;
  }

  if (error) {
    return (
      <ErrorScreen
        title="Report Error"
        message={error}
        onRetry={() => {
          setError(null);
          handleRefresh();
        }}
      />
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={() => router.back()}
            className="mr-4"
          >
            <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Summary Reports
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Class-wise attendance overview
            </Text>
          </View>
        </View>
      </View>

      <ScrollView
        className="flex-1 p-4"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={isDark ? '#60A5FA' : '#2563EB'}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Overall Statistics */}
        {attendanceStats && (
          <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Overall Statistics (Last 30 Days)
            </Text>
            <View className="flex-row justify-between">
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
                  {attendanceStats.total_sessions}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Total Sessions
                </Text>
              </View>
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl text-success`}>
                  {attendanceStats.present_percentage.toFixed(1)}%
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Attendance Rate
                </Text>
              </View>
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {attendanceStats.total_students}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Total Students
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Class Summaries */}
        <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Class-wise Performance
          </Text>

          {classSummaries.length === 0 ? (
            <View className="items-center py-8">
              <IconSymbol
                name="chart.bar.fill"
                size={48}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                No class data available
              </Text>
              <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Create attendance sessions to see class summaries
              </Text>
            </View>
          ) : (
            <View className="space-y-3">
              {classSummaries.map((summary) => (
                <TouchableOpacity
                  key={summary.classId}
                  onPress={() => router.push(`/reports/detailed?classId=${summary.classId}` as any)}
                  className={`p-4 rounded-lg border ${
                    isDark ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border'
                  }`}
                >
                  <View className="flex-row items-center justify-between mb-2">
                    <Text className={`font-rubik-semibold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {summary.className}
                    </Text>
                    <View className={`px-2 py-1 rounded ${getAttendanceBadgeColor(summary.averageAttendance)}`}>
                      <Text className="text-white font-rubik-medium text-xs">
                        {summary.averageAttendance.toFixed(1)}%
                      </Text>
                    </View>
                  </View>

                  <View className="flex-row justify-between items-center">
                    <View>
                      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        {summary.totalSessions} sessions • {summary.totalStudents} students
                      </Text>
                      <View className="flex-row mt-2 flex-wrap">
                        <Text className="text-success font-rubik text-sm mr-4 mb-1">
                          Present: {summary.presentCount}
                        </Text>
                        <Text className="text-error font-rubik text-sm mr-4 mb-1">
                          Absent: {summary.absentCount}
                        </Text>
                        {summary.lateCount > 0 && (
                          <Text className="text-warning font-rubik text-sm mr-4 mb-1">
                            Late: {summary.lateCount}
                          </Text>
                        )}
                        {summary.excusedCount > 0 && (
                          <Text className="text-info font-rubik text-sm mr-4 mb-1">
                            Excused: {summary.excusedCount}
                          </Text>
                        )}
                      </View>
                    </View>
                    <IconSymbol name="chevron.right" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Quick Actions
          </Text>

          <TouchableOpacity
            onPress={() => router.push('/reports/detailed' as any)}
            className={`p-3 rounded-lg flex-row items-center justify-between mb-4 ${
              isDark ? 'bg-primary-600' : 'bg-primary-500'
            }`}
          >
            <Text className="text-white font-rubik-semibold">
              View Detailed Reports
            </Text>
            <IconSymbol name="chevron.right" size={16} color="#FFFFFF" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => router.push('/reports/export' as any)}
            className={`p-3 rounded-lg flex-row items-center justify-between mb-4 border-2 border-primary-500 ${
              isDark ? 'bg-dark-surface' : 'bg-light-surface'
            }`}
          >
            <Text className="text-primary-500 font-rubik-semibold">
              Export Data
            </Text>
            <IconSymbol name="chevron.right" size={16} color="#3B82F6" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => router.push('/reports/trends' as any)}
            className={`p-3 rounded-lg flex-row items-center justify-between border-2 ${
              isDark ? 'border-dark-border bg-dark-surface' : 'border-light-border bg-light-surface'
            }`}
          >
            <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              View Trends
            </Text>
            <IconSymbol name="chevron.right" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SummaryReportsScreen;
