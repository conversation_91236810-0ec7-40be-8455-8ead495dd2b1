-- Create materials and curriculum management system

-- Materials table (Teacher Notes, Lesson Plans, Resources)
CREATE TABLE IF NOT EXISTS materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  uploaded_by UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT, -- For text-based materials
  file_url TEXT, -- URL to Supabase storage for files
  file_type VARCHAR(50), -- pdf, doc, ppt, image, etc.
  file_size INTEGER, -- File size in bytes
  subject VARCHAR(100),
  grade_level VARCHAR(50),
  tags TEXT[], -- Array of tags for categorization
  material_type VARCHAR(50) DEFAULT 'resource' CHECK (material_type IN ('lesson_plan', 'worksheet', 'quiz', 'resource', 'assignment', 'presentation')),
  visibility VARCHAR(20) DEFAULT 'private' CHECK (visibility IN ('private', 'class', 'school', 'public')),
  gemini_generated BOOLEAN DEFAULT FALSE,
  download_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Material sharing table (which classes/teachers can access materials)
CREATE TABLE IF NOT EXISTS material_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  material_id UUID NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
  shared_with_type VARCHAR(20) NOT NULL CHECK (shared_with_type IN ('teacher', 'class', 'student')),
  shared_with_id UUID NOT NULL, -- Can reference teachers, classes, or students
  shared_by UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  permissions VARCHAR(20) DEFAULT 'view' CHECK (permissions IN ('view', 'download', 'edit')),
  shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  
  UNIQUE(material_id, shared_with_type, shared_with_id)
);

-- Curriculum plans table
CREATE TABLE IF NOT EXISTS curriculum_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  subject VARCHAR(100) NOT NULL,
  grade_level VARCHAR(50),
  academic_year VARCHAR(20),
  semester VARCHAR(20),
  start_date DATE,
  end_date DATE,
  total_lessons INTEGER DEFAULT 0,
  completed_lessons INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Curriculum lessons table
CREATE TABLE IF NOT EXISTS curriculum_lessons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  curriculum_plan_id UUID NOT NULL REFERENCES curriculum_plans(id) ON DELETE CASCADE,
  lesson_number INTEGER NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  objectives TEXT[],
  duration_minutes INTEGER,
  lesson_date DATE,
  status VARCHAR(20) DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'completed', 'skipped')),
  notes TEXT,
  materials_used UUID[], -- Array of material IDs
  homework_assigned TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(curriculum_plan_id, lesson_number)
);

-- Material categories for better organization
CREATE TABLE IF NOT EXISTS material_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_category_id UUID REFERENCES material_categories(id),
  color_code VARCHAR(7), -- Hex color code
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(tenant_id, name)
);

-- Material category assignments
CREATE TABLE IF NOT EXISTS material_category_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  material_id UUID NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES material_categories(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(material_id, category_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_materials_tenant_id ON materials(tenant_id);
CREATE INDEX IF NOT EXISTS idx_materials_uploaded_by ON materials(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_materials_subject ON materials(subject);
CREATE INDEX IF NOT EXISTS idx_materials_grade_level ON materials(grade_level);
CREATE INDEX IF NOT EXISTS idx_materials_material_type ON materials(material_type);
CREATE INDEX IF NOT EXISTS idx_materials_visibility ON materials(visibility);
CREATE INDEX IF NOT EXISTS idx_materials_created_at ON materials(created_at);
CREATE INDEX IF NOT EXISTS idx_materials_tags ON materials USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_material_shares_tenant_id ON material_shares(tenant_id);
CREATE INDEX IF NOT EXISTS idx_material_shares_material_id ON material_shares(material_id);
CREATE INDEX IF NOT EXISTS idx_material_shares_shared_with ON material_shares(shared_with_type, shared_with_id);

CREATE INDEX IF NOT EXISTS idx_curriculum_plans_tenant_id ON curriculum_plans(tenant_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_plans_teacher_id ON curriculum_plans(teacher_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_plans_class_id ON curriculum_plans(class_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_plans_subject ON curriculum_plans(subject);

CREATE INDEX IF NOT EXISTS idx_curriculum_lessons_curriculum_plan_id ON curriculum_lessons(curriculum_plan_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_lessons_lesson_date ON curriculum_lessons(lesson_date);

CREATE INDEX IF NOT EXISTS idx_material_categories_tenant_id ON material_categories(tenant_id);
CREATE INDEX IF NOT EXISTS idx_material_categories_parent_id ON material_categories(parent_category_id);

-- Enable Row Level Security
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE curriculum_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE curriculum_lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_category_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for materials
CREATE POLICY "Teachers can manage their own materials" ON materials
  FOR ALL USING (
    tenant_id = current_setting('app.tenant_id')::UUID AND
    uploaded_by = (
      SELECT t.id FROM teachers t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE COALESCE(t.clerk_user_id, u.clerk_user_id) = current_setting('app.clerk_user_id')::TEXT
    )
  );

CREATE POLICY "Teachers can view shared materials" ON materials
  FOR SELECT USING (
    tenant_id = current_setting('app.tenant_id')::UUID AND (
      uploaded_by = (
        SELECT t.id FROM teachers t
        LEFT JOIN users u ON t.user_id = u.id
        WHERE COALESCE(t.clerk_user_id, u.clerk_user_id) = current_setting('app.clerk_user_id')::TEXT
      ) OR
      visibility IN ('class', 'school', 'public') OR
      id IN (
        SELECT material_id FROM material_shares 
        WHERE shared_with_type = 'teacher' AND 
        shared_with_id = (
          SELECT t.id FROM teachers t
          LEFT JOIN users u ON t.user_id = u.id
          WHERE COALESCE(t.clerk_user_id, u.clerk_user_id) = current_setting('app.clerk_user_id')::TEXT
        )
      )
    )
  );

-- RLS Policies for material shares
CREATE POLICY "Teachers can manage material shares" ON material_shares
  FOR ALL USING (
    tenant_id = current_setting('app.tenant_id')::UUID AND
    shared_by = (
      SELECT t.id FROM teachers t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE COALESCE(t.clerk_user_id, u.clerk_user_id) = current_setting('app.clerk_user_id')::TEXT
    )
  );

-- RLS Policies for curriculum plans
CREATE POLICY "Teachers can manage their curriculum plans" ON curriculum_plans
  FOR ALL USING (
    tenant_id = current_setting('app.tenant_id')::UUID AND
    teacher_id = (
      SELECT t.id FROM teachers t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE COALESCE(t.clerk_user_id, u.clerk_user_id) = current_setting('app.clerk_user_id')::TEXT
    )
  );

-- RLS Policies for curriculum lessons
CREATE POLICY "Teachers can manage their curriculum lessons" ON curriculum_lessons
  FOR ALL USING (
    tenant_id = current_setting('app.tenant_id')::UUID AND
    curriculum_plan_id IN (
      SELECT id FROM curriculum_plans 
      WHERE teacher_id = (
        SELECT t.id FROM teachers t
        LEFT JOIN users u ON t.user_id = u.id
        WHERE COALESCE(t.clerk_user_id, u.clerk_user_id) = current_setting('app.clerk_user_id')::TEXT
      )
    )
  );

-- RLS Policies for material categories
CREATE POLICY "Tenant isolation for material categories" ON material_categories
  FOR ALL USING (tenant_id = current_setting('app.tenant_id')::UUID);

CREATE POLICY "Tenant isolation for material category assignments" ON material_category_assignments
  FOR ALL USING (
    material_id IN (
      SELECT id FROM materials 
      WHERE tenant_id = current_setting('app.tenant_id')::UUID
    )
  );

-- Function to update material view count
CREATE OR REPLACE FUNCTION increment_material_view_count(material_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE materials 
  SET view_count = view_count + 1, updated_at = NOW()
  WHERE id = material_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update material download count
CREATE OR REPLACE FUNCTION increment_material_download_count(material_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE materials 
  SET download_count = download_count + 1, updated_at = NOW()
  WHERE id = material_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get teacher's material statistics
CREATE OR REPLACE FUNCTION get_teacher_material_stats(teacher_id UUID)
RETURNS TABLE (
  total_materials INTEGER,
  total_views INTEGER,
  total_downloads INTEGER,
  materials_by_type JSONB,
  recent_materials INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_materials,
    COALESCE(SUM(view_count), 0)::INTEGER as total_views,
    COALESCE(SUM(download_count), 0)::INTEGER as total_downloads,
    COALESCE(
      jsonb_object_agg(
        material_type, 
        type_count
      ), 
      '{}'::jsonb
    ) as materials_by_type,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END)::INTEGER as recent_materials
  FROM (
    SELECT 
      material_type,
      view_count,
      download_count,
      created_at,
      COUNT(*) as type_count
    FROM materials 
    WHERE uploaded_by = teacher_id AND is_active = TRUE
    GROUP BY material_type, view_count, download_count, created_at
  ) stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search materials with filters
CREATE OR REPLACE FUNCTION search_materials(
  p_teacher_id UUID,
  p_search_query TEXT DEFAULT NULL,
  p_subject VARCHAR(100) DEFAULT NULL,
  p_material_type VARCHAR(50) DEFAULT NULL,
  p_grade_level VARCHAR(50) DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL,
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  title VARCHAR(255),
  description TEXT,
  subject VARCHAR(100),
  grade_level VARCHAR(50),
  material_type VARCHAR(50),
  file_url TEXT,
  file_type VARCHAR(50),
  tags TEXT[],
  view_count INTEGER,
  download_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  uploaded_by_name VARCHAR(255)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    m.id,
    m.title,
    m.description,
    m.subject,
    m.grade_level,
    m.material_type,
    m.file_url,
    m.file_type,
    m.tags,
    m.view_count,
    m.download_count,
    m.created_at,
    COALESCE(u.name, t.name) as uploaded_by_name
  FROM materials m
  JOIN teachers t ON m.uploaded_by = t.id
  LEFT JOIN users u ON t.user_id = u.id
  WHERE 
    (m.uploaded_by = p_teacher_id OR m.visibility IN ('class', 'school', 'public')) AND
    (p_search_query IS NULL OR 
     m.title ILIKE '%' || p_search_query || '%' OR 
     m.description ILIKE '%' || p_search_query || '%') AND
    (p_subject IS NULL OR m.subject = p_subject) AND
    (p_material_type IS NULL OR m.material_type = p_material_type) AND
    (p_grade_level IS NULL OR m.grade_level = p_grade_level) AND
    (p_tags IS NULL OR m.tags && p_tags)
  ORDER BY m.created_at DESC
  LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update curriculum plan progress
CREATE OR REPLACE FUNCTION update_curriculum_progress()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the curriculum plan's completed lessons count
  UPDATE curriculum_plans 
  SET 
    completed_lessons = (
      SELECT COUNT(*) 
      FROM curriculum_lessons 
      WHERE curriculum_plan_id = COALESCE(NEW.curriculum_plan_id, OLD.curriculum_plan_id) 
      AND status = 'completed'
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.curriculum_plan_id, OLD.curriculum_plan_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for curriculum progress updates
DROP TRIGGER IF EXISTS trigger_update_curriculum_progress ON curriculum_lessons;
CREATE TRIGGER trigger_update_curriculum_progress
  AFTER INSERT OR UPDATE OR DELETE ON curriculum_lessons
  FOR EACH ROW EXECUTE FUNCTION update_curriculum_progress();

-- Note: Default material categories should be created by the application
-- for each tenant when they first access the materials system 