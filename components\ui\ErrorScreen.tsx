import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { IconSymbol } from './IconSymbol';

interface ErrorScreenProps {
  message: string;
  onRetry?: () => void;
}

export function ErrorScreen({ message, onRetry }: ErrorScreenProps) {
  const isDark = useColorScheme() === 'dark';

  return (
    <View className="flex-1 justify-center items-center p-4">
      <IconSymbol
        name="exclamationmark.triangle.fill"
        size={64}
        color={isDark ? '#EF4444' : '#DC2626'}
      />
      <Text className={`text-lg font-rubik-bold mt-4 text-center ${
        isDark ? 'text-dark-text' : 'text-light-text'
      }`}>
        Something went wrong
      </Text>
      <Text className={`text-center mt-2 ${
        isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
      }`}>
        {message}
      </Text>
      {onRetry && (
        <TouchableOpacity
          onPress={onRetry}
          className="mt-6 bg-primary-500 px-6 py-3 rounded-xl"
        >
          <Text className="text-white font-rubik-medium">Try Again</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
