import React from 'react';
import { View, Text } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

interface RecentActivityProps {
  // Future: Add activities prop when implementing real activity data
  // activities?: Activity[];
}

export const RecentActivity: React.FC<RecentActivityProps> = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <View className="mb-6">
      {/* Section Title */}
      <Text 
        className={`
          text-lg font-rubik-semibold mb-4
          ${isDark ? 'text-dark-text' : 'text-light-text'}
        `}
      >
        Recent Activity
      </Text>
      
      {/* Activity Container */}
      <View 
        className={`
          p-4 rounded-xl
          ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}
          shadow-sm
        `}
        style={{
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        }}
      >
        <Text 
          className={`
            text-center italic font-rubik
            ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}
          `}
        >
          Recent activity will appear here
        </Text>
      </View>
    </View>
  );
};
