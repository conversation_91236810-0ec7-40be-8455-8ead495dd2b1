import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Alert,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';
import { AssignmentData } from '../AssignmentCreationWizard';

interface RubricsStepProps {
  data: AssignmentData;
  updateData: (updates: Partial<AssignmentData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

interface RubricCriteria {
  criteria_name: string;
  description: string;
  max_points: number;
  order_index: number;
}

const RUBRIC_TEMPLATES = [
  {
    id: 'essay',
    title: 'Essay Rubric',
    criteria: [
      { criteria_name: 'Content & Understanding', description: 'Demonstrates clear understanding of the topic with relevant examples', max_points: 40, order_index: 0 },
      { criteria_name: 'Organization & Structure', description: 'Well-organized with clear introduction, body, and conclusion', max_points: 30, order_index: 1 },
      { criteria_name: 'Grammar & Style', description: 'Proper grammar, spelling, and writing style', max_points: 20, order_index: 2 },
      { criteria_name: 'Citations & References', description: 'Proper use of citations and credible sources', max_points: 10, order_index: 3 },
    ],
  },
  {
    id: 'presentation',
    title: 'Presentation Rubric',
    criteria: [
      { criteria_name: 'Content Knowledge', description: 'Demonstrates thorough understanding of the subject', max_points: 35, order_index: 0 },
      { criteria_name: 'Delivery & Speaking', description: 'Clear speech, good pace, and engaging presentation', max_points: 25, order_index: 1 },
      { criteria_name: 'Visual Aids', description: 'Effective use of slides, charts, or other visual materials', max_points: 25, order_index: 2 },
      { criteria_name: 'Time Management', description: 'Stays within time limits and covers all required points', max_points: 15, order_index: 3 },
    ],
  },
  {
    id: 'project',
    title: 'Project Rubric',
    criteria: [
      { criteria_name: 'Research & Analysis', description: 'Thorough research with critical analysis of information', max_points: 30, order_index: 0 },
      { criteria_name: 'Creativity & Innovation', description: 'Original ideas and creative approach to the project', max_points: 25, order_index: 1 },
      { criteria_name: 'Technical Execution', description: 'Quality of work and attention to detail', max_points: 25, order_index: 2 },
      { criteria_name: 'Presentation & Communication', description: 'Clear communication of ideas and findings', max_points: 20, order_index: 3 },
    ],
  },
];

export default function RubricsStep({ data, updateData, onNext, onPrev }: RubricsStepProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  
  const [showTemplates, setShowTemplates] = useState(false);
  const [editingCriteria, setEditingCriteria] = useState<number | null>(null);

  const addCriteria = () => {
    const newCriteria: RubricCriteria = {
      criteria_name: '',
      description: '',
      max_points: 10,
      order_index: data.rubrics.length,
    };
    
    updateData({ rubrics: [...data.rubrics, newCriteria] });
    setEditingCriteria(data.rubrics.length);
  };

  const updateCriteria = (index: number, updates: Partial<RubricCriteria>) => {
    const updatedRubrics = data.rubrics.map((criteria, i) =>
      i === index ? { ...criteria, ...updates } : criteria
    );
    updateData({ rubrics: updatedRubrics });
  };

  const removeCriteria = (index: number) => {
    const updatedRubrics = data.rubrics
      .filter((_, i) => i !== index)
      .map((criteria, i) => ({ ...criteria, order_index: i }));
    updateData({ rubrics: updatedRubrics });
  };

  const applyTemplate = (template: typeof RUBRIC_TEMPLATES[0]) => {
    Alert.alert(
      'Use Template',
      `This will replace your current rubric with the "${template.title}" template. Continue?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Use Template',
          onPress: () => {
            updateData({ rubrics: template.criteria });
            setShowTemplates(false);
          },
        },
      ]
    );
  };

  const getTotalPoints = () => {
    return data.rubrics.reduce((total, criteria) => total + criteria.max_points, 0);
  };

  const handleNext = () => {
    const totalPoints = getTotalPoints();
    if (data.rubrics.length > 0 && totalPoints !== data.max_points) {
      Alert.alert(
        'Points Mismatch',
        `The total rubric points (${totalPoints}) don't match the assignment max points (${data.max_points}). Would you like to continue anyway?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Continue', onPress: onNext },
        ]
      );
    } else {
      onNext();
    }
  };

  const renderCriteriaItem = (criteria: RubricCriteria, index: number) => (
    <Animated.View
      key={index}
      entering={FadeInRight.delay(index * 100).duration(300)}
      className={`p-3 rounded-lg mb-2 ${
        isDark ? 'bg-dark-card' : 'bg-light-card'
      }`}
    >
      {editingCriteria === index ? (
        <View className="space-y-2">
          <TextInput
            value={criteria.criteria_name}
            onChangeText={(text) => updateCriteria(index, { criteria_name: text })}
            placeholder="Criteria name"
            placeholderTextColor={isDark ? '#666' : '#999'}
            className={`font-rubik-medium text-base p-3 rounded-lg ${
              isDark ? 'bg-dark-background text-dark-text' : 'bg-light-background text-light-text'
            }`}
          />
          
          <TextInput
            value={criteria.description}
            onChangeText={(text) => updateCriteria(index, { description: text })}
            placeholder="Description of what students need to achieve"
            placeholderTextColor={isDark ? '#666' : '#999'}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            className={`font-rubik p-3 rounded-lg ${
              isDark ? 'bg-dark-background text-dark-text' : 'bg-light-background text-light-text'
            }`}
          />
          
          <View className="flex-row items-center space-x-3">
            <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Points:
            </Text>
            <TextInput
              value={criteria.max_points.toString()}
              onChangeText={(text) => {
                const points = parseInt(text) || 0;
                updateCriteria(index, { max_points: points });
              }}
              keyboardType="numeric"
              className={`font-rubik p-2 rounded-lg w-20 text-center ${
                isDark ? 'bg-dark-background text-dark-text' : 'bg-light-background text-light-text'
              }`}
            />
          </View>
          
          <View className="flex-row justify-end space-x-2">
            <TouchableOpacity
              onPress={() => setEditingCriteria(null)}
              className="px-4 py-2 rounded-lg bg-green-500"
            >
              <Text className="text-white font-rubik-medium">Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View>
          <View className="flex-row items-start justify-between mb-2">
            <View className="flex-1">
              <Text className={`font-rubik-bold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {criteria.criteria_name || 'Untitled Criteria'}
              </Text>
              <Text className={`font-rubik text-sm mt-1 ${
                isDark ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {criteria.description || 'No description provided'}
              </Text>
            </View>
            
            <View className="flex-row items-center space-x-2 ml-3">
              <View className="bg-primary-500 px-3 py-1 rounded-full">
                <Text className="text-white font-rubik-bold text-sm">
                  {criteria.max_points} pts
                </Text>
              </View>
              
              <TouchableOpacity
                onPress={() => setEditingCriteria(index)}
                className="p-2"
              >
                <Ionicons name="pencil-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={() => removeCriteria(index)}
                className="p-2"
              >
                <Ionicons name="trash-outline" size={16} color="#EF4444" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </Animated.View>
  );

  return (
    <View className="flex-1 p-5">
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 20 }}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="space-y-5">
          {/* Header */}
          <View>
            <Text className={`text-xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Grading Rubric
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Define clear criteria for grading this assignment. This helps ensure consistent and fair evaluation.
            </Text>
          </View>

          {/* Template Toggle */}
          <TouchableOpacity
            onPress={() => setShowTemplates(!showTemplates)}
            className={`flex-row items-center justify-between p-3 rounded-lg ${
              isDark ? 'bg-dark-card' : 'bg-light-card'
            }`}
          >
            <View className="flex-row items-center">
              <Ionicons
                name="list-outline"
                size={20}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`ml-3 font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Use Rubric Template
              </Text>
            </View>
            <Ionicons
              name={showTemplates ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          </TouchableOpacity>

          {/* Templates */}
          {showTemplates && (
            <Animated.View entering={FadeInDown.duration(300)} className="space-y-2">
              {RUBRIC_TEMPLATES.map((template) => (
                <TouchableOpacity
                  key={template.id}
                  onPress={() => applyTemplate(template)}
                  className={`p-3 rounded-lg border ${
                    isDark ? 'border-dark-border bg-dark-card' : 'border-light-border bg-light-card'
                  }`}
                >
                  <Text className={`font-rubik-medium text-base mb-2 ${
                    isDark ? 'text-dark-text' : 'text-light-text'
                  }`}>
                    {template.title}
                  </Text>
                  <Text className={`font-rubik text-sm ${
                    isDark ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    {template.criteria.length} criteria • {template.criteria.reduce((sum, c) => sum + c.max_points, 0)} total points
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          )}

          {/* Current Rubric */}
          <View className="space-y-2 mt-1">
            <View className="flex-row items-center justify-between">
              <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Grading Criteria
              </Text>
              <View className="flex-row items-center space-x-4">
                <Text className={`font-rubik text-sm ${
                  getTotalPoints() === data.max_points
                    ? 'text-green-500'
                    : 'text-orange-500'
                }`}>
                  {getTotalPoints()}/{data.max_points} pts
                </Text>
                <TouchableOpacity
                  onPress={addCriteria}
                  className="bg-primary-500 px-4 py-2 rounded-full"
                >
                  <Text className="text-white font-rubik-medium text-sm">Add Criteria</Text>
                </TouchableOpacity>
              </View>
            </View>

            {data.rubrics.length === 0 ? (
              <View className={`p-4 rounded-xl border-2 border-dashed items-center ${
                isDark ? 'border-dark-border bg-dark-card' : 'border-light-border bg-light-card'
              }`}>
                <Ionicons
                  name="list-outline"
                  size={32}
                  color={isDark ? '#4B5563' : '#9CA3AF'}
                />
                <Text className={`font-rubik-medium mt-2 text-center ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  No grading criteria added yet
                </Text>
                <Text className={`font-rubik text-xs text-center mt-1 ${
                  isDark ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Add criteria to create a structured grading rubric
                </Text>
              </View>
            ) : (
              data.rubrics.map(renderCriteriaItem)
            )}
          </View>

          {/* Skip Option - Compact */}
          {data.rubrics.length === 0 && (
            <View className={`p-3 rounded-lg mt-2 ${isDark ? 'bg-blue-900/10' : 'bg-blue-50/50'}`}>
              <Text className={`font-rubik text-center text-sm ${isDark ? 'text-blue-300' : 'text-blue-700'}`}>
                You can skip this step and grade assignments manually, or add criteria for structured grading.
              </Text>
            </View>
          )}
        </Animated.View>
      </ScrollView>


    </View>
  );
}
