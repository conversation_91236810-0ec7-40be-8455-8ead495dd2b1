import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { RefreshControl, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

interface TrendData {
  date: string;
  totalSessions: number;
  totalStudents: number;
  presentCount: number;
  absentCount: number;
  attendanceRate: number;
}

interface WeeklyTrend {
  week: string;
  weekStart: string;
  weekEnd: string;
  totalSessions: number;
  averageAttendance: number;
  trend: 'up' | 'down' | 'stable';
}

const TrendsReportsScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { userId: clerkUserId } = useAuth();

  const [dailyTrends, setDailyTrends] = useState<TrendData[]>([]);
  const [weeklyTrends, setWeeklyTrends] = useState<WeeklyTrend[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');

  const { sessions, attendanceStats, loadSessions, loadAttendanceStats } = useAttendanceStore();
  const { currentTeacher, loadTeacherData } = useEnrollmentStore();

  // Load data
  useEffect(() => {
    const initializeData = async () => {
      if (clerkUserId && !currentTeacher) {
        await loadTeacherData(clerkUserId);
      }
    };
    initializeData();
  }, [clerkUserId, currentTeacher, loadTeacherData]);

  useEffect(() => {
    const loadTrendsData = async () => {
      if (currentTeacher?.id) {
        try {
          setIsLoading(true);
          setError(null);

          const today = new Date();
          let startDate: Date;

          switch (selectedPeriod) {
            case 'week':
              startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
              break;
            case 'month':
              startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
              break;
            case 'quarter':
              startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
              break;
          }

          await loadSessions(currentTeacher.id, undefined, {
            start: startDate.toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          });
          await loadAttendanceStats(currentTeacher.id);

        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to load trends data');
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadTrendsData();
  }, [currentTeacher?.id, selectedPeriod, loadSessions, loadAttendanceStats]);

  // Calculate trends
  useEffect(() => {
    if (sessions.length > 0) {
      calculateDailyTrends();
      calculateWeeklyTrends();
    }
  }, [sessions]);

  const calculateDailyTrends = () => {
    const dailyData: { [key: string]: TrendData } = {};

    sessions.forEach(session => {
      const date = session.session_date;
      
      if (!dailyData[date]) {
        dailyData[date] = {
          date,
          totalSessions: 0,
          totalStudents: 0,
          presentCount: 0,
          absentCount: 0,
          attendanceRate: 0
        };
      }

      dailyData[date].totalSessions++;
      dailyData[date].totalStudents += session.total_students;
      dailyData[date].presentCount += session.present_count;
      dailyData[date].absentCount += (session.total_students - session.present_count);
    });

    // Calculate attendance rates
    Object.values(dailyData).forEach(day => {
      day.attendanceRate = day.totalStudents > 0 
        ? (day.presentCount / day.totalStudents) * 100 
        : 0;
    });

    const sortedTrends = Object.values(dailyData).sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    setDailyTrends(sortedTrends);
  };

  const calculateWeeklyTrends = () => {
    const weeklyData: { [key: string]: WeeklyTrend } = {};

    sessions.forEach(session => {
      const sessionDate = new Date(session.session_date);
      const weekStart = new Date(sessionDate);
      weekStart.setDate(sessionDate.getDate() - sessionDate.getDay());
      
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = {
          week: `Week of ${weekStart.toLocaleDateString()}`,
          weekStart: weekStart.toISOString().split('T')[0],
          weekEnd: weekEnd.toISOString().split('T')[0],
          totalSessions: 0,
          averageAttendance: 0,
          trend: 'stable'
        };
      }

      weeklyData[weekKey].totalSessions++;
      const attendanceRate = session.total_students > 0 
        ? (session.present_count / session.total_students) * 100 
        : 0;
      weeklyData[weekKey].averageAttendance += attendanceRate;
    });

    // Calculate averages and trends
    const weeks = Object.values(weeklyData).sort((a, b) => 
      new Date(a.weekStart).getTime() - new Date(b.weekStart).getTime()
    );

    weeks.forEach((week, index) => {
      week.averageAttendance = week.totalSessions > 0 
        ? week.averageAttendance / week.totalSessions 
        : 0;

      if (index > 0) {
        const previousWeek = weeks[index - 1];
        const difference = week.averageAttendance - previousWeek.averageAttendance;
        
        if (difference > 2) week.trend = 'up';
        else if (difference < -2) week.trend = 'down';
        else week.trend = 'stable';
      }
    });

    setWeeklyTrends(weeks);
  };

  const handleRefresh = async () => {
    if (currentTeacher?.id) {
      setRefreshing(true);
      try {
        const today = new Date();
        let startDate: Date;

        switch (selectedPeriod) {
          case 'week':
            startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case 'quarter':
            startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
        }

        await loadSessions(currentTeacher.id, undefined, {
          start: startDate.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        });
        await loadAttendanceStats(currentTeacher.id);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to refresh data');
      } finally {
        setRefreshing(false);
      }
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return { name: 'arrow.up.circle.fill', color: '#10B981' };
      case 'down':
        return { name: 'arrow.down.circle.fill', color: '#EF4444' };
      default:
        return { name: 'minus.circle.fill', color: '#6B7280' };
    }
  };

  const getAttendanceColor = (rate: number) => {
    if (rate >= 90) return 'text-success';
    if (rate >= 75) return 'text-warning';
    return 'text-error';
  };

  if (isLoading) {
    return <LoadingScreen message="Loading attendance trends..." />;
  }

  if (error) {
    return (
      <ErrorScreen
        title="Trends Error"
        message={error}
        onRetry={() => {
          setError(null);
          handleRefresh();
        }}
      />
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={() => router.back()}
            className="mr-4"
          >
            <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Attendance Trends
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Track patterns over time
            </Text>
          </View>
        </View>
      </View>

      {/* Period Selector */}
      <View className="p-4">
        <View className="flex-row space-x-2">
          {(['week', 'month', 'quarter'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              onPress={() => setSelectedPeriod(period)}
              className={`flex-1 p-3 rounded-lg ${
                selectedPeriod === period
                  ? 'bg-primary-500'
                  : isDark ? 'bg-dark-surface' : 'bg-light-surface'
              }`}
            >
              <Text className={`text-center font-rubik-semibold capitalize ${
                selectedPeriod === period 
                  ? 'text-white' 
                  : isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                {period}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView
        className="flex-1 px-4"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={isDark ? '#60A5FA' : '#2563EB'}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Overall Trend Summary */}
        {attendanceStats && (
          <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Overall Performance
            </Text>
            <View className="flex-row justify-between">
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
                  {attendanceStats.total_sessions}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Sessions
                </Text>
              </View>
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl text-success`}>
                  {attendanceStats.present_percentage.toFixed(1)}%
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Avg Attendance
                </Text>
              </View>
              <View className="items-center">
                <Text className={`font-rubik-bold text-2xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {weeklyTrends.length}
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Weeks
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Weekly Trends */}
        <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Weekly Trends
          </Text>

          {weeklyTrends.length === 0 ? (
            <View className="items-center py-8">
              <IconSymbol
                name="calendar"
                size={48}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                No trend data available
              </Text>
              <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                Create more attendance sessions to see trends
              </Text>
            </View>
          ) : (
            <View className="space-y-3">
              {weeklyTrends.map((week, index) => {
                const trendIcon = getTrendIcon(week.trend);
                return (
                  <View
                    key={week.weekStart}
                    className={`p-3 rounded-lg border ${
                      isDark ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border'
                    }`}
                  >
                    <View className="flex-row items-center justify-between">
                      <View className="flex-1">
                        <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                          {week.week}
                        </Text>
                        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          {week.totalSessions} sessions
                        </Text>
                      </View>
                      <View className="items-center">
                        <View className="flex-row items-center">
                          <IconSymbol 
                            name={trendIcon.name as any} 
                            size={20} 
                            color={trendIcon.color} 
                          />
                          <Text className={`ml-2 font-rubik-semibold ${getAttendanceColor(week.averageAttendance)}`}>
                            {week.averageAttendance.toFixed(1)}%
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>
          )}
        </View>

        {/* Daily Breakdown */}
        <View className={`p-4 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Daily Breakdown
          </Text>

          {dailyTrends.length === 0 ? (
            <View className="items-center py-8">
              <IconSymbol
                name="chart.bar.fill"
                size={48}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                No daily data available
              </Text>
            </View>
          ) : (
            <View className="space-y-2">
              {dailyTrends.slice(-7).map((day) => (
                <View
                  key={day.date}
                  className={`p-3 rounded-lg ${
                    isDark ? 'bg-dark-background' : 'bg-light-background'
                  }`}
                >
                  <View className="flex-row items-center justify-between">
                    <View>
                      <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                        {new Date(day.date).toLocaleDateString('en-US', { 
                          weekday: 'short', 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </Text>
                      <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        {day.totalSessions} sessions • {day.totalStudents} students
                      </Text>
                    </View>
                    <View className="items-end">
                      <Text className={`font-rubik-bold ${getAttendanceColor(day.attendanceRate)}`}>
                        {day.attendanceRate.toFixed(1)}%
                      </Text>
                      <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        {day.presentCount}/{day.totalStudents}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Insights */}
        <View className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
          <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Insights
          </Text>
          <View className="space-y-2">
            {weeklyTrends.length > 1 && (
              <>
                {weeklyTrends.filter(w => w.trend === 'up').length > 0 && (
                  <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    • {weeklyTrends.filter(w => w.trend === 'up').length} week(s) showed improvement
                  </Text>
                )}
                {weeklyTrends.filter(w => w.trend === 'down').length > 0 && (
                  <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    • {weeklyTrends.filter(w => w.trend === 'down').length} week(s) showed decline
                  </Text>
                )}
                <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  • Best week: {Math.max(...weeklyTrends.map(w => w.averageAttendance)).toFixed(1)}% attendance
                </Text>
              </>
            )}
            {dailyTrends.length > 0 && (
              <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                • Total sessions analyzed: {dailyTrends.reduce((sum, day) => sum + day.totalSessions, 0)}
              </Text>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default TrendsReportsScreen;
