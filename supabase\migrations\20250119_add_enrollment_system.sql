-- Add is_class_teacher field to teachers table
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS is_class_teacher BOOLEAN DEFAULT FALSE;

-- Create class_students table for many-to-many relationship between classes and students
CREATE TABLE IF NOT EXISTS class_students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  class_id UUID NOT NULL REFERENCES classes(id),
  student_id UUID NOT NULL REFERENCES students(id),
  enrollment_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'transferred')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  UNIQUE(class_id, student_id)
);

-- Add enrollment_code to students table if not exists
ALTER TABLE students ADD COLUMN IF NOT EXISTS enrollment_code VARCHAR(50) UNIQUE;

-- Add grade and section to classes table if not exists
ALTER TABLE classes ADD COLUMN IF NOT EXISTS grade VARCHAR(50);
ALTER TABLE classes ADD COLUMN IF NOT EXISTS section VARCHAR(50);

-- Create enrollment_requests table for pending enrollments
CREATE TABLE IF NOT EXISTS enrollment_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  teacher_id UUID NOT NULL REFERENCES teachers(id),
  class_id UUID NOT NULL REFERENCES classes(id),
  student_name VARCHAR(255) NOT NULL,
  student_email VARCHAR(255) NOT NULL,
  student_grade VARCHAR(50),
  student_section VARCHAR(50),
  roll_number VARCHAR(50),
  date_of_birth DATE,
  parent_name VARCHAR(255),
  parent_email VARCHAR(255),
  parent_phone VARCHAR(20),
  enrollment_code VARCHAR(50) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Add RLS policies for new tables
ALTER TABLE class_students ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollment_requests ENABLE ROW LEVEL SECURITY;

-- Policies for class_students
CREATE POLICY "Class students can be viewed by teachers of the class" ON class_students
  FOR SELECT USING (
    tenant_id = (current_setting('app.tenant_id', true))::UUID AND
    class_id IN (
      SELECT id FROM classes 
      WHERE teacher_id = (
        SELECT id FROM teachers 
        WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      )
    )
  );

CREATE POLICY "Class teachers can insert class students" ON class_students
  FOR INSERT WITH CHECK (
    tenant_id = (current_setting('app.tenant_id', true))::UUID AND
    class_id IN (
      SELECT id FROM classes 
      WHERE teacher_id = (
        SELECT id FROM teachers 
        WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        AND is_class_teacher = TRUE
      )
    )
  );

CREATE POLICY "Class teachers can update class students" ON class_students
  FOR UPDATE USING (
    tenant_id = (current_setting('app.tenant_id', true))::UUID AND
    class_id IN (
      SELECT id FROM classes 
      WHERE teacher_id = (
        SELECT id FROM teachers 
        WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
        AND is_class_teacher = TRUE
      )
    )
  );

-- Policies for enrollment_requests
CREATE POLICY "Enrollment requests can be viewed by class teachers" ON enrollment_requests
  FOR SELECT USING (
    tenant_id = (current_setting('app.tenant_id', true))::UUID AND
    teacher_id = (
      SELECT id FROM teachers 
      WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      AND is_class_teacher = TRUE
    )
  );

CREATE POLICY "Class teachers can insert enrollment requests" ON enrollment_requests
  FOR INSERT WITH CHECK (
    tenant_id = (current_setting('app.tenant_id', true))::UUID AND
    teacher_id = (
      SELECT id FROM teachers 
      WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      AND is_class_teacher = TRUE
    )
  );

CREATE POLICY "Class teachers can update enrollment requests" ON enrollment_requests
  FOR UPDATE USING (
    tenant_id = (current_setting('app.tenant_id', true))::UUID AND
    teacher_id = (
      SELECT id FROM teachers 
      WHERE clerk_user_id = (current_setting('app.clerk_user_id', true))::TEXT
      AND is_class_teacher = TRUE
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_class_students_class_id ON class_students(class_id);
CREATE INDEX IF NOT EXISTS idx_class_students_student_id ON class_students(student_id);
CREATE INDEX IF NOT EXISTS idx_class_students_tenant_id ON class_students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_teacher_id ON enrollment_requests(teacher_id);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_class_id ON enrollment_requests(class_id);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_tenant_id ON enrollment_requests(tenant_id);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_status ON enrollment_requests(status);
CREATE INDEX IF NOT EXISTS idx_enrollment_requests_enrollment_code ON enrollment_requests(enrollment_code);
CREATE INDEX IF NOT EXISTS idx_teachers_is_class_teacher ON teachers(is_class_teacher);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_code ON students(enrollment_code);

-- Function to generate unique enrollment code
CREATE OR REPLACE FUNCTION generate_enrollment_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  exists_check INTEGER;
BEGIN
  LOOP
    -- Generate a 8-character alphanumeric code
    code := upper(substring(md5(random()::text) from 1 for 8));
    
    -- Check if code already exists in enrollment_requests or students
    SELECT COUNT(*) INTO exists_check 
    FROM (
      SELECT enrollment_code FROM enrollment_requests WHERE enrollment_code = code
      UNION
      SELECT enrollment_code FROM students WHERE enrollment_code = code
    ) AS existing_codes;
    
    -- If code doesn't exist, return it
    IF exists_check = 0 THEN
      RETURN code;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate enrollment code for enrollment_requests
CREATE OR REPLACE FUNCTION set_enrollment_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.enrollment_code IS NULL OR NEW.enrollment_code = '' THEN
    NEW.enrollment_code := generate_enrollment_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_enrollment_code
  BEFORE INSERT ON enrollment_requests
  FOR EACH ROW
  EXECUTE FUNCTION set_enrollment_code();

-- Update updated_at timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_class_students_updated_at
  BEFORE UPDATE ON class_students
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_enrollment_requests_updated_at
  BEFORE UPDATE ON enrollment_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
