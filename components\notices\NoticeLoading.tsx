import React, { memo } from 'react';
import {
  View,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

const NoticeLoading: React.FC = memo(() => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <SafeAreaView
      className={`flex-1 ${
        isDark ? "bg-dark-background" : "bg-light-background"
      }`}
    >
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator
          size="large"
          color={isDark ? Colors.dark.primary : Colors.light.primary}
        />
      </View>
    </SafeAreaView>
  );
});

NoticeLoading.displayName = 'NoticeLoading';

export default NoticeLoading;
