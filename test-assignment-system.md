# Testing the Assignment System

## Quick Start (Fixed the "User not authenticated" error!)

### Step 1: Fix Authentication
1. **Start your app**: `npm start` or `expo start`
2. **Go to the Debug tab** in your app (new tab added)
3. **Go to the "Auth" tab** first
4. **Click "Check Authentication Status"** to see what's wrong
5. **Click "Create Missing Records"** if user/teacher records are missing

### Step 2: Test Assignment System
1. **Go to the "Debugger" tab**
2. **Run "Debug User & Teacher"** to verify everything is set up
3. **Run "Create Test Class"** to have a class for assignments
4. **Run "Test Assignment Creation"** to verify everything works

### Step 2: Set Up Environment
Make sure your `.env` file has:
```
EXPO_PUBLIC_GEMINI_API_KEY=your_api_key_here
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## Prerequisites
1. Make sure Docker Desktop is running (for database migrations)
2. Start your development server: `npm start` or `expo start`
3. Use the Debug tab to set up teacher account automatically

## Testing Steps

### 1. Test Basic Assignment Creation
1. **Navigate to Assignment Creation**
   - Go to the assignments section in your app
   - Click "Create Assignment" or use the quick actions

2. **Create a Test Assignment**
   - Title: "Test Assignment - Math Problems"
   - Description: "Solve the following algebra problems"
   - Instructions: "Show all work and box your final answers"
   - Max Points: 100
   - Due Date: Set to tomorrow
   - Add some rubric criteria

3. **Verify Assignment Creation**
   - Check that the assignment appears in your assignments list
   - Verify all fields are saved correctly

### 2. Test AI-Assisted Grading
1. **Create a Mock Submission**
   - You'll need to manually insert a submission in the database or create a student account
   - Add some sample content to the submission

2. **Test AI Grading**
   - Go to the assignment's submissions view
   - Click on a submission to grade it
   - Click the "Generate AI Feedback" button
   - Verify that AI feedback is generated (requires Gemini API key)

3. **Expected Results**
   - AI should provide feedback, strengths, improvements
   - A suggested grade should be calculated
   - Feedback should be saved to the submission

### 3. Test Assignment Analytics
1. **Access Analytics**
   - Open an assignment that has submissions
   - Navigate to the Analytics tab/section

2. **Verify Analytics Features**
   - Check Overview tab shows key metrics
   - Performance tab displays grade distribution
   - Trends tab shows submission timeline
   - Comparison tab compares with other assignments

3. **Expected Results**
   - Charts and graphs display correctly
   - Data is calculated accurately
   - All tabs switch properly

### 4. Test Notification System
1. **Access Notification Center**
   - Look for a notifications icon/section in your app
   - Open the notification center

2. **Test Notification Settings**
   - Go to notification settings
   - Toggle different notification types
   - Save preferences

3. **Test Notification Creation**
   - Create an assignment with a due date
   - Submit an assignment (if you have student access)
   - Check if notifications are generated

### 5. Test Template System
1. **Create a Template**
   - Open an existing assignment
   - Look for "Save as Template" or "Create Template" option
   - Fill in template details and save

2. **Use a Template**
   - Go to assignment creation
   - Look for "Use Template" or "Template Library"
   - Select a template and create an assignment from it

3. **Manage Templates**
   - Access "My Templates" section
   - Edit, duplicate, or delete templates
   - Toggle public/private status

## API Testing (Optional)

### Test with Postman or curl

1. **Test Template Creation**
```bash
curl -X POST http://localhost:8081/api/assignment-templates \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template",
    "description": "A test template",
    "category": "homework",
    "subject": "math",
    "gradeLevel": "High School",
    "templateData": {
      "title": "Math Homework",
      "description": "Practice problems",
      "instructions": "Solve all problems",
      "maxPoints": 50
    },
    "isPublic": false,
    "tags": ["math", "homework"]
  }'
```

2. **Test AI Grading**
```bash
curl -X POST http://localhost:8081/api/assignments/{assignment-id}/submissions/{submission-id}/ai-grade \
  -H "Content-Type: application/json"
```

3. **Test Analytics**
```bash
curl -X GET http://localhost:8081/api/assignments/{assignment-id}/analytics
```

## Troubleshooting

### Common Issues

1. **Gemini API Key Missing**
   - Add `EXPO_PUBLIC_GEMINI_API_KEY` to your `.env` file
   - Get API key from Google AI Studio

2. **Database Tables Missing**
   - Run the migration files manually in Supabase dashboard
   - Or use `npx supabase db reset` (requires Docker)

3. **Authentication Issues**
   - Make sure you're logged in as a teacher
   - Check that user has proper permissions

4. **Import Errors**
   - Verify all new files are properly imported
   - Check for syntax errors in TypeScript files

### Database Setup (Manual)

If you can't run migrations automatically, manually create these tables in Supabase:

1. **assignment_notifications**
2. **notification_preferences** 
3. **assignment_templates**
4. **template_usage_log**
5. **template_ratings**
6. **template_favorites**

Copy the SQL from the migration files and run in Supabase SQL editor.

## Expected Behavior

### AI Grading
- Should generate meaningful feedback
- Provide numerical scores
- List strengths and improvements
- Work with rubrics if available

### Analytics
- Display real-time data
- Show interactive charts
- Calculate percentages correctly
- Handle empty data gracefully

### Notifications
- Create notifications for key events
- Respect user preferences
- Mark as read/unread
- Support different priority levels

### Templates
- Save assignment structure
- Allow customization when using
- Support public/private sharing
- Track usage statistics

## Performance Testing

1. **Create Multiple Assignments** (10-20)
2. **Add Multiple Submissions** per assignment
3. **Test with Large Data Sets**
4. **Check Loading Times**
5. **Verify Memory Usage**

## User Experience Testing

1. **Navigation Flow**
   - Can users easily find features?
   - Are workflows intuitive?

2. **Error Handling**
   - What happens with invalid data?
   - Are error messages helpful?

3. **Mobile Responsiveness**
   - Test on different screen sizes
   - Check touch interactions

4. **Dark/Light Mode**
   - Verify both themes work
   - Check color contrast

## Next Steps After Testing

1. **Fix any bugs found**
2. **Optimize performance issues**
3. **Improve user experience**
4. **Add more test data**
5. **Document any limitations**

Remember to test incrementally - start with basic features and gradually test more complex functionality!
