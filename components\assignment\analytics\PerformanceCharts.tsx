import React from 'react';
import { View, Text, Dimensions } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface PerformanceData {
  totalSubmissions: number;
  gradedSubmissions: number;
  averageGrade: number;
  completionRate: number;
  gradeDistribution: { range: string; count: number; percentage: number }[];
  performanceMetrics: {
    highPerformers: number;
    averagePerformers: number;
    needsImprovement: number;
  };
}

interface Assignment {
  id: string;
  title: string;
  max_points: number;
}

interface PerformanceChartsProps {
  data: PerformanceData | null;
  assignment: Assignment;
}

export default function PerformanceCharts({ data, assignment }: PerformanceChartsProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const screenWidth = Dimensions.get('window').width;

  if (!data) {
    return (
      <View className="items-center justify-center py-8">
        <Text className={`font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          No performance data available
        </Text>
      </View>
    );
  }

  const renderPerformanceBreakdown = () => {
    const total = data.performanceMetrics.highPerformers + 
                  data.performanceMetrics.averagePerformers + 
                  data.performanceMetrics.needsImprovement;

    const highPercentage = (data.performanceMetrics.highPerformers / total) * 100;
    const averagePercentage = (data.performanceMetrics.averagePerformers / total) * 100;
    const lowPercentage = (data.performanceMetrics.needsImprovement / total) * 100;

    return (
      <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Performance Breakdown
        </Text>

        {/* Visual Bar Chart */}
        <View className="mb-4">
          <View className="flex-row h-8 rounded-lg overflow-hidden">
            <View 
              className="bg-green-500" 
              style={{ width: `${highPercentage}%` }}
            />
            <View 
              className="bg-yellow-500" 
              style={{ width: `${averagePercentage}%` }}
            />
            <View 
              className="bg-red-500" 
              style={{ width: `${lowPercentage}%` }}
            />
          </View>
        </View>

        {/* Legend */}
        <View className="space-y-3">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="w-4 h-4 bg-green-500 rounded mr-3" />
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                High Performers (90-100%)
              </Text>
            </View>
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {data.performanceMetrics.highPerformers} ({highPercentage.toFixed(1)}%)
            </Text>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="w-4 h-4 bg-yellow-500 rounded mr-3" />
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Average Performers (70-89%)
              </Text>
            </View>
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {data.performanceMetrics.averagePerformers} ({averagePercentage.toFixed(1)}%)
            </Text>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="w-4 h-4 bg-red-500 rounded mr-3" />
              <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Needs Improvement (<70%)
              </Text>
            </View>
            <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {data.performanceMetrics.needsImprovement} ({lowPercentage.toFixed(1)}%)
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderGradeDistributionChart = () => {
    const maxCount = Math.max(...data.gradeDistribution.map(item => item.count));

    return (
      <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Grade Distribution Chart
        </Text>

        <View className="space-y-4">
          {data.gradeDistribution.map((item, index) => {
            const barWidth = (item.count / maxCount) * 100;
            const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500'];
            const color = colors[data.gradeDistribution.length - 1 - index] || 'bg-gray-500';

            return (
              <View key={index} className="space-y-2">
                <View className="flex-row items-center justify-between">
                  <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {item.range}
                  </Text>
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                    {item.count} students ({item.percentage}%)
                  </Text>
                </View>
                <View className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                  <View 
                    className={`h-full ${color} rounded-lg`}
                    style={{ width: `${barWidth}%` }}
                  />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderStatsSummary = () => (
    <View className={`p-4 rounded-xl mb-4 ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
      <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        Performance Statistics
      </Text>

      <View className="grid grid-cols-2 gap-4">
        <View className={`p-3 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
          <Text className={`text-xl font-rubik-bold text-blue-500`}>
            {data.averageGrade.toFixed(1)}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Class Average
          </Text>
        </View>

        <View className={`p-3 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
          <Text className={`text-xl font-rubik-bold text-green-500`}>
            {((data.averageGrade / assignment.max_points) * 100).toFixed(1)}%
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Success Rate
          </Text>
        </View>

        <View className={`p-3 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
          <Text className={`text-xl font-rubik-bold text-purple-500`}>
            {data.completionRate.toFixed(1)}%
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Completion Rate
          </Text>
        </View>

        <View className={`p-3 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
          <Text className={`text-xl font-rubik-bold text-orange-500`}>
            {assignment.max_points}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Max Points
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <View className="space-y-4">
      <Animated.View entering={FadeInDown.delay(100).duration(400)}>
        {renderStatsSummary()}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(200).duration(400)}>
        {renderPerformanceBreakdown()}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        {renderGradeDistributionChart()}
      </Animated.View>
    </View>
  );
}
