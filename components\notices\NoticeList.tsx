import React, { memo } from 'react';
import {
  FlatList,
  Text,
  TouchableOpacity,
  View,
  RefreshControl,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Notice } from '@/stores/noticeStore';

interface NoticeListProps {
  notices: Notice[];
  refreshing: boolean;
  onRefresh: () => void;
  onNoticePress: (notice: Notice) => void;
  searchQuery: string;
}

const NoticeList: React.FC<NoticeListProps> = memo(({
  notices,
  refreshing,
  onRefresh,
  onNoticePress,
  searchQuery,
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Filter notices based on search query
  const filteredNotices = notices.filter((notice) => {
    return (
      notice.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (notice.content &&
        notice.content.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  // Render notice item
  const renderNoticeItem = ({ item }: { item: Notice }) => (
    <TouchableOpacity
      className={`p-4 rounded-xl mb-4 ${
        isDark ? "bg-dark-surface" : "bg-light-surface"
      }`}
      onPress={() => onNoticePress(item)}
      style={{
        elevation: 2,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}
    >
      <View className="flex-row justify-between items-start">
        <View className="flex-1 mr-2">
          <Text
            className={`text-lg font-rubik-medium mb-1 ${
              isDark ? "text-dark-text" : "text-light-text"
            }`}
            numberOfLines={1}
          >
            {item.title}
          </Text>
          <Text
            className={`mb-2 ${
              isDark ? "text-dark-textSecondary" : "text-light-textSecondary"
            }`}
            numberOfLines={1}
          >
            {formatDate(item.created_at)}
          </Text>
          {item.content && (
            <Text
              className={`${
                isDark ? "text-dark-textSecondary" : "text-light-textSecondary"
              }`}
              numberOfLines={2}
            >
              {item.content}
            </Text>
          )}
        </View>
        <View className="flex-row items-center">
          {item.file_url && (
            <IconSymbol
              name="doc.text.fill"
              size={20}
              color={isDark ? Colors.dark.secondary : Colors.light.secondary}
              style={{ marginRight: 8 }}
            />
          )}
          <IconSymbol
            name="chevron.right"
            size={20}
            color={
              isDark ? Colors.dark.textSecondary : Colors.light.textSecondary
            }
          />
        </View>
      </View>
    </TouchableOpacity>
  );

  // Empty state component
  const EmptyState = () => (
    <View className="flex-1 justify-center items-center p-8 mt-10">
      <IconSymbol
        name="megaphone.fill"
        size={50}
        color={isDark ? Colors.dark.secondary : Colors.light.secondary}
        style={{ marginBottom: 16, opacity: 0.7 }}
      />
      <Text
        className={`text-center text-lg font-rubik-medium mb-2 ${
          isDark ? "text-dark-text" : "text-light-text"
        }`}
      >
        {searchQuery ? "No matching notices found" : "No notices found"}
      </Text>
      <Text
        className={`text-center ${
          isDark
            ? "text-dark-textSecondary"
            : "text-light-textSecondary"
        }`}
      >
        {searchQuery
          ? "Try adjusting your search criteria."
          : "Check back later for new notices."}
      </Text>
    </View>
  );

  return (
    <FlatList
      data={filteredNotices}
      renderItem={renderNoticeItem}
      keyExtractor={(item) => item.id}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={isDark ? Colors.dark.primary : Colors.light.primary}
        />
      }
      ListEmptyComponent={EmptyState}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={filteredNotices.length === 0 ? { flex: 1 } : undefined}
    />
  );
});

NoticeList.displayName = 'NoticeList';

export default NoticeList;
