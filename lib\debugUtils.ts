import { supabase } from './supabase';

export const debugUserAndTeacher = async () => {
  try {
    console.log('=== DEBUG: Checking User and Teacher Records ===');

    // Import the helper function dynamically
    const { getCurrentUserAndTeacher } = await import('./authHelpers');

    // Try to get current user and teacher (will use global Clerk auth functions)
    try {
      const result = await getCurrentUserAndTeacher();
      console.log('✅ Successfully found user and teacher:', result);
      return result;
    } catch (error) {
      console.log('❌ Failed to get user and teacher:', error.message);

      // Try the fallback method
      const { getOrCreateUserAndTeacher } = await import('./authHelpers');
      try {
        const result = await getOrCreateUserAndTeacher();
        console.log('✅ Successfully created/found user and teacher:', result);
        return result;
      } catch (createError) {
        console.log('❌ Failed to create user and teacher:', createError.message);
        return { error: createError.message };
      }
    }

    // Check if user exists in users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('clerk_user_id', session.user.id);
    
    console.log('Users query result:', userData, userError);
    
    if (!userData || userData.length === 0) {
      console.log('❌ No user found with clerk_user_id:', session.user.id);
      
      // Let's see what users exist
      const { data: allUsers } = await supabase
        .from('users')
        .select('id, name, email, clerk_user_id, role')
        .limit(10);
      
      console.log('Available users:', allUsers);
      return;
    }

    const user = userData[0];
    console.log('✅ User found:', user);

    // Check if teacher record exists
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('*')
      .eq('user_id', user.id);
    
    console.log('Teacher query result:', teacherData, teacherError);
    
    if (!teacherData || teacherData.length === 0) {
      console.log('❌ No teacher record found for user_id:', user.id);
      
      // Let's see what teachers exist
      const { data: allTeachers } = await supabase
        .from('teachers')
        .select('id, user_id, tenant_id')
        .limit(10);
      
      console.log('Available teachers:', allTeachers);
      return;
    }

    const teacher = teacherData[0];
    console.log('✅ Teacher found:', teacher);
    
    // Check classes for this teacher
    const { data: classData } = await supabase
      .from('classes')
      .select('id, name, teacher_id')
      .eq('teacher_id', teacher.id);
    
    console.log('Classes for this teacher:', classData);
    
    console.log('=== DEBUG COMPLETE ===');
    
    return {
      session: session.user,
      user,
      teacher,
      classes: classData,
    };
    
  } catch (error) {
    console.error('Debug error:', error);
  }
};

export const createTestTeacherRecord = async () => {
  try {
    console.log('=== Creating Test Teacher Record ===');

    // Import the helper function dynamically
    const { getOrCreateUserAndTeacher } = await import('./authHelpers');

    // Use the helper to get or create user and teacher
    const result = await getOrCreateUserAndTeacher();
    console.log('✅ Teacher record ready:', result.teacher);
    return result.teacher;

  } catch (error) {
    console.error('Error creating test teacher:', error);
    throw error;
  }
};

export const createTestClass = async () => {
  try {
    console.log('=== Creating Test Class ===');
    
    // First ensure teacher exists
    const teacher = await createTestTeacherRecord();
    if (!teacher) {
      console.log('❌ Could not create/find teacher');
      return;
    }

    // Check if test class exists
    const { data: existingClass } = await supabase
      .from('classes')
      .select('*')
      .eq('teacher_id', teacher.id)
      .eq('name', 'Test Class')
      .single();
    
    if (existingClass) {
      console.log('✅ Test class already exists:', existingClass);
      return existingClass;
    }

    // Create test class
    const { data: newClass, error: createClassError } = await supabase
      .from('classes')
      .insert({
        name: 'Test Class',
        description: 'A test class for assignment testing',
        teacher_id: teacher.id,
        tenant_id: teacher.tenant_id,
        subject: 'General',
        grade_level: 'High School',
      })
      .select()
      .single();
    
    if (createClassError) {
      console.error('Error creating class:', createClassError);
      return;
    }
    
    console.log('✅ Test class created:', newClass);
    return newClass;
    
  } catch (error) {
    console.error('Error creating test class:', error);
  }
};
