import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useMaterialStore } from '@/stores/materialStore';
import React, { useCallback, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

const MATERIAL_TYPES = [
  { value: 'lesson_plan', label: 'Lesson Plans' },
  { value: 'worksheet', label: 'Worksheets' },
  { value: 'quiz', label: 'Quizzes' },
  { value: 'resource', label: 'Resources' },
  { value: 'assignment', label: 'Assignments' },
  { value: 'presentation', label: 'Presentations' },
];

export function MaterialFilters() {
  const isDark = useColorScheme() === 'dark';
  const { filters, setFilters } = useMaterialStore();
  const [showFilters, setShowFilters] = useState(false);

  const handleTypeFilter = useCallback((type: string) => {
    setFilters({
      ...filters,
      material_type: filters.material_type === type ? undefined : type,
    });
  }, [filters, setFilters]);

  if (!showFilters) {
    return (
      <TouchableOpacity
        onPress={() => setShowFilters(true)}
        className={`flex-row items-center p-3 rounded-lg mb-3 ${
          isDark ? 'bg-dark-surface' : 'bg-light-surface'
        }`}
      >
        <IconSymbol name="line.3.horizontal.decrease" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
        <Text className={`ml-2 font-rubik ${
          isDark ? 'text-dark-text' : 'text-light-text'
        }`}>
          Filter Materials
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <View className="mb-3">
      <View className={`p-4 rounded-lg ${
        isDark ? 'bg-dark-surface' : 'bg-light-surface'
      }`}>
        <View className="flex-row items-center justify-between mb-4">
          <Text className={`text-lg font-rubik-medium ${
            isDark ? 'text-dark-text' : 'text-light-text'
          }`}>
            Filters
          </Text>
          <TouchableOpacity onPress={() => setShowFilters(false)}>
            <IconSymbol name="xmark" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </TouchableOpacity>
        </View>

        <Text className={`mb-2 font-rubik-medium ${
          isDark ? 'text-dark-text' : 'text-light-text'
        }`}>
          Material Type
        </Text>
        <View className="flex-row flex-wrap gap-2">
          {MATERIAL_TYPES.map((type) => (
            <TouchableOpacity
              key={type.value}
              onPress={() => handleTypeFilter(type.value)}
              className={`px-3 py-2 rounded-lg ${
                filters.material_type === type.value
                  ? 'bg-primary-500'
                  : isDark ? 'bg-dark-surface-2' : 'bg-light-surface-2'
              }`}
            >
              <Text className={`font-rubik ${
                filters.material_type === type.value
                  ? 'text-white'
                  : isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
} 