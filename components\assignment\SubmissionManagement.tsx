import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useAssignmentStore } from '@/stores/assignmentStore';
import SubmissionCard from './SubmissionCard';

interface Assignment {
  id: string;
  title: string;
  description: string;
  due_date: string;
  max_points: number;
  status: 'draft' | 'published' | 'closed';
}

interface Submission {
  id: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  attachment_urls?: string[];
  status: 'draft' | 'submitted' | 'graded' | 'returned';
  grade?: number;
  feedback?: string;
  gemini_feedback?: string;
  submitted_at: string;
  graded_at?: string;
  graded_by?: string;
  student_name?: string;
  student_email?: string;
}

const FILTER_OPTIONS = [
  { id: 'all', label: 'All', icon: 'list-outline' },
  { id: 'submitted', label: 'Submitted', icon: 'checkmark-circle-outline' },
  { id: 'graded', label: 'Graded', icon: 'school-outline' },
  { id: 'pending', label: 'Pending', icon: 'time-outline' },
];

const SORT_OPTIONS = [
  { id: 'submitted_desc', label: 'Latest Submissions' },
  { id: 'submitted_asc', label: 'Oldest Submissions' },
  { id: 'student_name_asc', label: 'Student Name A-Z' },
  { id: 'grade_desc', label: 'Highest Grade' },
  { id: 'grade_asc', label: 'Lowest Grade' },
];

interface SubmissionManagementProps {
  assignment: Assignment;
}

export default function SubmissionManagement({ assignment }: SubmissionManagementProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  
  const {
    submissions,
    loading,
    error,
    fetchSubmissions,
    gradeSubmission,
  } = useAssignmentStore();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('submitted_desc');
  const [selectedSubmissions, setSelectedSubmissions] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  useFocusEffect(
    useCallback(() => {
      fetchSubmissions(assignment.id);
    }, [assignment.id, fetchSubmissions])
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchSubmissions(assignment.id);
    setRefreshing(false);
  }, [assignment.id, fetchSubmissions]);

  // Filter and sort submissions
  const filteredAndSortedSubmissions = React.useMemo(() => {
    let filtered = submissions;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(submission =>
        submission.student_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        submission.student_email?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'pending') {
        filtered = filtered.filter(submission => submission.status === 'submitted');
      } else {
        filtered = filtered.filter(submission => submission.status === selectedFilter);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (selectedSort) {
        case 'submitted_asc':
          return new Date(a.submitted_at).getTime() - new Date(b.submitted_at).getTime();
        case 'submitted_desc':
          return new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime();
        case 'student_name_asc':
          return (a.student_name || '').localeCompare(b.student_name || '');
        case 'grade_desc':
          return (b.grade || 0) - (a.grade || 0);
        case 'grade_asc':
          return (a.grade || 0) - (b.grade || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [submissions, searchQuery, selectedFilter, selectedSort]);

  const handleSubmissionSelect = (submissionId: string) => {
    setSelectedSubmissions(prev => {
      const newSelection = prev.includes(submissionId)
        ? prev.filter(id => id !== submissionId)
        : [...prev, submissionId];
      
      setShowBulkActions(newSelection.length > 0);
      return newSelection;
    });
  };

  const handleBulkGrade = () => {
    if (selectedSubmissions.length === 0) return;

    Alert.prompt(
      'Bulk Grade',
      `Enter grade for ${selectedSubmissions.length} submission(s) (out of ${assignment.max_points}):`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Grade',
          onPress: async (grade) => {
            if (!grade || isNaN(Number(grade))) {
              Alert.alert('Invalid Grade', 'Please enter a valid number.');
              return;
            }

            const gradeValue = Number(grade);
            if (gradeValue < 0 || gradeValue > assignment.max_points) {
              Alert.alert('Invalid Grade', `Grade must be between 0 and ${assignment.max_points}.`);
              return;
            }

            try {
              for (const submissionId of selectedSubmissions) {
                await gradeSubmission(submissionId, gradeValue);
              }
              setSelectedSubmissions([]);
              setShowBulkActions(false);
              Alert.alert('Success', 'Submissions graded successfully!');
            } catch (error) {
              Alert.alert('Error', 'Failed to grade submissions');
            }
          },
        },
      ],
      'plain-text'
    );
  };

  const getSubmissionStats = () => {
    const total = submissions.length;
    const submitted = submissions.filter(s => s.status !== 'draft').length;
    const graded = submissions.filter(s => s.status === 'graded').length;
    const pending = submissions.filter(s => s.status === 'submitted').length;

    return { total, submitted, graded, pending };
  };

  const stats = getSubmissionStats();

  const renderFilterButton = (filter: typeof FILTER_OPTIONS[0]) => (
    <TouchableOpacity
      key={filter.id}
      onPress={() => setSelectedFilter(filter.id)}
      className={`flex-row items-center px-4 py-2 rounded-full mr-3 ${
        selectedFilter === filter.id
          ? 'bg-primary-500'
          : isDark
          ? 'bg-dark-card'
          : 'bg-light-card'
      }`}
    >
      <Ionicons
        name={filter.icon as any}
        size={16}
        color={selectedFilter === filter.id ? 'white' : isDark ? '#9CA3AF' : '#6B7280'}
      />
      <Text className={`ml-2 font-rubik-medium ${
        selectedFilter === filter.id
          ? 'text-white'
          : isDark ? 'text-dark-text' : 'text-light-text'
      }`}>
        {filter.label}
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View className="space-y-4 mb-4">
      {/* Assignment Info */}
      <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`text-lg font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {assignment.title}
        </Text>
        <Text className={`font-rubik mb-3 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          {assignment.description}
        </Text>
        
        {/* Stats */}
        <View className="flex-row justify-between">
          <View className="items-center">
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {stats.submitted}
            </Text>
            <Text className={`text-sm font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Submitted
            </Text>
          </View>
          
          <View className="items-center">
            <Text className={`text-2xl font-rubik-bold text-green-500`}>
              {stats.graded}
            </Text>
            <Text className={`text-sm font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Graded
            </Text>
          </View>
          
          <View className="items-center">
            <Text className={`text-2xl font-rubik-bold text-orange-500`}>
              {stats.pending}
            </Text>
            <Text className={`text-sm font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Pending
            </Text>
          </View>
          
          <View className="items-center">
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {stats.submitted > 0 ? Math.round((stats.graded / stats.submitted) * 100) : 0}%
            </Text>
            <Text className={`text-sm font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Complete
            </Text>
          </View>
        </View>
      </View>

      {/* Search Bar */}
      <View className={`flex-row items-center px-4 py-3 rounded-xl ${
        isDark ? 'bg-dark-card' : 'bg-light-card'
      }`}>
        <Ionicons name="search-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
        <TextInput
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search students..."
          placeholderTextColor={isDark ? '#666' : '#999'}
          className={`flex-1 ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Buttons */}
      <FlatList
        data={FILTER_OPTIONS}
        renderItem={({ item }) => renderFilterButton(item)}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      />

      {/* Sort and Actions */}
      <View className="flex-row items-center justify-between px-4">
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              'Sort By',
              'Choose sorting option',
              SORT_OPTIONS.map(option => ({
                text: option.label,
                onPress: () => setSelectedSort(option.id),
              }))
            );
          }}
          className={`flex-row items-center px-3 py-2 rounded-lg ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          }`}
        >
          <Ionicons name="funnel-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          <Text className={`ml-2 font-rubik text-sm ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Sort
          </Text>
        </TouchableOpacity>

        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          {filteredAndSortedSubmissions.length} submission{filteredAndSortedSubmissions.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderSubmission = ({ item, index }: { item: Submission; index: number }) => (
    <Animated.View entering={FadeInDown.delay(index * 100).duration(400)}>
      <SubmissionCard
        submission={item}
        assignment={assignment}
        isSelected={selectedSubmissions.includes(item.id)}
        onSelect={() => handleSubmissionSelect(item.id)}
        onPress={() => {
          router.push(`/reports/assignment/${assignment.id}/submission/${item.id}`);
        }}
        showSelection={showBulkActions}
      />
    </Animated.View>
  );

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
        </TouchableOpacity>
        
        <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Submissions
        </Text>
        
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              'Export Options',
              'Choose export format',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Export CSV', onPress: () => Alert.alert('Export', 'CSV export feature coming soon!') },
                { text: 'Export PDF', onPress: () => Alert.alert('Export', 'PDF export feature coming soon!') },
              ]
            );
          }}
          className="p-2"
        >
          <Ionicons name="download-outline" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
        </TouchableOpacity>
      </View>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <Animated.View
          entering={FadeInDown.duration(300)}
          className={`flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          }`}
        >
          <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {selectedSubmissions.length} selected
          </Text>
          
          <View className="flex-row space-x-2">
            <TouchableOpacity
              onPress={handleBulkGrade}
              className="bg-primary-500 px-4 py-2 rounded-lg"
            >
              <Text className="text-white font-rubik-medium">Bulk Grade</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={() => {
                setSelectedSubmissions([]);
                setShowBulkActions(false);
              }}
              className="bg-gray-500 px-4 py-2 rounded-lg"
            >
              <Text className="text-white font-rubik-medium">Cancel</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Submissions List */}
      <FlatList
        data={filteredAndSortedSubmissions}
        renderItem={renderSubmission}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={{ padding: 16, paddingTop: 0 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#2196F3']}
            tintColor="#2196F3"
          />
        }
        ListEmptyComponent={
          <View className="items-center py-12">
            <Ionicons
              name="document-text-outline"
              size={64}
              color={isDark ? '#4B5563' : '#9CA3AF'}
            />
            <Text className={`text-lg font-rubik-medium mt-4 ${
              isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
            }`}>
              No submissions found
            </Text>
            <Text className={`text-center mt-2 ${
              isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
            }`}>
              {searchQuery || selectedFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Students haven\'t submitted their assignments yet'
              }
            </Text>
          </View>
        }
      />
    </View>
  );
}
