import { IconSymbol } from "@/components/ui/IconSymbol";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { useAssignmentStore } from "@/stores/assignmentStore";
import { useEnrollmentStore } from "@/stores/enrollmentStore";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
    ActivityIndicator,
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from "react-native";
import Animated, { FadeInDown } from "react-native-reanimated";
import { SafeAreaView } from "react-native-safe-area-context";

export default function CreateAssignment() {
  const colorScheme = useColorScheme() ?? "light";
  const isDark = colorScheme === "dark";
  const router = useRouter();
  const { createAssignment, generateAssignmentWithAI, loading, error } =
    useAssignmentStore();
  const { availableClasses, loadAvailableClasses, loadTeacherData } = useEnrollmentStore();
  const { clerkUser, supabaseUser } = useSupabaseAuth();

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dueDate, setDueDate] = useState(new Date());
  const [maxPoints, setMaxPoints] = useState("100");
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedClass, setSelectedClass] = useState<string>("");
  const [loadingTeacher, setLoadingTeacher] = useState(true);
  const [numberOfQuestions, setNumberOfQuestions] = useState("5");
  const [difficulty, setDifficulty] = useState<"easy" | "medium" | "hard">("medium");

  // Load teacher data and available classes
  useEffect(() => {
    async function loadTeacherAndClasses() {
      if (!supabaseUser?.clerk_user_id) return;

      try {
        setLoadingTeacher(true);
        // First load teacher data, then load classes
        await loadTeacherData(supabaseUser.clerk_user_id);
        const { currentTeacher } = useEnrollmentStore.getState();
        if (currentTeacher) {
          await loadAvailableClasses(currentTeacher.id);
        }
      } catch (err) {
        console.error('Error loading teacher data:', err);
        Alert.alert('Error', err instanceof Error ? err.message : 'Failed to load teacher data');
      } finally {
        setLoadingTeacher(false);
      }
    }

    loadTeacherAndClasses();
  }, [supabaseUser?.clerk_user_id, loadAvailableClasses, loadTeacherData]);

  // Auto-select first class if only one available
  useEffect(() => {
    if (availableClasses.length === 1 && !selectedClass) {
      setSelectedClass(availableClasses[0].id);
    }
  }, [availableClasses, selectedClass]);

  // Form validation
  const [errors, setErrors] = useState({
    title: "",
    description: "",
    maxPoints: "",
    dueDate: "",
  });

  const validateForm = () => {
    const newErrors = {
      title: "",
      description: "",
      maxPoints: "",
      dueDate: "",
    };

    if (!title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!description.trim()) {
      newErrors.description = "Description is required";
    }

    const points = parseInt(maxPoints);
    if (!maxPoints || isNaN(points) || points <= 0) {
      newErrors.maxPoints = "Valid points required";
    }

    if (dueDate <= new Date()) {
      newErrors.dueDate = "Due date must be in the future";
    }

    setErrors(newErrors);
    return Object.values(newErrors).every((error) => !error);
  };

  const handleSubmit = async () => {
    if (!selectedClass) {
      Alert.alert("Error", "Please select a class");
      return;
    }

    if (!title.trim()) {
      Alert.alert("Error", "Please enter a title");
      return;
    }

    if (!user?.id) {
      Alert.alert("Error", "Not authenticated");
      return;
    }

    if (!supabaseUser?.id) {
      Alert.alert("Error", "Supabase user not found");
      return;
    }

    try {
      await createAssignment({
        title,
        description,
        instructions: "",
          due_date: dueDate.toISOString(),
          max_points: parseInt(maxPoints),
        class_id: selectedClass,
        status: "draft",
        clerk_user_id: clerkUser.id,
        teacher_id: supabaseUser.id
      });

      Alert.alert("Success", "Assignment saved as draft successfully!");
      router.back();
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      Alert.alert("Error", "Failed to create assignment. Please try again.");
    }
  };

  const handleGenerateWithAI = async () => {
    if (!selectedClass) {
      Alert.alert("Error", "No class selected. Please select a class first.");
      return;
    }

    if (!title.trim() || !description.trim()) {
      Alert.alert(
        "Missing Information", 
        "Please enter both a title and description for the assignment first."
      );
      return;
    }

    if (!clerkUser?.id) {
      Alert.alert("Error", "Not authenticated");
      return;
    }

    if (!supabaseUser?.id) {
      Alert.alert("Error", "Supabase user not found");
      return;
    }

    try {
      console.log("Starting AI generation...");
      // Combine title and description as the prompt
      const prompt = `Generate an educational assignment with the following details:
Title: ${title}
Description: ${description}

Please create a structured assignment that includes clear instructions and questions based on this topic.`;
      
      const result = await generateAssignmentWithAI(prompt, selectedClass, parseInt(numberOfQuestions), difficulty, supabaseUser.id);
      console.log("Generation result:", result);
      
      if (result) {
        // Keep the original title and description
        setMaxPoints(result.max_points?.toString() || "100");
        Alert.alert(
          "Success",
          "Assignment questions generated with AI! You can now review and modify them."
        );
      } else {
        throw new Error("No result returned from AI generation");
      }
    } catch (err) {
      console.error("Error in handleGenerateWithAI:", err);
      Alert.alert(
        "Error Generating Assignment",
        err instanceof Error 
          ? `${err.message}\n\nPlease check your internet connection and try again.`
          : "Failed to generate assignment. Please try again."
      );
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };

  return (
    <SafeAreaView className="flex-1" edges={["top"]}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
    <ScrollView
          className={`flex-1 ${
        isDark ? "bg-dark-background" : "bg-light-background"
          }`}
          contentContainerStyle={{ padding: 24, paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="space-y-6">
            {/* Class Selection */}
            <View className="mb-6">
              <Text className={`font-rubik-medium mb-2 ${isDark ? "text-dark-text" : "text-light-text"}`}>
                Class
              </Text>
              <TouchableOpacity
                onPress={() => {
                  if (loadingTeacher) return;
                  if (availableClasses.length === 0) {
                    Alert.alert("No Classes", "You don't have any classes assigned.");
                    return;
                  }
                  Alert.alert(
                    "Select Class",
                    "Choose a class for this assignment",
                    [
                      { text: "Cancel", style: "cancel" },
                      ...availableClasses.map((cls) => ({
                        text: `${cls.name} ${cls.grade ? `(Grade ${cls.grade}${cls.section ? ` - ${cls.section}` : ""})` : ""}`,
                        onPress: () => setSelectedClass(cls.id),
                      })),
                    ]
                  );
                }}
                className={`p-4 rounded-lg border ${
                  isDark
                    ? "bg-dark-surface border-dark-border"
                    : "bg-light-surface border-light-border"
                }`}
              >
                <View className="flex-row items-center justify-between">
                  <View>
                    {loadingTeacher ? (
                      <Text className={`font-rubik ${isDark ? "text-dark-textSecondary" : "text-light-textSecondary"}`}>
                        Loading classes...
                      </Text>
                    ) : selectedClass ? (
                      <>
                        <Text className={`font-rubik-semibold ${isDark ? "text-dark-text" : "text-light-text"}`}>
                          {availableClasses.find((c) => c.id === selectedClass)?.name}
                        </Text>
                        <Text className={`font-rubik text-sm ${isDark ? "text-dark-textSecondary" : "text-light-textSecondary"}`}>
                          {availableClasses.find((c) => c.id === selectedClass)?.grade
                            ? `Grade ${availableClasses.find((c) => c.id === selectedClass)?.grade}`
                            : ""}{" "}
                          {availableClasses.find((c) => c.id === selectedClass)?.section
                            ? `Section ${availableClasses.find((c) => c.id === selectedClass)?.section}`
                            : ""}
                        </Text>
                      </>
                    ) : (
                      <Text className={`font-rubik ${isDark ? "text-dark-textSecondary" : "text-light-textSecondary"}`}>
                        Select a class
                </Text>
                    )}
                  </View>
                  <IconSymbol
                    name={loadingTeacher ? "arrow.clockwise" : "chevron.right"}
                    size={20}
                    color={isDark ? "#9CA3AF" : "#6B7280"}
                  />
                </View>
              </TouchableOpacity>
            </View>

            {/* Title */}
            <Animated.View 
              entering={FadeInDown.delay(100).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                Assignment Title
          </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${
                isDark ? "bg-dark-card" : "bg-light-card"
              }`}>
          <TextInput
            value={title}
                  onChangeText={setTitle}
            placeholder="Enter assignment title"
            placeholderTextColor={isDark ? "#666" : "#999"}
                  className={`px-4 py-3.5 font-rubik ${
                    isDark ? "text-dark-text" : "text-light-text"
                  } ${errors.title ? "border-2 border-red-500" : ""}`}
                />
              </View>
          {errors.title ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.title}</Text>
          ) : null}
            </Animated.View>

            {/* Description */}
            <Animated.View 
              entering={FadeInDown.delay(200).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                Description
          </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${
                isDark ? "bg-dark-card" : "bg-light-card"
              }`}>
          <TextInput
            value={description}
                  onChangeText={setDescription}
            placeholder="Enter assignment description"
            placeholderTextColor={isDark ? "#666" : "#999"}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
                  className={`px-4 py-3.5 min-h-[120px] font-rubik ${
              isDark ? "text-dark-text" : "text-light-text"
                  } ${errors.description ? "border-2 border-red-500" : ""}`}
          />
        </View>
              {errors.description ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.description}</Text>
              ) : null}
            </Animated.View>

            {/* AI Generation Settings */}
            <Animated.View 
              entering={FadeInDown.delay(300).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"} mb-4`}>
                AI Generation Settings
              </Text>
              
              {/* Number of Questions */}
              <View className="space-y-2">
                <Text className={`font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                  Number of Questions
                </Text>
                <View className={`rounded-xl overflow-hidden shadow-sm ${
                  isDark ? "bg-dark-card" : "bg-light-card"
                }`}>
              <TextInput
                    value={numberOfQuestions}
                    onChangeText={setNumberOfQuestions}
                    placeholder="Enter number of questions"
                placeholderTextColor={isDark ? "#666" : "#999"}
                keyboardType="numeric"
                    className={`px-4 py-3.5 font-rubik ${
                      isDark ? "text-dark-text" : "text-light-text"
                    }`}
                  />
                </View>
              </View>

              {/* Difficulty */}
              <View className="space-y-2">
                <Text className={`font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                  Difficulty Level
                </Text>
                <View className={`flex-row space-x-2 ${
                  isDark ? "bg-dark-card" : "bg-light-card"
                } rounded-xl p-2`}>
                  {(['easy', 'medium', 'hard'] as const).map((level) => (
                    <TouchableOpacity
                      key={level}
                      onPress={() => setDifficulty(level)}
                      className={`flex-1 py-2 rounded-lg items-center ${
                        difficulty === level
                          ? 'bg-primary-500'
                          : isDark
                          ? 'bg-dark-surface'
                          : 'bg-light-surface'
                      }`}
                    >
                      <Text className={`font-rubik-medium ${
                        difficulty === level
                          ? 'text-white'
                          : isDark
                          ? 'text-dark-text'
                          : 'text-light-text'
                      }`}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
            </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <TouchableOpacity
                onPress={handleGenerateWithAI}
                disabled={loading}
                className={`bg-primary-500 px-6 py-3 rounded-xl ${loading ? 'opacity-70' : ''} flex-row items-center justify-center space-x-2 mt-2`}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
                <Text className="text-white font-rubik-medium">
                  {loading ? "Generating with AI..." : "Generate Questions with AI"}
                </Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Due Date */}
            <Animated.View 
              entering={FadeInDown.delay(500).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"} mt-4`}>
                Due Date
              </Text>
              <TouchableOpacity
                onPress={() => setShowDatePicker(true)}
                className={`flex-row items-center justify-between px-4 py-3.5 rounded-xl shadow-sm ${
                  isDark ? "bg-dark-card" : "bg-light-card"
                } ${errors.dueDate ? "border-2 border-red-500" : ""}`}
              >
                <Text className={`font-rubik ${isDark ? "text-dark-text" : "text-light-text"}`}>
                  {dueDate.toLocaleDateString()}
                </Text>
              <Ionicons
                name="calendar-outline"
                size={20}
                  color={isDark ? "#ffffff80" : "#00000080"}
            />
          </TouchableOpacity>
          {errors.dueDate ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.dueDate}</Text>
          ) : null}
          {showDatePicker && (
            <DateTimePicker
              value={dueDate}
              mode="date"
                  display="default"
                  onChange={handleDateChange}
              minimumDate={new Date()}
                />
              )}
            </Animated.View>

            {/* Max Points */}
            <Animated.View 
              entering={FadeInDown.delay(600).duration(400)}
              className="space-y-3"
            >
              <Text className={`text-base font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                Maximum Points
            </Text>
              <View className={`rounded-xl overflow-hidden shadow-sm ${
                isDark ? "bg-dark-card" : "bg-light-card"
              }`}>
                <TextInput
                  value={maxPoints}
                  onChangeText={setMaxPoints}
                  placeholder="Enter maximum points"
                  placeholderTextColor={isDark ? "#666" : "#999"}
                  keyboardType="numeric"
                  className={`px-4 py-3.5 font-rubik ${
                    isDark ? "text-dark-text" : "text-light-text"
                  } ${errors.maxPoints ? "border-2 border-red-500" : ""}`}
                />
          </View>
              {errors.maxPoints ? (
                <Text className="text-red-500 text-sm font-rubik-medium">{errors.maxPoints}</Text>
              ) : null}
            </Animated.View>

            {/* Action Buttons */}
            <Animated.View 
              entering={FadeInDown.delay(700).duration(400)}
              className="flex-row justify-end space-x-4 mt-6"
            >
              <TouchableOpacity
                onPress={() => router.back()}
                className={`px-6 py-3 rounded-xl ${
                  isDark ? "bg-dark-card" : "bg-light-card"
                }`}
              >
                <Text className={`font-rubik-medium ${isDark ? "text-dark-text" : "text-light-text"}`}>
                Cancel
              </Text>
            </TouchableOpacity>
              
            <TouchableOpacity
                onPress={handleSubmit}
                disabled={loading}
                className="bg-primary-500 px-6 py-3 rounded-xl"
              >
                <Text className="text-white font-rubik-medium">
                  {loading ? "Creating..." : "Save as Draft"}
                  </Text>
            </TouchableOpacity>
            </Animated.View>
      </View>
    </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
