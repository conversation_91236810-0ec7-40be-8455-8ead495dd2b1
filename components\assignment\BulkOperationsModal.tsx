import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAssignmentStore } from '@/stores/assignmentStore';

interface BulkOperationsModalProps {
  visible: boolean;
  onClose: () => void;
  selectedAssignments: string[];
  assignmentTitles: Record<string, string>;
  onOperationComplete: () => void;
}

type BulkOperation = 'publish' | 'close' | 'delete' | 'export';

export default function BulkOperationsModal({
  visible,
  onClose,
  selectedAssignments,
  assignmentTitles,
  onOperationComplete,
}: BulkOperationsModalProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [selectedOperation, setSelectedOperation] = useState<BulkOperation | null>(null);
  const [processing, setProcessing] = useState(false);

  const {
    bulkPublishAssignments,
    bulkCloseAssignments,
    bulkDeleteAssignments,
    exportMultipleAssignments,
    loading,
  } = useAssignmentStore();

  const operations = [
    {
      id: 'publish' as const,
      title: 'Publish Assignments',
      description: 'Make selected assignments available to students',
      icon: 'paperplane.fill' as const,
      color: '#10B981',
      bgColor: isDark ? '#065F46' : '#D1FAE5',
    },
    {
      id: 'close' as const,
      title: 'Close Assignments',
      description: 'Stop accepting new submissions for selected assignments',
      icon: 'lock.fill' as const,
      color: '#F59E0B',
      bgColor: isDark ? '#92400E' : '#FEF3C7',
    },
    {
      id: 'export' as const,
      title: 'Export Data',
      description: 'Download assignment data and submissions',
      icon: 'square.and.arrow.up' as const,
      color: '#3B82F6',
      bgColor: isDark ? '#1E40AF' : '#DBEAFE',
    },
    {
      id: 'delete' as const,
      title: 'Delete Assignments',
      description: 'Permanently remove selected assignments and all data',
      icon: 'trash.fill' as const,
      color: '#EF4444',
      bgColor: isDark ? '#991B1B' : '#FEE2E2',
    },
  ];

  const handleOperation = async () => {
    if (!selectedOperation) return;

    try {
      setProcessing(true);

      switch (selectedOperation) {
        case 'publish':
          await bulkPublishAssignments(selectedAssignments);
          Alert.alert(
            'Success',
            `${selectedAssignments.length} assignment(s) have been published.`,
            [{ text: 'OK', onPress: () => { onOperationComplete(); onClose(); } }]
          );
          break;

        case 'close':
          await bulkCloseAssignments(selectedAssignments);
          Alert.alert(
            'Success',
            `${selectedAssignments.length} assignment(s) have been closed.`,
            [{ text: 'OK', onPress: () => { onOperationComplete(); onClose(); } }]
          );
          break;

        case 'delete':
          Alert.alert(
            'Confirm Deletion',
            `Are you sure you want to delete ${selectedAssignments.length} assignment(s)? This action cannot be undone.`,
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: async () => {
                  try {
                    await bulkDeleteAssignments(selectedAssignments);
                    Alert.alert(
                      'Success',
                      `${selectedAssignments.length} assignment(s) have been deleted.`,
                      [{ text: 'OK', onPress: () => { onOperationComplete(); onClose(); } }]
                    );
                  } catch (error) {
                    Alert.alert('Error', 'Failed to delete assignments. Please try again.');
                  }
                },
              },
            ]
          );
          return; // Don't set processing to false here since we're showing another alert

        case 'export':
          await exportMultipleAssignments(selectedAssignments, 'csv');
          Alert.alert(
            'Export Complete',
            'Assignment data has been exported successfully.',
            [{ text: 'OK', onPress: onClose }]
          );
          break;
      }
    } catch (error) {
      console.error('Bulk operation error:', error);
      Alert.alert(
        'Error',
        'Failed to complete the operation. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setProcessing(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`px-6 py-4 border-b ${isDark ? 'border-dark-border' : 'border-gray-200'}`}>
          <View className="flex-row items-center justify-between">
            <View className="flex-1">
              <Text className={`text-xl font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                Bulk Operations
              </Text>
              <Text className={`text-sm font-rubik mt-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                {selectedAssignments.length} assignment(s) selected
              </Text>
            </View>
            <TouchableOpacity
              onPress={onClose}
              className={`w-8 h-8 rounded-full items-center justify-center ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
            >
              <IconSymbol name="xmark" size={16} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Selected Assignments */}
          <View className="mb-6">
            <Text className={`text-lg font-rubik-bold mb-3 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Selected Assignments
            </Text>
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-gray-50'}`}>
              {selectedAssignments.slice(0, 3).map((id, index) => (
                <Text
                  key={id}
                  className={`font-rubik text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'} ${
                    index > 0 ? 'mt-1' : ''
                  }`}
                >
                  • {assignmentTitles[id] || `Assignment ${id.slice(0, 8)}`}
                </Text>
              ))}
              {selectedAssignments.length > 3 && (
                <Text className={`font-rubik text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                  ... and {selectedAssignments.length - 3} more
                </Text>
              )}
            </View>
          </View>

          {/* Operations */}
          <View className="mb-6">
            <Text className={`text-lg font-rubik-bold mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Choose Operation
            </Text>

            {operations.map((operation) => (
              <TouchableOpacity
                key={operation.id}
                onPress={() => setSelectedOperation(operation.id)}
                className={`p-4 rounded-xl mb-3 border-2 ${
                  selectedOperation === operation.id
                    ? 'border-primary-500'
                    : isDark
                    ? 'border-gray-700'
                    : 'border-gray-200'
                }`}
                style={{
                  backgroundColor: selectedOperation === operation.id ? operation.bgColor : undefined,
                }}
              >
                <View className="flex-row items-center">
                  <View
                    className="w-12 h-12 rounded-lg items-center justify-center mr-4"
                    style={{ backgroundColor: operation.bgColor }}
                  >
                    <IconSymbol
                      name={operation.icon}
                      size={24}
                      color={operation.color}
                    />
                  </View>
                  <View className="flex-1">
                    <Text className={`font-rubik-bold text-base ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      {operation.title}
                    </Text>
                    <Text className={`font-rubik text-sm mt-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                      {operation.description}
                    </Text>
                  </View>
                  {selectedOperation === operation.id && (
                    <IconSymbol
                      name="checkmark.circle.fill"
                      size={24}
                      color="#3B82F6"
                    />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        {/* Footer */}
        <View className={`px-6 py-4 border-t ${isDark ? 'border-dark-border' : 'border-gray-200'}`}>
          <View className="flex-row space-x-3">
            <TouchableOpacity
              onPress={onClose}
              className={`flex-1 py-3 rounded-lg items-center justify-center ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
              disabled={processing || loading}
            >
              <Text className={`font-rubik-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleOperation}
              className={`flex-1 py-3 rounded-lg items-center justify-center ${
                !selectedOperation || processing || loading ? 'bg-gray-400' : 'bg-primary-500'
              }`}
              disabled={!selectedOperation || processing || loading}
            >
              {processing || loading ? (
                <View className="flex-row items-center">
                  <ActivityIndicator size="small" color="#FFFFFF" />
                  <Text className="text-white font-rubik-medium ml-2">Processing...</Text>
                </View>
              ) : (
                <Text className="text-white font-rubik-medium">
                  {selectedOperation ? operations.find(op => op.id === selectedOperation)?.title : 'Select Operation'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
