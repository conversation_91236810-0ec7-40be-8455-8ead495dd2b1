import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useEnrollmentStore, type EnrollmentRequest } from '@/stores/enrollmentStore';
import React from 'react';
import { Alert, FlatList, RefreshControl, Text, TouchableOpacity, View } from 'react-native';

interface EnrollmentRequestListProps {
  onRequestPress?: (request: EnrollmentRequest) => void;
}

export const EnrollmentRequestList: React.FC<EnrollmentRequestListProps> = ({
  onRequestPress
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const {
    enrollmentRequests,
    approveEnrollmentRequest,
    rejectEnrollmentRequest,
    refreshData,
    refreshing,
    isLoading
  } = useEnrollmentStore();

  const getStatusColor = (status: EnrollmentRequest['status']) => {
    switch (status) {
      case 'pending':
        return isDark ? '#F59E0B' : '#D97706';
      case 'approved':
        return isDark ? '#10B981' : '#059669';
      case 'rejected':
        return isDark ? '#EF4444' : '#DC2626';
      case 'completed':
        return isDark ? '#6366F1' : '#4F46E5';
      default:
        return isDark ? '#9CA3AF' : '#6B7280';
    }
  };

  const getStatusIcon = (status: EnrollmentRequest['status']) => {
    switch (status) {
      case 'pending':
        return 'clock.fill';
      case 'approved':
        return 'checkmark.circle.fill';
      case 'rejected':
        return 'xmark.circle.fill';
      case 'completed':
        return 'checkmark.seal.fill';
      default:
        return 'questionmark.circle.fill';
    }
  };

  const handleApprove = (request: EnrollmentRequest) => {
    Alert.alert(
      'Approve Enrollment',
      `Are you sure you want to approve enrollment for ${request.student_name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Approve',
          style: 'default',
          onPress: () => approveEnrollmentRequest(request.id)
        }
      ]
    );
  };

  const handleReject = (request: EnrollmentRequest) => {
    Alert.prompt(
      'Reject Enrollment',
      `Please provide a reason for rejecting ${request.student_name}'s enrollment:`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: (reason) => rejectEnrollmentRequest(request.id, reason)
        }
      ],
      'plain-text',
      '',
      'default'
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderEnrollmentRequest = ({ item }: { item: EnrollmentRequest }) => (
    <TouchableOpacity
      onPress={() => onRequestPress?.(item)}
      className={`p-4 rounded-lg mb-3 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
      style={{
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}
    >
      {/* Header */}
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className={`font-rubik-semibold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {item.student_name}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            {item.student_email}
          </Text>
        </View>
        <View className="flex-row items-center">
          <IconSymbol
            name={getStatusIcon(item.status)}
            size={20}
            color={getStatusColor(item.status)}
          />
          <Text
            className="ml-2 font-rubik-medium text-sm capitalize"
            style={{ color: getStatusColor(item.status) }}
          >
            {item.status}
          </Text>
        </View>
      </View>

      {/* Student Details */}
      <View className="mb-3">
        <View className="flex-row mb-1">
          <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            Grade:
          </Text>
          <Text className={`font-rubik ml-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {item.student_grade || 'Not specified'}
          </Text>
        </View>
        {item.student_section && (
          <View className="flex-row mb-1">
            <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Section:
            </Text>
            <Text className={`font-rubik ml-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {item.student_section}
            </Text>
          </View>
        )}
        {item.roll_number && (
          <View className="flex-row mb-1">
            <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Roll Number:
            </Text>
            <Text className={`font-rubik ml-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {item.roll_number}
            </Text>
          </View>
        )}
      </View>

      {/* Parent Information */}
      {(item.parent_name || item.parent_email || item.parent_phone) && (
        <View className="mb-3">
          <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Parent Information:
          </Text>
          {item.parent_name && (
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Name: {item.parent_name}
            </Text>
          )}
          {item.parent_email && (
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Email: {item.parent_email}
            </Text>
          )}
          {item.parent_phone && (
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Phone: {item.parent_phone}
            </Text>
          )}
        </View>
      )}

      {/* Enrollment Code */}
      <View className="mb-3">
        <Text className={`font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
          Enrollment Code:
        </Text>
        <Text className={`font-rubik-bold ${isDark ? 'text-primary-400' : 'text-primary-600'}`}>
          {item.enrollment_code}
        </Text>
      </View>

      {/* Notes */}
      {item.notes && (
        <View className="mb-3">
          <Text className={`font-rubik-medium mb-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Notes:
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            {item.notes}
          </Text>
        </View>
      )}

      {/* Footer */}
      <View className="flex-row items-center justify-between">
        <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
          {formatDate(item.created_at)}
        </Text>

        {/* Action Buttons */}
        {item.status === 'pending' && (
          <View className="flex-row">
            <TouchableOpacity
              onPress={() => handleReject(item)}
              disabled={isLoading}
              className="mr-3 px-3 py-1 rounded bg-error"
            >
              <Text className="text-white font-rubik-medium text-sm">Reject</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => handleApprove(item)}
              disabled={isLoading}
              className="px-3 py-1 rounded bg-success"
            >
              <Text className="text-white font-rubik-medium text-sm">Approve</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center py-12">
      <IconSymbol
        name="person.badge.plus"
        size={48}
        color={isDark ? '#9CA3AF' : '#6B7280'}
      />
      <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        No enrollment requests yet
      </Text>
      <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        Enrollment requests will appear here
      </Text>
    </View>
  );

  return (
    <View className="flex-1">
      <FlatList
        data={enrollmentRequests}
        renderItem={renderEnrollmentRequest}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refreshData}
            tintColor={isDark ? '#60A5FA' : '#2563EB'}
          />
        }
        contentContainerStyle={{
          flexGrow: 1,
          padding: 16
        }}
      />
    </View>
  );
};
