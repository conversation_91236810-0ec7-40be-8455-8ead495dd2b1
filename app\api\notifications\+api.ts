import { supabase } from '@/lib/supabase';

// GET - Fetch notifications for the current user
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const filter = url.searchParams.get('filter') || 'all'; // all, unread, high_priority

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user record
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Build query
    let query = supabase
      .from('assignment_notifications')
      .select('*')
      .eq('user_id', userData.id)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply filters
    if (filter === 'unread') {
      query = query.eq('is_read', false);
    } else if (filter === 'high_priority') {
      query = query.eq('priority', 'high');
    }

    const { data: notifications, error } = await query;

    if (error) {
      return Response.json({ error: 'Failed to fetch notifications' }, { status: 500 });
    }

    // Get unread count
    const { data: unreadCountData, error: countError } = await supabase
      .rpc('get_unread_notification_count', { user_uuid: userData.id });

    const unreadCount = countError ? 0 : unreadCountData;

    return Response.json({
      notifications: notifications || [],
      unreadCount,
      total: notifications?.length || 0,
    });

  } catch (error) {
    console.error('Error fetching notifications:', error);
    return Response.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

// POST - Create a new notification
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      type,
      title,
      message,
      assignmentId,
      assignmentTitle,
      userId,
      studentId,
      studentName,
      priority = 'medium',
      actionRequired = false,
      scheduledFor,
    } = body;

    // Validate required fields
    if (!type || !title || !message || !assignmentId || !assignmentTitle || !userId) {
      return Response.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get tenant_id from user
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Create notification
    const notificationData = {
      type,
      title,
      message,
      assignment_id: assignmentId,
      assignment_title: assignmentTitle,
      user_id: userId,
      student_id: studentId,
      student_name: studentName,
      priority,
      action_required: actionRequired,
      scheduled_for: scheduledFor,
      tenant_id: userData.tenant_id,
      is_read: false,
    };

    const { data: notification, error } = await supabase
      .from('assignment_notifications')
      .insert([notificationData])
      .select()
      .single();

    if (error) {
      return Response.json({ error: 'Failed to create notification' }, { status: 500 });
    }

    return Response.json(notification, { status: 201 });

  } catch (error) {
    console.error('Error creating notification:', error);
    return Response.json(
      { error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}

// PATCH - Update notification (mark as read, etc.)
export async function PATCH(request: Request) {
  try {
    const body = await request.json();
    const { notificationId, isRead, action } = body;

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user record
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    if (action === 'mark_all_read') {
      // Mark all notifications as read
      const { error } = await supabase
        .rpc('mark_all_notifications_read', { user_uuid: userData.id });

      if (error) {
        return Response.json({ error: 'Failed to mark all notifications as read' }, { status: 500 });
      }

      return Response.json({ success: true });
    }

    if (notificationId) {
      // Update specific notification
      const updateData: any = {};
      if (typeof isRead === 'boolean') {
        updateData.is_read = isRead;
      }

      const { data: notification, error } = await supabase
        .from('assignment_notifications')
        .update(updateData)
        .eq('id', notificationId)
        .eq('user_id', userData.id)
        .select()
        .single();

      if (error) {
        return Response.json({ error: 'Failed to update notification' }, { status: 500 });
      }

      return Response.json(notification);
    }

    return Response.json({ error: 'Invalid request' }, { status: 400 });

  } catch (error) {
    console.error('Error updating notification:', error);
    return Response.json(
      { error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}

// DELETE - Delete notification
export async function DELETE(request: Request) {
  try {
    const url = new URL(request.url);
    const notificationId = url.searchParams.get('id');

    if (!notificationId) {
      return Response.json({ error: 'Notification ID required' }, { status: 400 });
    }

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user record
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Delete notification
    const { error } = await supabase
      .from('assignment_notifications')
      .delete()
      .eq('id', notificationId)
      .eq('user_id', userData.id);

    if (error) {
      return Response.json({ error: 'Failed to delete notification' }, { status: 500 });
    }

    return Response.json({ success: true });

  } catch (error) {
    console.error('Error deleting notification:', error);
    return Response.json(
      { error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}
