import { useColorScheme } from '@/hooks/useColorScheme';
import { useEnrollmentStore } from '@/stores/enrollmentStore';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    ScrollView,
    Text,
    View
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { AssignmentData } from '../AssignmentCreationWizard';

interface PreviewStepProps {
  data: AssignmentData;
  updateData: (updates: Partial<AssignmentData>) => void;
}

export default function PreviewStep({ data }: PreviewStepProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const { availableClasses } = useEnrollmentStore();

  const selectedClass = availableClasses.find(cls => cls.id === data.class_id);



  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTotalRubricPoints = () => {
    return data.rubrics.reduce((total, criteria) => total + criteria.max_points, 0);
  };

  return (
    <View className="flex-1 p-4">
      <ScrollView showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="space-y-6">
          {/* Header */}
          <View>
            <Text className={`text-xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Assignment Preview
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Review your assignment before publishing. You can make changes later if needed.
            </Text>
          </View>

          {/* Assignment Card */}
          <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            {/* Title and Status */}
            <View className="flex-row items-start justify-between mb-4">
              <View className="flex-1">
                <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {data.title}
                </Text>
                <Text className={`font-rubik mt-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  {selectedClass ? `${selectedClass.name} (${selectedClass.grade} - ${selectedClass.section})` : 'No class selected'}
                </Text>
              </View>
              
              <View className={`px-3 py-1 rounded-full ${
                data.status === 'published' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-yellow-100 dark:bg-yellow-900/30'
              }`}>
                <Text className={`font-rubik-medium text-sm ${
                  data.status === 'published' ? 'text-green-700 dark:text-green-300' : 'text-yellow-700 dark:text-yellow-300'
                }`}>
                  {data.status === 'published' ? 'Published' : 'Draft'}
                </Text>
              </View>
            </View>

            {/* Description */}
            <Text className={`font-rubik mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {data.description}
            </Text>

            {/* Assignment Details */}
            <View className="space-y-3">
              <View className="flex-row items-center">
                <Ionicons name="calendar-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
                <Text className={`ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Due: {formatDate(data.due_date)}
                </Text>
              </View>

              <View className="flex-row items-center">
                <Ionicons name="trophy-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
                <Text className={`ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Max Points: {data.max_points}
                </Text>
              </View>

              {data.attachment_urls.length > 0 && (
                <View className="flex-row items-center">
                  <Ionicons name="attach-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
                  <Text className={`ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {data.attachment_urls.length} attachment{data.attachment_urls.length > 1 ? 's' : ''}
                  </Text>
                </View>
              )}

              {data.gemini_generated && (
                <View className="flex-row items-center">
                  <Ionicons name="sparkles-outline" size={20} color="#8B5CF6" />
                  <Text className={`ml-3 font-rubik text-purple-600 dark:text-purple-400`}>
                    AI Enhanced
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Instructions */}
          {data.instructions && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Instructions
              </Text>
              <Text className={`font-rubik ${isDark ? 'text-gray-200' : 'text-gray-800'}`}>
                {data.instructions}
              </Text>
            </View>
          )}

          {/* Rubrics */}
          {data.rubrics.length > 0 && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <View className="flex-row items-center justify-between mb-3">
                <Text className={`font-rubik-bold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Grading Rubric
                </Text>
                <Text className={`font-rubik text-sm ${
                  getTotalRubricPoints() === data.max_points
                    ? 'text-green-500'
                    : 'text-orange-500'
                }`}>
                  {getTotalRubricPoints()}/{data.max_points} pts
                </Text>
              </View>
              
              {data.rubrics.map((criteria, index) => (
                <View key={index} className="flex-row items-start justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                  <View className="flex-1">
                    <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {criteria.criteria_name}
                    </Text>
                    <Text className={`font-rubik text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                      {criteria.description}
                    </Text>
                  </View>
                  <Text className={`font-rubik-bold ml-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {criteria.max_points} pts
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Settings */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <Text className={`font-rubik-bold text-base mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Assignment Settings
            </Text>
            
            <View className="space-y-2">
              <View className="flex-row items-center justify-between">
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Allow Late Submissions
                </Text>
                <Text className={`font-rubik-medium ${
                  data.allow_late_submissions ? 'text-green-500' : 'text-red-500'
                }`}>
                  {data.allow_late_submissions ? 'Yes' : 'No'}
                </Text>
              </View>
              
              <View className="flex-row items-center justify-between">
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Show Grades Immediately
                </Text>
                <Text className={`font-rubik-medium ${
                  data.show_grades_immediately ? 'text-green-500' : 'text-red-500'
                }`}>
                  {data.show_grades_immediately ? 'Yes' : 'No'}
                </Text>
              </View>
            </View>
          </View>

          {/* Validation Warnings */}
          {getTotalRubricPoints() !== data.max_points && data.rubrics.length > 0 && (
            <View className={`p-4 rounded-xl ${isDark ? 'bg-orange-900/20' : 'bg-orange-50'}`}>
              <View className="flex-row items-center mb-2">
                <Ionicons name="warning-outline" size={20} color="#F59E0B" />
                <Text className="ml-2 font-rubik-medium text-orange-600 dark:text-orange-400">
                  Points Mismatch
                </Text>
              </View>
              <Text className={`font-rubik text-sm ${isDark ? 'text-orange-200' : 'text-orange-700'}`}>
                Your rubric total ({getTotalRubricPoints()} pts) doesn't match the assignment max points ({data.max_points} pts).
              </Text>
            </View>
          )}
        </Animated.View>
      </ScrollView>


    </View>
  );
}
