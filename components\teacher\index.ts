// Teacher Dashboard Components
export { DashboardHeader } from './DashboardHeader';
export { QuickActions } from './QuickActions';
export { RecentActivity } from './RecentActivity';
export { StatsCard } from './StatsCard';
export { StatsOverview } from './StatsOverview';
export { TeacherDashboardCard } from './TeacherDashboardCard';

// Types
export interface TeacherStats {
  totalClasses: number;
  totalStudents: number;
  pendingAssignments: number;
  todayAttendance: number;
}


