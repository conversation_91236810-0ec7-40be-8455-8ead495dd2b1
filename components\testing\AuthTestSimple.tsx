import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { getCurrentUserAndTeacher } from '@/lib/authHelpers';

export default function AuthTestSimple() {
  const { isSignedIn, getToken } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testAuth = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('=== AUTH TEST START ===');
      console.log('Clerk isSignedIn:', isSignedIn);
      console.log('Clerk user:', user?.id, user?.emailAddresses?.[0]?.emailAddress);

      if (!isSignedIn || !user) {
        throw new Error('Not signed in with Clerk');
      }

      // Test getting token
      const token = await getToken({ template: 'supabase' });
      console.log('Clerk token obtained:', !!token);

      // Test our auth helper
      const authResult = await getCurrentUserAndTeacher();
      console.log('Auth helper result:', authResult);

      setResult(`✅ Success!
User: ${authResult.user.name} (${authResult.user.email})
Teacher ID: ${authResult.teacher.id}
Tenant: ${authResult.teacher.tenant_id}`);

    } catch (error) {
      console.error('Auth test error:', error);
      setResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ padding: 20, backgroundColor: 'white', margin: 20, borderRadius: 10 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
        Auth Test
      </Text>
      
      <TouchableOpacity
        onPress={testAuth}
        disabled={loading}
        style={{
          backgroundColor: loading ? '#ccc' : '#007AFF',
          padding: 15,
          borderRadius: 8,
          marginBottom: 15
        }}
      >
        <Text style={{ color: 'white', textAlign: 'center', fontWeight: 'bold' }}>
          {loading ? 'Testing...' : 'Test Authentication'}
        </Text>
      </TouchableOpacity>

      {result ? (
        <View style={{ 
          backgroundColor: result.startsWith('✅') ? '#d4edda' : '#f8d7da',
          padding: 10,
          borderRadius: 5,
          borderColor: result.startsWith('✅') ? '#c3e6cb' : '#f5c6cb',
          borderWidth: 1
        }}>
          <Text style={{ 
            color: result.startsWith('✅') ? '#155724' : '#721c24',
            fontSize: 12,
            fontFamily: 'monospace'
          }}>
            {result}
          </Text>
        </View>
      ) : null}
    </View>
  );
}
