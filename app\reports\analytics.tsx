import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Dimensions, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Stores
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

const { width: screenWidth } = Dimensions.get('window');

interface TrendData {
  date: string;
  attendance_rate: number;
  total_sessions: number;
  present_count: number;
  absent_count: number;
}

interface ClassComparison {
  class_id: string;
  class_name: string;
  attendance_rate: number;
  total_students: number;
  total_sessions: number;
}

const AnalyticsScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { userId: clerkUserId } = useAuth();

  const {
    sessions,
    isLoading,
    error,
    loadTeacherSessions,
    clearError
  } = useAttendanceStore();

  const { currentTeacher, classes, loadTeacherData, loadTeacherClasses } = useEnrollmentStore();

  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'semester'>('month');
  const [selectedMetric, setSelectedMetric] = useState<'attendance' | 'sessions' | 'trends'>('attendance');

  // Load teacher data
  useEffect(() => {
    if (clerkUserId && !currentTeacher) {
      loadTeacherData(clerkUserId);
    }
  }, [clerkUserId, currentTeacher, loadTeacherData]);

  useEffect(() => {
    if (currentTeacher && classes.length === 0) {
      loadTeacherClasses(currentTeacher.id);
    }
  }, [currentTeacher, classes.length, loadTeacherClasses]);

  useEffect(() => {
    if (currentTeacher) {
      loadTeacherSessions(currentTeacher.id);
    }
  }, [currentTeacher, loadTeacherSessions]);

  const getDateRange = (period: 'week' | 'month' | 'semester') => {
    const now = new Date();
    const start = new Date();

    switch (period) {
      case 'week':
        start.setDate(now.getDate() - 7);
        break;
      case 'month':
        start.setMonth(now.getMonth() - 1);
        break;
      case 'semester':
        start.setMonth(now.getMonth() - 6);
        break;
    }

    return { start, end: now };
  };

  const generateTrendData = (): TrendData[] => {
    const { start, end } = getDateRange(selectedPeriod);
    const filteredSessions = sessions.filter(session => {
      const sessionDate = new Date(session.session_date);
      return sessionDate >= start && sessionDate <= end;
    });

    // Group sessions by date
    const sessionsByDate = filteredSessions.reduce((acc, session) => {
      const date = session.session_date;
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(session);
      return acc;
    }, {} as Record<string, typeof sessions>);

    // Calculate daily statistics
    return Object.entries(sessionsByDate).map(([date, daySessions]) => {
      const totalRecords = daySessions.flatMap(session => session.attendance_records || []);
      const presentCount = totalRecords.filter(record => record.status === 'present').length;
      const absentCount = totalRecords.filter(record => record.status === 'absent').length;
      const attendanceRate = totalRecords.length > 0 ? (presentCount / totalRecords.length) * 100 : 0;

      return {
        date,
        attendance_rate: attendanceRate,
        total_sessions: daySessions.length,
        present_count: presentCount,
        absent_count: absentCount
      };
    }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const generateClassComparison = (): ClassComparison[] => {
    return classes.map(cls => {
      const classSessions = sessions.filter(session => session.class_id === cls.id);
      const totalRecords = classSessions.flatMap(session => session.attendance_records || []);
      const presentCount = totalRecords.filter(record => record.status === 'present').length;
      const attendanceRate = totalRecords.length > 0 ? (presentCount / totalRecords.length) * 100 : 0;

      // Get unique students count
      const uniqueStudents = new Set(totalRecords.map(record => record.student_id)).size;

      return {
        class_id: cls.id,
        class_name: cls.name,
        attendance_rate: attendanceRate,
        total_students: uniqueStudents,
        total_sessions: classSessions.length
      };
    }).sort((a, b) => b.attendance_rate - a.attendance_rate);
  };

  const getOverallStats = () => {
    const { start, end } = getDateRange(selectedPeriod);
    const filteredSessions = sessions.filter(session => {
      const sessionDate = new Date(session.session_date);
      return sessionDate >= start && sessionDate <= end;
    });

    const totalRecords = filteredSessions.flatMap(session => session.attendance_records || []);
    const presentCount = totalRecords.filter(record => record.status === 'present').length;
    const absentCount = totalRecords.filter(record => record.status === 'absent').length;
    const lateCount = totalRecords.filter(record => record.status === 'late').length;
    const excusedCount = totalRecords.filter(record => record.status === 'excused').length;

    const totalStudentSessions = totalRecords.length;
    const attendanceRate = totalStudentSessions > 0 ? (presentCount / totalStudentSessions) * 100 : 0;

    return {
      totalSessions: filteredSessions.length,
      totalStudentSessions,
      attendanceRate,
      presentCount,
      absentCount,
      lateCount,
      excusedCount
    };
  };

  const renderSimpleChart = (data: TrendData[]) => {
    if (data.length === 0) return null;

    const maxRate = Math.max(...data.map(d => d.attendance_rate));
    const chartWidth = screenWidth - 32;
    const chartHeight = 200;
    const barWidth = chartWidth / data.length - 8;

    return (
      <View className="p-4">
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Attendance Trend
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row items-end" style={{ height: chartHeight }}>
            {data.map((item, index) => {
              const barHeight = (item.attendance_rate / maxRate) * (chartHeight - 40);
              return (
                <View key={index} className="items-center mr-2" style={{ width: barWidth }}>
                  <Text className={`font-rubik-semibold text-xs mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {item.attendance_rate.toFixed(0)}%
                  </Text>
                  <View
                    className="bg-primary-500 rounded-t"
                    style={{ width: barWidth - 4, height: Math.max(barHeight, 4) }}
                  />
                  <Text className={`font-rubik text-xs mt-2 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    {new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </Text>
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderClassComparison = (data: ClassComparison[]) => {
    if (data.length === 0) return null;

    return (
      <View className="p-4">
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Class Performance
        </Text>
        <View className="space-y-3">
          {data.map((cls, index) => (
            <View
              key={cls.class_id}
              className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
            >
              <View className="flex-row items-center justify-between mb-2">
                <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {cls.class_name}
                </Text>
                <View className="flex-row items-center">
                  <Text className={`font-rubik-bold text-lg ${
                    cls.attendance_rate >= 90 ? 'text-success' :
                    cls.attendance_rate >= 75 ? 'text-warning' : 'text-error'
                  }`}>
                    {cls.attendance_rate.toFixed(1)}%
                  </Text>
                  {index === 0 && (
                    <IconSymbol name="star.fill" size={16} color="#FFD700" />
                  )}
                </View>
              </View>

              <View className="flex-row justify-between">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  {cls.total_students} students • {cls.total_sessions} sessions
                </Text>
                <View className="flex-row items-center">
                  <View
                    className="bg-primary-500 h-2 rounded"
                    style={{ width: (cls.attendance_rate / 100) * 100 }}
                  />
                  <View
                    className={`h-2 rounded-r ${isDark ? 'bg-dark-border' : 'bg-light-border'}`}
                    style={{ width: ((100 - cls.attendance_rate) / 100) * 100 }}
                  />
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen message="Loading analytics..." />;
  }

  // Show error screen
  if (error) {
    return (
      <ErrorScreen
        title="Analytics Error"
        message={error}
        onRetry={() => {
          clearError();
          if (currentTeacher) {
            loadTeacherSessions(currentTeacher.id);
          }
        }}
      />
    );
  }

  if (!currentTeacher) {
    return (
      <ErrorScreen
        title="Teacher Not Found"
        message="Unable to load teacher information."
        onRetry={() => router.back()}
      />
    );
  }

  const trendData = generateTrendData();
  const classComparison = generateClassComparison();
  const overallStats = getOverallStats();

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <View className="flex-row items-center justify-between mb-4">
            <TouchableOpacity onPress={() => router.back()}>
              <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
            <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Analytics
            </Text>
            <View className="w-6" />
          </View>

          {/* Period Selector */}
          <View className="flex-row gap-2 mb-4">
            {(['week', 'month', 'semester'] as const).map((period) => (
              <TouchableOpacity
                key={period}
                onPress={() => setSelectedPeriod(period)}
                className={`px-4 py-2 rounded-lg ${
                  selectedPeriod === period ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-medium capitalize ${
                  selectedPeriod === period ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  {period}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Metric Selector */}
          <View className="flex-row gap-2">
            {(['attendance', 'sessions', 'trends'] as const).map((metric) => (
              <TouchableOpacity
                key={metric}
                onPress={() => setSelectedMetric(metric)}
                className={`px-3 py-2 rounded-lg ${
                  selectedMetric === metric ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-medium text-sm capitalize ${
                  selectedMetric === metric ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  {metric}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {/* Overall Statistics */}
          <View className={`m-4 p-6 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-xl mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Overview ({selectedPeriod})
            </Text>

            <View className="flex-row flex-wrap gap-4">
              <View className="flex-1 min-w-[120px]">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Attendance Rate
                </Text>
                <Text className={`font-rubik-bold text-2xl ${
                  overallStats.attendanceRate >= 90 ? 'text-success' :
                  overallStats.attendanceRate >= 75 ? 'text-warning' : 'text-error'
                }`}>
                  {overallStats.attendanceRate.toFixed(1)}%
                </Text>
              </View>

              <View className="flex-1 min-w-[120px]">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Total Sessions
                </Text>
                <Text className={`font-rubik-bold text-2xl text-primary-500`}>
                  {overallStats.totalSessions}
                </Text>
              </View>

              <View className="flex-1 min-w-[120px]">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Present
                </Text>
                <Text className={`font-rubik-bold text-2xl text-success`}>
                  {overallStats.presentCount}
                </Text>
              </View>

              <View className="flex-1 min-w-[120px]">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Absent
                </Text>
                <Text className={`font-rubik-bold text-2xl text-error`}>
                  {overallStats.absentCount}
                </Text>
              </View>
            </View>
          </View>

          {/* Dynamic Content Based on Selected Metric */}
          {selectedMetric === 'trends' && (
            <View className={`mx-4 mb-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
              {renderSimpleChart(trendData)}
            </View>
          )}

          {selectedMetric === 'attendance' && (
            <View className={`mx-4 mb-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
              {renderClassComparison(classComparison)}
            </View>
          )}

          {selectedMetric === 'sessions' && (
            <View className={`mx-4 mb-4 p-6 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
              <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Session Breakdown
              </Text>

              <View className="space-y-4">
                <View className="flex-row justify-between items-center">
                  <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Completed Sessions
                  </Text>
                  <Text className="font-rubik-bold text-success">
                    {sessions.filter(s => s.status === 'completed').length}
                  </Text>
                </View>

                <View className="flex-row justify-between items-center">
                  <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Active Sessions
                  </Text>
                  <Text className="font-rubik-bold text-warning">
                    {sessions.filter(s => s.status === 'active').length}
                  </Text>
                </View>

                <View className="flex-row justify-between items-center">
                  <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Average per Day
                  </Text>
                  <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {trendData.length > 0 ? (trendData.reduce((sum, d) => sum + d.total_sessions, 0) / trendData.length).toFixed(1) : '0'}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Insights */}
          <View className={`mx-4 mb-4 p-6 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Insights
            </Text>

            <View className="space-y-3">
              {overallStats.attendanceRate >= 90 && (
                <View className="flex-row items-center">
                  <IconSymbol name="checkmark.circle.fill" size={20} color="#10B981" />
                  <Text className={`font-rubik ml-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Excellent attendance rate! Keep up the good work.
                  </Text>
                </View>
              )}

              {overallStats.attendanceRate < 75 && (
                <View className="flex-row items-center">
                  <IconSymbol name="exclamationmark.triangle.fill" size={20} color="#EF4444" />
                  <Text className={`font-rubik ml-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Attendance rate needs improvement. Consider follow-up actions.
                  </Text>
                </View>
              )}

              {classComparison.length > 1 && (
                <View className="flex-row items-center">
                  <IconSymbol name="chart.bar.fill" size={20} color="#3B82F6" />
                  <Text className={`font-rubik ml-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {classComparison[0].class_name} has the highest attendance rate at {classComparison[0].attendance_rate.toFixed(1)}%.
                  </Text>
                </View>
              )}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default AnalyticsScreen;
