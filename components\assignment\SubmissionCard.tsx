import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';

interface Assignment {
  id: string;
  title: string;
  max_points: number;
}

interface Submission {
  id: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  attachment_urls?: string[];
  status: 'draft' | 'submitted' | 'graded' | 'returned';
  grade?: number;
  feedback?: string;
  gemini_feedback?: string;
  submitted_at: string;
  graded_at?: string;
  graded_by?: string;
  student_name?: string;
  student_email?: string;
}

interface SubmissionCardProps {
  submission: Submission;
  assignment: Assignment;
  onPress: () => void;
  isSelected?: boolean;
  onSelect?: () => void;
  showSelection?: boolean;
}

export default function SubmissionCard({
  submission,
  assignment,
  onPress,
  isSelected = false,
  onSelect,
  showSelection = false,
}: SubmissionCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300';
      case 'graded':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300';
      case 'returned':
        return 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300';
      case 'draft':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'checkmark-circle-outline';
      case 'graded':
        return 'school-outline';
      case 'returned':
        return 'return-down-back-outline';
      case 'draft':
        return 'document-outline';
      default:
        return 'document-outline';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      });
    }
  };

  const getGradeColor = (grade: number, maxPoints: number) => {
    const percentage = (grade / maxPoints) * 100;
    if (percentage >= 90) return 'text-green-500';
    if (percentage >= 80) return 'text-blue-500';
    if (percentage >= 70) return 'text-yellow-500';
    if (percentage >= 60) return 'text-orange-500';
    return 'text-red-500';
  };

  const getGradePercentage = () => {
    if (!submission.grade) return 0;
    return Math.round((submission.grade / assignment.max_points) * 100);
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`p-4 rounded-xl mb-3 ${
        isSelected
          ? 'border-2 border-primary-500 bg-primary-50 dark:bg-primary-900/20'
          : isDark
          ? 'bg-dark-card'
          : 'bg-light-card'
      }`}
    >
      <View className="flex-row items-start justify-between">
        {/* Selection Checkbox */}
        {showSelection && (
          <TouchableOpacity
            onPress={onSelect}
            className={`w-6 h-6 rounded border-2 mr-3 items-center justify-center ${
              isSelected
                ? 'bg-primary-500 border-primary-500'
                : isDark
                ? 'border-dark-border'
                : 'border-light-border'
            }`}
          >
            {isSelected && (
              <Ionicons name="checkmark" size={16} color="white" />
            )}
          </TouchableOpacity>
        )}

        {/* Main Content */}
        <View className="flex-1">
          {/* Student Info */}
          <View className="flex-row items-start justify-between mb-2">
            <View className="flex-1 mr-3">
              <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {submission.student_name || 'Unknown Student'}
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {submission.student_email}
              </Text>
            </View>

            {/* Status Badge */}
            <View className={`px-3 py-1 rounded-full ${getStatusColor(submission.status)}`}>
              <View className="flex-row items-center">
                <Ionicons
                  name={getStatusIcon(submission.status) as any}
                  size={12}
                  color="currentColor"
                />
                <Text className="ml-1 font-rubik-medium text-xs capitalize">
                  {submission.status}
                </Text>
              </View>
            </View>
          </View>

          {/* Submission Details */}
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <Ionicons
                name="time-outline"
                size={16}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`ml-2 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Submitted {formatDate(submission.submitted_at)}
              </Text>
            </View>

            {submission.attachment_urls && submission.attachment_urls.length > 0 && (
              <View className="flex-row items-center">
                <Ionicons
                  name="attach-outline"
                  size={16}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
                <Text className={`ml-1 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {submission.attachment_urls.length} file{submission.attachment_urls.length > 1 ? 's' : ''}
                </Text>
              </View>
            )}
          </View>

          {/* Content Preview */}
          {submission.content && (
            <View className={`p-3 rounded-lg mb-3 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text' : 'text-light-text'}`} numberOfLines={3}>
                {submission.content}
              </Text>
            </View>
          )}

          {/* Grade and Feedback */}
          {submission.status === 'graded' && submission.grade !== undefined && (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className={`w-12 h-12 rounded-full items-center justify-center ${
                  isDark ? 'bg-dark-background' : 'bg-light-background'
                }`}>
                  <Text className={`font-rubik-bold text-lg ${getGradeColor(submission.grade, assignment.max_points)}`}>
                    {getGradePercentage()}%
                  </Text>
                </View>
                <View className="ml-3">
                  <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    {submission.grade}/{assignment.max_points} points
                  </Text>
                  {submission.graded_at && (
                    <Text className={`font-rubik text-xs ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                      Graded {formatDate(submission.graded_at)}
                    </Text>
                  )}
                </View>
              </View>

              {submission.feedback && (
                <View className="flex-row items-center">
                  <Ionicons
                    name="chatbubble-outline"
                    size={16}
                    color={isDark ? '#9CA3AF' : '#6B7280'}
                  />
                  <Text className={`ml-1 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                    Feedback
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Pending Grade Indicator */}
          {submission.status === 'submitted' && (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className={`w-8 h-8 rounded-full items-center justify-center ${
                  isDark ? 'bg-orange-900/30' : 'bg-orange-100'
                }`}>
                  <Ionicons
                    name="time-outline"
                    size={16}
                    color="#F59E0B"
                  />
                </View>
                <Text className={`ml-3 font-rubik-medium text-orange-600 dark:text-orange-400`}>
                  Awaiting Grade
                </Text>
              </View>

              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Max: {assignment.max_points} pts
              </Text>
            </View>
          )}

          {/* AI Feedback Indicator */}
          {submission.gemini_feedback && (
            <View className="flex-row items-center mt-2">
              <Ionicons name="sparkles-outline" size={14} color="#8B5CF6" />
              <Text className="ml-1 font-rubik text-xs text-purple-600 dark:text-purple-400">
                AI Feedback Available
              </Text>
            </View>
          )}
        </View>

        {/* Action Button */}
        {!showSelection && (
          <TouchableOpacity
            onPress={(e) => {
              e.stopPropagation();
              // Handle quick actions menu
            }}
            className="p-2 ml-2"
          >
            <Ionicons
              name="ellipsis-vertical"
              size={20}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
}
