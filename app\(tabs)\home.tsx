import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { Alert, AppState, RefreshControl, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Components
import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';
import {
    DashboardHeader,
    QuickActions,
    StatsOverview,
} from '@/components/teacher';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { LoadingScreen } from '@/components/ui/LoadingScreen';

// Hooks and stores
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { useAuthStore } from '@/stores/authStore';
import { useSecurityStore } from '@/stores/securityStore';
import { useTeacherStore } from '@/stores/teacherStore';

const HomeScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const { supabaseUser, isLoaded } = useSupabaseAuth();
  const [initialLoading, setInitialLoading] = useState(true);

  // Zustand stores with security features
  const { updateActivity } = useAuthStore();
  const { stats, isLoading: teacherLoading, error: teacherError, refreshing, refreshData } = useTeacherStore();
  const { getSecurityStatus, setAppBackground, isAccountLocked, requiresReauth } = useSecurityStore();

  // Security monitoring
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      setAppBackground(nextAppState === 'background');
      if (nextAppState === 'active') {
        updateActivity();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [setAppBackground, updateActivity]);

  // Check security status and handle re-authentication
  useEffect(() => {
    const securityStatus = getSecurityStatus();

    if (isAccountLocked()) {
      Alert.alert(
        'Account Locked',
        'Your account has been temporarily locked due to security concerns. Please try again later.',
        [{ text: 'OK', onPress: () => router.replace('/sign-in') }]
      );
      return;
    }

    if (requiresReauth) {
      Alert.alert(
        'Re-authentication Required',
        'For security reasons, please sign in again.',
        [{ text: 'OK', onPress: () => router.replace('/sign-in') }]
      );
      return;
    }

    if (!securityStatus.isSecure) {
      console.warn('Security warnings:', securityStatus.warnings);
    }
  }, [isAccountLocked, requiresReauth, getSecurityStatus]);

  // Load initial data
  const loadTeacherData = useCallback(async () => {
    try {
      setInitialLoading(true);
      await refreshData();
    } catch (error) {
      console.error('Failed to load teacher data:', error);
    } finally {
      setInitialLoading(false);
    }
  }, [refreshData]);

  useEffect(() => {
    if (isLoaded && supabaseUser) {
      loadTeacherData();
    }
  }, [isLoaded, supabaseUser, loadTeacherData]);

  // Navigation handlers with security checks
  const handleSecureNavigation = useCallback((route: string) => {
    updateActivity();
    const securityStatus = getSecurityStatus();

    if (!securityStatus.isSecure) {
      Alert.alert('Security Check Failed', 'Please try again or contact support.');
      return;
    }

    router.push(route as any);
  }, [updateActivity, getSecurityStatus]);

  if (!isLoaded || initialLoading) {
    return <LoadingScreen message="Loading your dashboard..." />;
  }

  if (teacherError) {
    return (
      <ErrorScreen
        message={teacherError}
        onRetry={loadTeacherData}
      />
    );
  }

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ padding: 16 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={loadTeacherData}
              tintColor={isDark ? '#60A5FA' : '#2563EB'}
            />
          }
        >
          {/* Header */}
          <DashboardHeader userName={supabaseUser?.name} />

          {/* Stats Overview */}
          <StatsOverview
            stats={stats}
            onNavigate={handleSecureNavigation}
          />

          {/* Quick Actions */}
          <QuickActions
            onNavigate={handleSecureNavigation}
            loading={teacherLoading}
            pendingAssignments={stats.pendingAssignments}
          />
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default HomeScreen;