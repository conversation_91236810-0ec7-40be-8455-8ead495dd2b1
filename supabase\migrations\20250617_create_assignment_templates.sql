-- Create assignment templates system

-- Assignment templates table
CREATE TABLE IF NOT EXISTS assignment_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(100) NOT NULL CHECK (category IN ('essay', 'research', 'lab', 'presentation', 'quiz', 'homework', 'project', 'exam')),
  subject VARCHAR(100) NOT NULL,
  grade_level VARCHAR(50) NOT NULL,
  template_data JSONB NOT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Template usage tracking table
CREATE TABLE IF NOT EXISTS template_usage_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID NOT NULL REFERENCES assignment_templates(id) ON DELETE CASCADE,
  used_by_teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  assignment_id UUID REFERENCES assignments(id) ON DELETE SET NULL,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Template ratings table (for public templates)
CREATE TABLE IF NOT EXISTS template_ratings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID NOT NULL REFERENCES assignment_templates(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(template_id, teacher_id)
);

-- Template favorites table
CREATE TABLE IF NOT EXISTS template_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID NOT NULL REFERENCES assignment_templates(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(template_id, teacher_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assignment_templates_teacher_id ON assignment_templates(teacher_id);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_category ON assignment_templates(category);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_subject ON assignment_templates(subject);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_is_public ON assignment_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_usage_count ON assignment_templates(usage_count);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_created_at ON assignment_templates(created_at);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_tags ON assignment_templates USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_template_usage_log_template_id ON template_usage_log(template_id);
CREATE INDEX IF NOT EXISTS idx_template_usage_log_teacher_id ON template_usage_log(used_by_teacher_id);
CREATE INDEX IF NOT EXISTS idx_template_ratings_template_id ON template_ratings(template_id);
CREATE INDEX IF NOT EXISTS idx_template_favorites_teacher_id ON template_favorites(teacher_id);

-- Create updated_at trigger
CREATE TRIGGER update_assignment_templates_updated_at 
  BEFORE UPDATE ON assignment_templates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies for assignment_templates
ALTER TABLE assignment_templates ENABLE ROW LEVEL SECURITY;

-- Teachers can view their own templates and public templates
CREATE POLICY "Teachers can view accessible templates" ON assignment_templates
  FOR SELECT USING (
    is_public = true OR
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = assignment_templates.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Teachers can create their own templates
CREATE POLICY "Teachers can create templates" ON assignment_templates
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = assignment_templates.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Teachers can update their own templates
CREATE POLICY "Teachers can update their own templates" ON assignment_templates
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = assignment_templates.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Teachers can delete their own templates
CREATE POLICY "Teachers can delete their own templates" ON assignment_templates
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = assignment_templates.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- RLS Policies for template_usage_log
ALTER TABLE template_usage_log ENABLE ROW LEVEL SECURITY;

-- Teachers can view usage logs for their templates and their own usage
CREATE POLICY "Teachers can view relevant usage logs" ON template_usage_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE (teachers.id = template_usage_log.used_by_teacher_id OR 
             teachers.id IN (SELECT teacher_id FROM assignment_templates WHERE id = template_usage_log.template_id))
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Teachers can create usage logs
CREATE POLICY "Teachers can create usage logs" ON template_usage_log
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = template_usage_log.used_by_teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- RLS Policies for template_ratings
ALTER TABLE template_ratings ENABLE ROW LEVEL SECURITY;

-- Teachers can view all ratings for public templates
CREATE POLICY "Teachers can view template ratings" ON template_ratings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM assignment_templates 
      WHERE assignment_templates.id = template_ratings.template_id 
      AND assignment_templates.is_public = true
    )
  );

-- Teachers can rate public templates (not their own)
CREATE POLICY "Teachers can rate public templates" ON template_ratings
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = template_ratings.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    ) AND
    EXISTS (
      SELECT 1 FROM assignment_templates 
      WHERE assignment_templates.id = template_ratings.template_id 
      AND assignment_templates.is_public = true
      AND assignment_templates.teacher_id != template_ratings.teacher_id
    )
  );

-- Teachers can update their own ratings
CREATE POLICY "Teachers can update their own ratings" ON template_ratings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = template_ratings.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Teachers can delete their own ratings
CREATE POLICY "Teachers can delete their own ratings" ON template_ratings
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = template_ratings.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- RLS Policies for template_favorites
ALTER TABLE template_favorites ENABLE ROW LEVEL SECURITY;

-- Teachers can view their own favorites
CREATE POLICY "Teachers can view their own favorites" ON template_favorites
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = template_favorites.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Teachers can manage their own favorites
CREATE POLICY "Teachers can manage their own favorites" ON template_favorites
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM teachers 
      JOIN users ON teachers.user_id = users.id
      WHERE teachers.id = template_favorites.teacher_id 
      AND users.clerk_user_id = auth.jwt() ->> 'sub'
    )
  );

-- Function to increment template usage count
CREATE OR REPLACE FUNCTION increment_template_usage(template_uuid UUID)
RETURNS void AS $$
BEGIN
  UPDATE assignment_templates
  SET usage_count = usage_count + 1, updated_at = NOW()
  WHERE id = template_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to get template statistics
CREATE OR REPLACE FUNCTION get_template_stats(template_uuid UUID)
RETURNS TABLE(
  usage_count INTEGER,
  average_rating DECIMAL(3,2),
  total_ratings INTEGER,
  favorite_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.usage_count,
    COALESCE(AVG(r.rating), 0)::DECIMAL(3,2) as average_rating,
    COUNT(r.rating)::INTEGER as total_ratings,
    COUNT(f.id)::INTEGER as favorite_count
  FROM assignment_templates t
  LEFT JOIN template_ratings r ON t.id = r.template_id
  LEFT JOIN template_favorites f ON t.id = f.template_id
  WHERE t.id = template_uuid
  GROUP BY t.id, t.usage_count;
END;
$$ LANGUAGE plpgsql;

-- Function to search templates
CREATE OR REPLACE FUNCTION search_templates(
  search_query TEXT DEFAULT '',
  category_filter TEXT DEFAULT 'all',
  subject_filter TEXT DEFAULT 'all',
  public_only BOOLEAN DEFAULT true,
  teacher_uuid UUID DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  name VARCHAR(255),
  description TEXT,
  category VARCHAR(100),
  subject VARCHAR(100),
  grade_level VARCHAR(50),
  is_public BOOLEAN,
  usage_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  teacher_name VARCHAR(255),
  average_rating DECIMAL(3,2),
  tags TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.name,
    t.description,
    t.category,
    t.subject,
    t.grade_level,
    t.is_public,
    t.usage_count,
    t.created_at,
    u.name as teacher_name,
    COALESCE(AVG(r.rating), 0)::DECIMAL(3,2) as average_rating,
    t.tags
  FROM assignment_templates t
  JOIN teachers te ON t.teacher_id = te.id
  JOIN users u ON te.user_id = u.id
  LEFT JOIN template_ratings r ON t.id = r.template_id
  WHERE 
    (NOT public_only OR t.is_public = true OR (teacher_uuid IS NOT NULL AND t.teacher_id = teacher_uuid)) AND
    (search_query = '' OR 
     t.name ILIKE '%' || search_query || '%' OR 
     t.description ILIKE '%' || search_query || '%' OR
     EXISTS (SELECT 1 FROM unnest(t.tags) tag WHERE tag ILIKE '%' || search_query || '%')) AND
    (category_filter = 'all' OR t.category = category_filter) AND
    (subject_filter = 'all' OR t.subject = subject_filter)
  GROUP BY t.id, t.name, t.description, t.category, t.subject, t.grade_level, 
           t.is_public, t.usage_count, t.created_at, u.name, t.tags
  ORDER BY t.usage_count DESC, t.created_at DESC;
END;
$$ LANGUAGE plpgsql;
