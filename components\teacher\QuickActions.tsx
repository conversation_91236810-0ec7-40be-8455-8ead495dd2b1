import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, View } from 'react-native';
import { TeacherDashboardCard } from './TeacherDashboardCard';

interface QuickActionsProps {
  onNavigate: (route: string) => void;
  loading?: boolean;
  pendingAssignments: number;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ 
  onNavigate, 
  loading = false,
  pendingAssignments 
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <View className="mb-6">
      {/* Section Title */}
      <Text 
        className={`
          text-lg font-rubik-semibold mb-4
          ${isDark ? 'text-dark-text' : 'text-light-text'}
        `}
      >
        Quick Actions
      </Text>

      {/* Action Cards */}
      <TeacherDashboardCard
        title="Materials"
        icon="folder.fill"
        onPress={() => onNavigate('/materials')}
        loading={loading}
      />

      <TeacherDashboardCard
        title="Create Assignment"
        icon="square.and.pencil"
        onPress={() => onNavigate('/reports/assignment/create-enhanced')}
        loading={loading}
      />

      <TeacherDashboardCard
        title="Create Mock Test"
        icon="doc.text.fill"
        onPress={() => onNavigate('/reports/mock-test/create')}
        loading={loading}
      />

      <TeacherDashboardCard
        title="Grade Submissions"
        icon="checkmark.circle.fill"
        count={pendingAssignments}
        onPress={() => onNavigate('/reports/grades')}
        loading={loading}
      />
    </View>
  );
};
