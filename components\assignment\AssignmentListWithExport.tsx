import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAssignmentStore } from '@/stores/assignmentStore';
import ExportModal from './ExportModal';
import BulkOperationsModal from './BulkOperationsModal';

interface Assignment {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'published' | 'closed';
  due_date: string;
  max_points: number;
  submissions_count?: number;
  graded_count?: number;
}

interface AssignmentListWithExportProps {
  classId: string;
  className?: string;
}

export default function AssignmentListWithExport({
  classId,
  className = 'Class',
}: AssignmentListWithExportProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [selectedAssignments, setSelectedAssignments] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [bulkModalVisible, setBulkModalVisible] = useState(false);
  const [exportType, setExportType] = useState<'single' | 'multiple' | 'class'>('single');
  const [selectedAssignmentId, setSelectedAssignmentId] = useState<string>('');

  const { assignments, fetchAssignments, loading } = useAssignmentStore();

  useEffect(() => {
    fetchAssignments(classId);
  }, [classId]);

  const toggleAssignmentSelection = (assignmentId: string) => {
    setSelectedAssignments(prev => 
      prev.includes(assignmentId)
        ? prev.filter(id => id !== assignmentId)
        : [...prev, assignmentId]
    );
  };

  const selectAllAssignments = () => {
    if (selectedAssignments.length === assignments.length) {
      setSelectedAssignments([]);
    } else {
      setSelectedAssignments(assignments.map(a => a.id));
    }
  };

  const handleSingleExport = (assignmentId: string) => {
    setSelectedAssignmentId(assignmentId);
    setExportType('single');
    setExportModalVisible(true);
  };

  const handleMultipleExport = () => {
    if (selectedAssignments.length === 0) {
      Alert.alert('No Selection', 'Please select assignments to export.');
      return;
    }
    setExportType('multiple');
    setExportModalVisible(true);
  };

  const handleClassExport = () => {
    setExportType('class');
    setExportModalVisible(true);
  };

  const handleBulkOperations = () => {
    if (selectedAssignments.length === 0) {
      Alert.alert('No Selection', 'Please select assignments for bulk operations.');
      return;
    }
    setBulkModalVisible(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return isDark ? 'text-green-400' : 'text-green-600';
      case 'closed':
        return isDark ? 'text-red-400' : 'text-red-600';
      default:
        return isDark ? 'text-yellow-400' : 'text-yellow-600';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'published':
        return isDark ? 'bg-green-900/30' : 'bg-green-100';
      case 'closed':
        return isDark ? 'bg-red-900/30' : 'bg-red-100';
      default:
        return isDark ? 'bg-yellow-900/30' : 'bg-yellow-100';
    }
  };

  const assignmentTitles = assignments.reduce((acc, assignment) => {
    acc[assignment.id] = assignment.title;
    return acc;
  }, {} as Record<string, string>);

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="px-6 py-4">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Assignments
            </Text>
            <Text className={`text-sm font-rubik ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              {className} • {assignments.length} assignment(s)
            </Text>
          </View>

          <View className="flex-row space-x-2">
            <TouchableOpacity
              onPress={handleClassExport}
              className={`px-4 py-2 rounded-lg ${isDark ? 'bg-blue-600' : 'bg-blue-500'}`}
            >
              <View className="flex-row items-center">
                <IconSymbol name="square.and.arrow.up" size={16} color="#FFFFFF" />
                <Text className="text-white font-rubik-medium ml-2">Export Class</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setSelectionMode(!selectionMode)}
              className={`px-4 py-2 rounded-lg ${
                selectionMode
                  ? isDark ? 'bg-red-600' : 'bg-red-500'
                  : isDark ? 'bg-gray-700' : 'bg-gray-200'
              }`}
            >
              <Text className={`font-rubik-medium ${
                selectionMode ? 'text-white' : isDark ? 'text-white' : 'text-gray-900'
              }`}>
                {selectionMode ? 'Cancel' : 'Select'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Selection Controls */}
        {selectionMode && (
          <View className="flex-row items-center justify-between mb-4">
            <TouchableOpacity
              onPress={selectAllAssignments}
              className={`px-3 py-2 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
            >
              <Text className={`font-rubik-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {selectedAssignments.length === assignments.length ? 'Deselect All' : 'Select All'}
              </Text>
            </TouchableOpacity>

            <View className="flex-row space-x-2">
              <TouchableOpacity
                onPress={handleMultipleExport}
                className={`px-3 py-2 rounded-lg ${
                  selectedAssignments.length > 0
                    ? isDark ? 'bg-blue-600' : 'bg-blue-500'
                    : 'bg-gray-400'
                }`}
                disabled={selectedAssignments.length === 0}
              >
                <Text className="text-white font-rubik-medium">Export ({selectedAssignments.length})</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleBulkOperations}
                className={`px-3 py-2 rounded-lg ${
                  selectedAssignments.length > 0
                    ? isDark ? 'bg-purple-600' : 'bg-purple-500'
                    : 'bg-gray-400'
                }`}
                disabled={selectedAssignments.length === 0}
              >
                <Text className="text-white font-rubik-medium">Actions</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Assignment List */}
      <ScrollView className="flex-1 px-6">
        {assignments.map((assignment) => (
          <TouchableOpacity
            key={assignment.id}
            onPress={() => {
              if (selectionMode) {
                toggleAssignmentSelection(assignment.id);
              }
            }}
            className={`p-4 rounded-xl mb-3 border ${
              selectionMode && selectedAssignments.includes(assignment.id)
                ? 'border-primary-500 bg-primary-500/10'
                : isDark
                ? 'border-gray-700 bg-dark-card'
                : 'border-gray-200 bg-white'
            }`}
          >
            <View className="flex-row items-start justify-between">
              <View className="flex-1">
                <View className="flex-row items-center mb-2">
                  {selectionMode && (
                    <View className="mr-3">
                      <IconSymbol
                        name={selectedAssignments.includes(assignment.id) ? "checkmark.circle.fill" : "circle"}
                        size={20}
                        color={selectedAssignments.includes(assignment.id) ? "#3B82F6" : isDark ? "#6B7280" : "#9CA3AF"}
                      />
                    </View>
                  )}
                  <Text className={`font-rubik-bold text-lg flex-1 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    {assignment.title}
                  </Text>
                  <View className={`px-2 py-1 rounded-full ${getStatusBg(assignment.status)}`}>
                    <Text className={`font-rubik-medium text-xs ${getStatusColor(assignment.status)}`}>
                      {assignment.status.toUpperCase()}
                    </Text>
                  </View>
                </View>

                <Text className={`font-rubik text-sm mb-2 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  {assignment.description}
                </Text>

                <View className="flex-row items-center justify-between">
                  <Text className={`font-rubik text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Due: {new Date(assignment.due_date).toLocaleDateString()}
                  </Text>
                  <Text className={`font-rubik text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    {assignment.submissions_count || 0} submissions • {assignment.graded_count || 0} graded
                  </Text>
                </View>
              </View>

              {!selectionMode && (
                <TouchableOpacity
                  onPress={() => handleSingleExport(assignment.id)}
                  className={`ml-3 p-2 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
                >
                  <IconSymbol
                    name="square.and.arrow.up"
                    size={16}
                    color={isDark ? '#FFFFFF' : '#000000'}
                  />
                </TouchableOpacity>
              )}
            </View>
          </TouchableOpacity>
        ))}

        {assignments.length === 0 && !loading && (
          <View className="items-center justify-center py-12">
            <IconSymbol
              name="doc.text"
              size={48}
              color={isDark ? '#6B7280' : '#9CA3AF'}
            />
            <Text className={`text-lg font-rubik-medium mt-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              No assignments found
            </Text>
            <Text className={`text-sm font-rubik mt-2 ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>
              Create your first assignment to get started
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Export Modal */}
      <ExportModal
        visible={exportModalVisible}
        onClose={() => setExportModalVisible(false)}
        assignmentId={exportType === 'single' ? selectedAssignmentId : undefined}
        assignmentIds={exportType === 'multiple' ? selectedAssignments : undefined}
        classId={exportType === 'class' ? classId : undefined}
        exportType={exportType}
        assignmentTitle={
          exportType === 'single' 
            ? assignments.find(a => a.id === selectedAssignmentId)?.title 
            : undefined
        }
      />

      {/* Bulk Operations Modal */}
      <BulkOperationsModal
        visible={bulkModalVisible}
        onClose={() => setBulkModalVisible(false)}
        selectedAssignments={selectedAssignments}
        assignmentTitles={assignmentTitles}
        onOperationComplete={() => {
          setSelectedAssignments([]);
          setSelectionMode(false);
          fetchAssignments(classId);
        }}
      />
    </View>
  );
}
