import * as Location from 'expo-location';
import { Alert } from 'react-native';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

export interface SchoolLocation {
  latitude: number;
  longitude: number;
  radius: number; // in meters
  name: string;
}

// Default school location (this should be configurable per school)
const DEFAULT_SCHOOL_LOCATION: SchoolLocation = {
  latitude: 28.6139, // Example: New Delhi coordinates
  longitude: 77.2090,
  radius: 100, // 100 meters radius
  name: 'School Campus'
};

/**
 * Fetch school location from database based on tenant
 */
export const getSchoolLocation = async (tenantId: string): Promise<SchoolLocation | null> => {
  try {
    const { supabaseAdmin } = await import('@/lib/supabase');
    const { data, error } = await supabaseAdmin
      .from('tenants')
      .select('latitude, longitude, name')
      .eq('id', tenantId)
      .single();

    if (error) {
      console.error('Error fetching school location:', error);
      return null;
    }

    if (!data.latitude || !data.longitude) {
      console.warn('School location not configured for tenant:', tenantId);
      return null;
    }

    return {
      latitude: parseFloat(data.latitude),
      longitude: parseFloat(data.longitude),
      radius: 100, // Default 100 meters radius
      name: data.name || 'School Campus'
    };
  } catch (error) {
    console.error('Error getting school location:', error);
    return null;
  }
};

/**
 * Request location permissions from the user
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

    if (foregroundStatus !== 'granted') {
      Alert.alert(
        'Location Permission Required',
        'This app needs location access to verify your attendance location. Please enable location permissions in your device settings.',
        [{ text: 'OK' }]
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

/**
 * Get current location with high accuracy
 */
export const getCurrentLocation = async (): Promise<LocationData | null> => {
  try {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      return null;
    }

    // Check if location services are enabled
    const isEnabled = await Location.hasServicesEnabledAsync();
    if (!isEnabled) {
      Alert.alert(
        'Location Services Disabled',
        'Please enable location services on your device to mark attendance.',
        [{ text: 'OK' }]
      );
      return null;
    }

    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High,
      timeInterval: 5000,
      distanceInterval: 1,
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: location.coords.accuracy || 0,
      timestamp: location.timestamp,
    };
  } catch (error) {
    console.error('Error getting current location:', error);
    Alert.alert(
      'Location Error',
      'Unable to get your current location. Please check your GPS settings and try again.',
      [{ text: 'OK' }]
    );
    return null;
  }
};

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

/**
 * Check if current location is within school premises
 */
export const isWithinSchoolPremises = (
  currentLocation: LocationData,
  schoolLocation: SchoolLocation = DEFAULT_SCHOOL_LOCATION
): { isWithin: boolean; distance: number; message: string } => {
  const distance = calculateDistance(
    currentLocation.latitude,
    currentLocation.longitude,
    schoolLocation.latitude,
    schoolLocation.longitude
  );

  const isWithin = distance <= schoolLocation.radius;

  let message = '';
  if (isWithin) {
    message = `You are within ${schoolLocation.name} (${Math.round(distance)}m away)`;
  } else {
    message = `You are ${Math.round(distance)}m away from ${schoolLocation.name}. You must be within ${schoolLocation.radius}m to mark attendance.`;
  }

  return {
    isWithin,
    distance: Math.round(distance),
    message
  };
};

/**
 * Verify location for attendance marking
 */
export const verifyAttendanceLocation = async (
  tenantId?: string
): Promise<{
  success: boolean;
  location?: LocationData;
  distance?: number;
  message: string;
}> => {
  try {
    const currentLocation = await getCurrentLocation();

    if (!currentLocation) {
      return {
        success: false,
        message: 'Unable to get your current location'
      };
    }

    // Get school location from database if tenant ID is provided
    let schoolLocation: SchoolLocation | undefined;
    if (tenantId) {
      schoolLocation = await getSchoolLocation(tenantId) || undefined;
    }

    // If no school location configured, show appropriate message
    if (!schoolLocation || (schoolLocation.latitude === 0 && schoolLocation.longitude === 0)) {
      return {
        success: false,
        location: currentLocation,
        distance: 0,
        message: 'School location not configured. Please contact your administrator to set up the school location for attendance verification.'
      };
    }

    const verification = isWithinSchoolPremises(currentLocation, schoolLocation);

    return {
      success: verification.isWithin,
      location: currentLocation,
      distance: verification.distance,
      message: verification.message
    };
  } catch (error) {
    console.error('Error verifying attendance location:', error);
    return {
      success: false,
      message: 'Error verifying your location. Please try again.'
    };
  }
};

/**
 * Get location address from coordinates (reverse geocoding)
 */
export const getAddressFromCoordinates = async (
  latitude: number,
  longitude: number
): Promise<string> => {
  try {
    const addresses = await Location.reverseGeocodeAsync({
      latitude,
      longitude
    });

    if (addresses && addresses.length > 0) {
      const address = addresses[0];
      return `${address.street || ''} ${address.city || ''} ${address.region || ''} ${address.postalCode || ''}`.trim();
    }

    return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
  } catch (error) {
    console.error('Error getting address:', error);
    return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
  }
};

/**
 * Format location data for display
 */
export const formatLocationForDisplay = (location: LocationData): string => {
  return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)} (±${Math.round(location.accuracy)}m)`;
};

/**
 * Check if location accuracy is acceptable
 */
export const isLocationAccuracyAcceptable = (location: LocationData): boolean => {
  // Consider accuracy acceptable if it's within 50 meters
  return location.accuracy <= 50;
};

/**
 * Get device location info for security
 */
export const getDeviceLocationInfo = async () => {
  try {
    const location = await getCurrentLocation();
    if (!location) return null;

    const address = await getAddressFromCoordinates(location.latitude, location.longitude);

    return {
      coordinates: {
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy
      },
      address,
      timestamp: new Date(location.timestamp).toISOString(),
      isAccurate: isLocationAccuracyAcceptable(location)
    };
  } catch (error) {
    console.error('Error getting device location info:', error);
    return null;
  }
};
