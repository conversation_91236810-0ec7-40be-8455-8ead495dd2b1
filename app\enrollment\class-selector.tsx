import React from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useEnrollmentStore, type Class } from '@/stores/enrollmentStore';

const ClassSelectorScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  
  const { availableClasses, isLoading } = useEnrollmentStore();

  const handleClassSelect = (classItem: Class) => {
    // For now, just go back - we'll implement class selection state later
    router.back();
  };

  const renderClassItem = ({ item }: { item: Class }) => {
    return (
      <TouchableOpacity
        onPress={() => handleClassSelect(item)}
        className={`p-4 rounded-lg mb-3 border ${
          isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
        }`}
        style={{
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        }}
      >
        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <Text className={`font-rubik-semibold text-base mb-1 ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              {item.name}
            </Text>
            
            <View className="flex-row items-center mb-2">
              {item.grade && (
                <View className="flex-row items-center mr-4">
                  <IconSymbol
                    name="graduationcap.fill"
                    size={14}
                    color={isDark ? '#9CA3AF' : '#6B7280'}
                  />
                  <Text className={`ml-1 font-rubik text-sm ${
                    isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                  }`}>
                    Grade {item.grade}
                  </Text>
                </View>
              )}
              
              {item.section && (
                <View className="flex-row items-center mr-4">
                  <IconSymbol
                    name="rectangle.3.group.fill"
                    size={14}
                    color={isDark ? '#9CA3AF' : '#6B7280'}
                  />
                  <Text className={`ml-1 font-rubik text-sm ${
                    isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                  }`}>
                    Section {item.section}
                  </Text>
                </View>
              )}
            </View>
            
            {item.student_count !== undefined && (
              <View className="flex-row items-center">
                <IconSymbol
                  name="person.2.fill"
                  size={14}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
                <Text className={`ml-1 font-rubik text-sm ${
                  isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                }`}>
                  {item.student_count} students
                </Text>
              </View>
            )}
          </View>
          
          <View className="ml-3">
            <IconSymbol
              name="chevron.right"
              size={20}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center py-12">
      <IconSymbol
        name="building.2.fill"
        size={48}
        color={isDark ? '#9CA3AF' : '#6B7280'}
      />
      <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        No classes available
      </Text>
      <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        You need to be assigned to classes to enroll students
      </Text>
    </View>
  );

  const renderLoadingState = () => (
    <View className="flex-1 justify-center items-center py-12">
      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
        Loading classes...
      </Text>
    </View>
  );

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className={`flex-row items-center p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <IconSymbol name="arrow.left" size={24} color={isDark ? '#9CA3AF' : '#6B7280'} />
        </TouchableOpacity>
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Select Class
        </Text>
      </View>

      {/* Class List */}
      <View className="flex-1">
        {isLoading ? (
          renderLoadingState()
        ) : (
          <FlatList
            data={availableClasses}
            renderItem={renderClassItem}
            keyExtractor={(item) => item.id}
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ 
              flexGrow: 1,
              padding: 16 
            }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default ClassSelectorScreen;
