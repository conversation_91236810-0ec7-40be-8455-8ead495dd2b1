import { supabase } from '@/lib/supabase';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types for teacher-specific data
interface Class {
  id: string;
  name: string;
  subject: string;
  student_count: number;
  grade: string;
}

interface Assignment {
  id: string;
  title: string;
  due_date: string;
  status: 'draft' | 'published' | 'closed';
  submissions_count: number;
  total_students: number;
}

interface Attendance {
  id: string;
  date: string;
  class_id: string;
  present_count: number;
  total_students: number;
}

interface TeacherStats {
  totalClasses: number;
  totalStudents: number;
  pendingAssignments: number;
  todayAttendance: number;
}

interface TeacherState {
  // Data
  classes: Class[];
  assignments: Assignment[];
  recentAttendance: Attendance[];
  stats: TeacherStats;

  // UI State
  isLoading: boolean;
  error: string | null;
  refreshing: boolean;
  lastUpdated: number;
}

interface TeacherActions {
  // Data actions
  setClasses: (classes: Class[]) => void;
  setAssignments: (assignments: Assignment[]) => void;
  setRecentAttendance: (attendance: Attendance[]) => void;
  setStats: (stats: TeacherStats) => void;

  // UI actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setRefreshing: (refreshing: boolean) => void;
  updateLastUpdated: () => void;

  // Utility actions
  clearData: () => void;
  refreshData: () => Promise<void>;
}

type TeacherStore = TeacherState & TeacherActions;

const initialStats: TeacherStats = {
  totalClasses: 0,
  totalStudents: 0,
  pendingAssignments: 0,
  todayAttendance: 0,
};

export const useTeacherStore = create<TeacherStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial State
      classes: [],
      assignments: [],
      recentAttendance: [],
      stats: initialStats,
      isLoading: false,
      error: null,
      refreshing: false,
      lastUpdated: 0,

      // Actions
      setClasses: (classes) => {
        set((state) => {
          state.classes = classes;
          state.stats.totalClasses = classes.length;
          state.stats.totalStudents = classes.reduce((sum, cls) => sum + cls.student_count, 0);
          state.lastUpdated = Date.now();
        });
      },

      setAssignments: (assignments) => {
        set((state) => {
          state.assignments = assignments;
          state.stats.pendingAssignments = assignments.filter(a => a.status === 'published').length;
          state.lastUpdated = Date.now();
        });
      },

      setRecentAttendance: (attendance) => {
        set((state) => {
          state.recentAttendance = attendance;
          const today = new Date().toISOString().split('T')[0];
          const todayAttendance = attendance.find(a => a.date === today);
          state.stats.todayAttendance = todayAttendance?.present_count || 0;
          state.lastUpdated = Date.now();
        });
      },

      setStats: (stats) => {
        set((state) => {
          state.stats = stats;
          state.lastUpdated = Date.now();
        });
      },

      setLoading: (isLoading) => {
        set((state) => {
          state.isLoading = isLoading;
        });
      },

      setError: (error) => {
        set((state) => {
          state.error = error;
        });
      },

      setRefreshing: (refreshing) => {
        set((state) => {
          state.refreshing = refreshing;
        });
      },

      updateLastUpdated: () => {
        set((state) => {
          state.lastUpdated = Date.now();
        });
      },

      clearData: () => {
        set((state) => {
          state.classes = [];
          state.assignments = [];
          state.recentAttendance = [];
          state.stats = initialStats;
          state.error = null;
          state.lastUpdated = 0;
        });
      },

      refreshData: async () => {
        try {
          set({ refreshing: true, error: null });

          // Get total students
          const { count: studentsCount } = await supabase
            .from('students')
            .select('*', { count: 'exact', head: true });

          // Get total classes
          const { count: classesCount } = await supabase
            .from('classes')
            .select('*', { count: 'exact', head: true });

          // Get today's attendance count
          const today = new Date().toISOString().split('T')[0];
          const { data: attendanceData } = await supabase
            .from('attendance')
            .select('present_count')
            .eq('date', today)
            .single();

          // Get pending notices count
          const { count: noticesCount } = await supabase
            .from('notices')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'pending');

          set((state) => {
            state.stats = {
              totalStudents: studentsCount || 0,
              totalClasses: classesCount || 0,
              todayAttendance: attendanceData?.present_count || 0,
              pendingAssignments: noticesCount || 0,
            };
            state.lastUpdated = Date.now();
            state.error = null;
          });
        } catch (error) {
          set({ error: 'Failed to load teacher data' });
          console.error('Error refreshing teacher data:', error);
        } finally {
          set({ refreshing: false, isLoading: false });
        }
      },
    }))
  )
);
