import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

interface SecurityState {
  // Security tracking
  failedAttempts: number;
  lastFailedAttempt: number;
  isLocked: boolean;
  lockoutUntil: number;
  
  // Activity monitoring
  suspiciousActivity: boolean;
  lastSecurityCheck: number;
  
  // App state security
  isAppInBackground: boolean;
  backgroundTime: number;
  requiresReauth: boolean;
}

interface SecurityActions {
  // Security actions
  recordFailedAttempt: () => void;
  resetFailedAttempts: () => void;
  lockAccount: (duration?: number) => void;
  unlockAccount: () => void;
  
  // Activity monitoring
  flagSuspiciousActivity: (reason: string) => void;
  clearSuspiciousActivity: () => void;
  updateSecurityCheck: () => void;
  
  // App state management
  setAppBackground: (isBackground: boolean) => void;
  checkReauthRequired: () => boolean;
  setRequiresReauth: (required: boolean) => void;
  
  // Utility
  isAccountLocked: () => boolean;
  getSecurityStatus: () => SecurityStatus;
}

interface SecurityStatus {
  isSecure: boolean;
  warnings: string[];
  requiresAction: boolean;
}

type SecurityStore = SecurityState & SecurityActions;

// Security constants
const MAX_FAILED_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
const BACKGROUND_TIMEOUT = 5 * 60 * 1000; // 5 minutes
const SECURITY_CHECK_INTERVAL = 60 * 1000; // 1 minute

export const useSecurityStore = create<SecurityStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial State
    failedAttempts: 0,
    lastFailedAttempt: 0,
    isLocked: false,
    lockoutUntil: 0,
    suspiciousActivity: false,
    lastSecurityCheck: Date.now(),
    isAppInBackground: false,
    backgroundTime: 0,
    requiresReauth: false,

    // Security Actions
    recordFailedAttempt: () => {
      set((state) => {
        const newFailedAttempts = state.failedAttempts + 1;
        const now = Date.now();
        
        return {
          ...state,
          failedAttempts: newFailedAttempts,
          lastFailedAttempt: now,
          isLocked: newFailedAttempts >= MAX_FAILED_ATTEMPTS,
          lockoutUntil: newFailedAttempts >= MAX_FAILED_ATTEMPTS ? now + LOCKOUT_DURATION : 0,
        };
      });
    },

    resetFailedAttempts: () => {
      set((state) => ({
        ...state,
        failedAttempts: 0,
        lastFailedAttempt: 0,
        isLocked: false,
        lockoutUntil: 0,
      }));
    },

    lockAccount: (duration = LOCKOUT_DURATION) => {
      set((state) => ({
        ...state,
        isLocked: true,
        lockoutUntil: Date.now() + duration,
      }));
    },

    unlockAccount: () => {
      set((state) => ({
        ...state,
        isLocked: false,
        lockoutUntil: 0,
        failedAttempts: 0,
      }));
    },

    // Activity Monitoring
    flagSuspiciousActivity: (reason: string) => {
      console.warn(`Suspicious activity detected: ${reason}`);
      set((state) => ({
        ...state,
        suspiciousActivity: true,
        lastSecurityCheck: Date.now(),
      }));
    },

    clearSuspiciousActivity: () => {
      set((state) => ({
        ...state,
        suspiciousActivity: false,
      }));
    },

    updateSecurityCheck: () => {
      set((state) => ({
        ...state,
        lastSecurityCheck: Date.now(),
      }));
    },

    // App State Management
    setAppBackground: (isBackground: boolean) => {
      set((state) => ({
        ...state,
        isAppInBackground: isBackground,
        backgroundTime: isBackground ? Date.now() : 0,
      }));
    },

    checkReauthRequired: () => {
      const { isAppInBackground, backgroundTime } = get();
      if (isAppInBackground && backgroundTime > 0) {
        const timeInBackground = Date.now() - backgroundTime;
        return timeInBackground > BACKGROUND_TIMEOUT;
      }
      return false;
    },

    setRequiresReauth: (required: boolean) => {
      set((state) => ({
        ...state,
        requiresReauth: required,
      }));
    },

    // Utility Functions
    isAccountLocked: () => {
      const { isLocked, lockoutUntil } = get();
      if (!isLocked) return false;
      
      if (Date.now() > lockoutUntil) {
        // Auto-unlock if lockout period has passed
        get().unlockAccount();
        return false;
      }
      
      return true;
    },

    getSecurityStatus: () => {
      const state = get();
      const warnings: string[] = [];
      let isSecure = true;
      let requiresAction = false;

      // Check for account lockout
      if (state.isAccountLocked()) {
        warnings.push('Account is temporarily locked due to failed attempts');
        isSecure = false;
        requiresAction = true;
      }

      // Check for suspicious activity
      if (state.suspiciousActivity) {
        warnings.push('Suspicious activity detected');
        isSecure = false;
        requiresAction = true;
      }

      // Check if re-authentication is required
      if (state.requiresReauth || state.checkReauthRequired()) {
        warnings.push('Re-authentication required');
        requiresAction = true;
      }

      // Check for failed attempts
      if (state.failedAttempts > 0) {
        warnings.push(`${state.failedAttempts} failed authentication attempts`);
        if (state.failedAttempts >= MAX_FAILED_ATTEMPTS - 2) {
          isSecure = false;
        }
      }

      return {
        isSecure,
        warnings,
        requiresAction,
      };
    },
  }))
);

// Security monitoring - check for background timeout
useSecurityStore.subscribe(
  (state) => state.isAppInBackground,
  (isBackground) => {
    if (!isBackground) {
      // App came to foreground, check if re-auth is needed
      const { checkReauthRequired, setRequiresReauth } = useSecurityStore.getState();
      if (checkReauthRequired()) {
        setRequiresReauth(true);
      }
    }
  }
);
