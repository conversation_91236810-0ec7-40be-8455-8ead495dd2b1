import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { useCurrentClass } from '@/hooks/useCurrentClass';
import AssignmentCard from './AssignmentCard';

interface Assignment {
  id: string;
  title: string;
  description: string;
  due_date: string;
  max_points: number;
  status: 'draft' | 'published' | 'closed';
  submissions_count?: number;
  graded_count?: number;
  created_at: string;
  gemini_generated: boolean;
}

const FILTER_OPTIONS = [
  { id: 'all', label: 'All', icon: 'list-outline' },
  { id: 'draft', label: 'Drafts', icon: 'document-outline' },
  { id: 'published', label: 'Published', icon: 'checkmark-circle-outline' },
  { id: 'closed', label: 'Closed', icon: 'lock-closed-outline' },
];

const SORT_OPTIONS = [
  { id: 'created_desc', label: 'Newest First' },
  { id: 'created_asc', label: 'Oldest First' },
  { id: 'due_date_asc', label: 'Due Date (Soon)' },
  { id: 'due_date_desc', label: 'Due Date (Later)' },
  { id: 'title_asc', label: 'Title A-Z' },
];

export default function AssignmentDashboard() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { currentClassId, loading: classLoading } = useCurrentClass();
  
  const {
    assignments,
    loading,
    error,
    fetchAssignments,
    deleteAssignment,
    publishAssignment,
    closeAssignment,
    setSelectedAssignment,
  } = useAssignmentStore();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('created_desc');
  const [selectedAssignments, setSelectedAssignments] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (currentClassId) {
        fetchAssignments(currentClassId);
      }
    }, [currentClassId, fetchAssignments])
  );

  const onRefresh = useCallback(async () => {
    if (currentClassId) {
      setRefreshing(true);
      await fetchAssignments(currentClassId);
      setRefreshing(false);
    }
  }, [currentClassId, fetchAssignments]);

  // Filter and sort assignments
  const filteredAndSortedAssignments = React.useMemo(() => {
    let filtered = assignments;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(assignment =>
        assignment.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        assignment.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(assignment => assignment.status === selectedFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (selectedSort) {
        case 'created_asc':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'created_desc':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'due_date_asc':
          return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
        case 'due_date_desc':
          return new Date(b.due_date).getTime() - new Date(a.due_date).getTime();
        case 'title_asc':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return filtered;
  }, [assignments, searchQuery, selectedFilter, selectedSort]);

  const handleAssignmentSelect = (assignmentId: string) => {
    setSelectedAssignments(prev => {
      const newSelection = prev.includes(assignmentId)
        ? prev.filter(id => id !== assignmentId)
        : [...prev, assignmentId];
      
      setShowBulkActions(newSelection.length > 0);
      return newSelection;
    });
  };

  const handleBulkAction = (action: string) => {
    if (selectedAssignments.length === 0) return;

    const actionText = action === 'publish' ? 'publish' : action === 'close' ? 'close' : 'delete';
    
    Alert.alert(
      `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Assignments`,
      `Are you sure you want to ${actionText} ${selectedAssignments.length} assignment(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: actionText.charAt(0).toUpperCase() + actionText.slice(1),
          style: action === 'delete' ? 'destructive' : 'default',
          onPress: async () => {
            try {
              for (const assignmentId of selectedAssignments) {
                switch (action) {
                  case 'publish':
                    await publishAssignment(assignmentId);
                    break;
                  case 'close':
                    await closeAssignment(assignmentId);
                    break;
                  case 'delete':
                    await deleteAssignment(assignmentId);
                    break;
                }
              }
              setSelectedAssignments([]);
              setShowBulkActions(false);
              if (currentClassId) {
                fetchAssignments(currentClassId);
              }
            } catch (error) {
              Alert.alert('Error', `Failed to ${actionText} assignments`);
            }
          },
        },
      ]
    );
  };

  const renderFilterButton = (filter: typeof FILTER_OPTIONS[0]) => (
    <TouchableOpacity
      key={filter.id}
      onPress={() => setSelectedFilter(filter.id)}
      className={`flex-row items-center px-4 py-2 rounded-full mr-3 ${
        selectedFilter === filter.id
          ? 'bg-primary-500'
          : isDark
          ? 'bg-dark-card'
          : 'bg-light-card'
      }`}
    >
      <Ionicons
        name={filter.icon as any}
        size={16}
        color={selectedFilter === filter.id ? 'white' : isDark ? '#9CA3AF' : '#6B7280'}
      />
      <Text className={`ml-2 font-rubik-medium ${
        selectedFilter === filter.id
          ? 'text-white'
          : isDark ? 'text-dark-text' : 'text-light-text'
      }`}>
        {filter.label}
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View className="space-y-4 mb-4">
      {/* Search Bar */}
      <View className={`flex-row items-center px-4 py-3 rounded-xl ${
        isDark ? 'bg-dark-card' : 'bg-light-card'
      }`}>
        <Ionicons name="search-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
        <TextInput
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search assignments..."
          placeholderTextColor={isDark ? '#666' : '#999'}
          className={`flex-1 ml-3 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Buttons */}
      <FlatList
        data={FILTER_OPTIONS}
        renderItem={({ item }) => renderFilterButton(item)}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      />

      {/* Sort and Stats */}
      <View className="flex-row items-center justify-between px-4">
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              'Sort By',
              'Choose sorting option',
              SORT_OPTIONS.map(option => ({
                text: option.label,
                onPress: () => setSelectedSort(option.id),
              }))
            );
          }}
          className={`flex-row items-center px-3 py-2 rounded-lg ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          }`}
        >
          <Ionicons name="funnel-outline" size={16} color={isDark ? '#9CA3AF' : '#6B7280'} />
          <Text className={`ml-2 font-rubik text-sm ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Sort
          </Text>
        </TouchableOpacity>

        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          {filteredAndSortedAssignments.length} assignment{filteredAndSortedAssignments.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderAssignment = ({ item, index }: { item: Assignment; index: number }) => (
    <Animated.View entering={FadeInDown.delay(index * 100).duration(400)}>
      <AssignmentCard
        assignment={item}
        isSelected={selectedAssignments.includes(item.id)}
        onSelect={() => handleAssignmentSelect(item.id)}
        onPress={() => {
          setSelectedAssignment(item);
          router.push(`/reports/assignment/${item.id}`);
        }}
        showSelection={showBulkActions}
      />
    </Animated.View>
  );

  if (classLoading) {
    return (
      <View className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <Text className={`${isDark ? 'text-dark-text' : 'text-light-text'}`}>Loading class...</Text>
      </View>
    );
  }

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Assignments
        </Text>
        
        <TouchableOpacity
          onPress={() => router.push('/reports/assignment/create-enhanced')}
          className="bg-primary-500 px-4 py-2 rounded-lg"
        >
          <Text className="text-white font-rubik-medium">Create</Text>
        </TouchableOpacity>
      </View>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <Animated.View
          entering={FadeInDown.duration(300)}
          className={`flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 ${
            isDark ? 'bg-dark-card' : 'bg-light-card'
          }`}
        >
          <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {selectedAssignments.length} selected
          </Text>
          
          <View className="flex-row space-x-2">
            <TouchableOpacity
              onPress={() => handleBulkAction('publish')}
              className="bg-green-500 px-3 py-1 rounded"
            >
              <Text className="text-white font-rubik-medium text-sm">Publish</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={() => handleBulkAction('close')}
              className="bg-orange-500 px-3 py-1 rounded"
            >
              <Text className="text-white font-rubik-medium text-sm">Close</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={() => handleBulkAction('delete')}
              className="bg-red-500 px-3 py-1 rounded"
            >
              <Text className="text-white font-rubik-medium text-sm">Delete</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={() => {
                setSelectedAssignments([]);
                setShowBulkActions(false);
              }}
              className="bg-gray-500 px-3 py-1 rounded"
            >
              <Text className="text-white font-rubik-medium text-sm">Cancel</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Assignment List */}
      <FlatList
        data={filteredAndSortedAssignments}
        renderItem={renderAssignment}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={{ padding: 16, paddingTop: 0 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#2196F3']}
            tintColor="#2196F3"
          />
        }
        ListEmptyComponent={
          <View className="items-center py-12">
            <Ionicons
              name="document-text-outline"
              size={64}
              color={isDark ? '#4B5563' : '#9CA3AF'}
            />
            <Text className={`text-lg font-rubik-medium mt-4 ${
              isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
            }`}>
              No assignments found
            </Text>
            <Text className={`text-center mt-2 ${
              isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
            }`}>
              {searchQuery || selectedFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first assignment to get started'
              }
            </Text>
            {(!searchQuery && selectedFilter === 'all') && (
              <TouchableOpacity
                onPress={() => router.push('/reports/assignment/create-enhanced')}
                className="mt-4 bg-primary-500 px-6 py-3 rounded-lg"
              >
                <Text className="text-white font-rubik-medium">Create Assignment</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />
    </View>
  );
}
