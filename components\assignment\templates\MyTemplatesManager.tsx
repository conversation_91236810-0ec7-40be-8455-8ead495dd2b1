import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Share,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface AssignmentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  subject: string;
  gradeLevel: string;
  isPublic: boolean;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  templateData: any;
}

interface MyTemplatesManagerProps {
  teacherId: string;
  onEditTemplate?: (template: AssignmentTemplate) => void;
  onUseTemplate?: (template: AssignmentTemplate) => void;
}

export default function MyTemplatesManager({
  teacherId,
  onEditTemplate,
  onUseTemplate,
}: MyTemplatesManagerProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const [templates, setTemplates] = useState<AssignmentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchMyTemplates();
  }, [teacherId]);

  const fetchMyTemplates = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockTemplates: AssignmentTemplate[] = [
        {
          id: '1',
          name: 'My Research Paper Template',
          description: 'Custom research paper template for my English classes',
          category: 'research',
          subject: 'english',
          gradeLevel: 'High School',
          isPublic: true,
          usageCount: 12,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-20T14:30:00Z',
          tags: ['research', 'writing', 'mla'],
          templateData: {
            title: 'Research Paper Assignment',
            description: 'Write a comprehensive research paper',
            instructions: 'Detailed instructions...',
            maxPoints: 100,
          },
        },
        {
          id: '2',
          name: 'Quick Math Homework',
          description: 'Simple template for daily math assignments',
          category: 'homework',
          subject: 'math',
          gradeLevel: 'High School',
          isPublic: false,
          usageCount: 25,
          createdAt: '2024-01-10T09:00:00Z',
          updatedAt: '2024-01-10T09:00:00Z',
          tags: ['math', 'homework', 'daily'],
          templateData: {
            title: 'Math Homework',
            description: 'Daily math practice',
            instructions: 'Complete the assigned problems...',
            maxPoints: 20,
          },
        },
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchMyTemplates();
    setRefreshing(false);
  };

  const handleDeleteTemplate = (template: AssignmentTemplate) => {
    Alert.alert(
      'Delete Template',
      `Are you sure you want to delete "${template.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Call API to delete template
              setTemplates(prev => prev.filter(t => t.id !== template.id));
              Alert.alert('Success', 'Template deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete template');
            }
          },
        },
      ]
    );
  };

  const handleTogglePublic = async (template: AssignmentTemplate) => {
    try {
      const newPublicStatus = !template.isPublic;
      // TODO: Call API to update template
      setTemplates(prev =>
        prev.map(t =>
          t.id === template.id ? { ...t, isPublic: newPublicStatus } : t
        )
      );
      
      Alert.alert(
        'Success',
        `Template is now ${newPublicStatus ? 'public' : 'private'}`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update template');
    }
  };

  const handleShareTemplate = async (template: AssignmentTemplate) => {
    try {
      const shareContent = {
        message: `Check out this assignment template: "${template.name}" - ${template.description}`,
        title: template.name,
      };
      
      await Share.share(shareContent);
    } catch (error) {
      console.error('Error sharing template:', error);
    }
  };

  const handleDuplicateTemplate = async (template: AssignmentTemplate) => {
    try {
      const duplicatedTemplate = {
        ...template,
        id: Date.now().toString(),
        name: `${template.name} (Copy)`,
        usageCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // TODO: Call API to create duplicate
      setTemplates(prev => [duplicatedTemplate, ...prev]);
      Alert.alert('Success', 'Template duplicated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to duplicate template');
    }
  };

  const renderTemplate = (template: AssignmentTemplate, index: number) => (
    <Animated.View
      key={template.id}
      entering={FadeInDown.delay(index * 100).duration(400)}
    >
      <View className={`p-4 mb-3 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <View className="flex-row items-start justify-between mb-3">
          <View className="flex-1 mr-3">
            <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {template.name}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Created {new Date(template.createdAt).toLocaleDateString()}
            </Text>
          </View>
          
          <View className="items-end">
            <View className="flex-row items-center mb-1">
              <Ionicons name="people-outline" size={14} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-1 font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {template.usageCount} uses
              </Text>
            </View>
            <View className={`px-2 py-1 rounded ${
              template.isPublic 
                ? 'bg-green-100 dark:bg-green-900/30' 
                : 'bg-gray-100 dark:bg-gray-800'
            }`}>
              <Text className={`text-xs font-rubik-medium ${
                template.isPublic
                  ? 'text-green-600 dark:text-green-300'
                  : isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'
              }`}>
                {template.isPublic ? 'Public' : 'Private'}
              </Text>
            </View>
          </View>
        </View>

        <Text className={`font-rubik mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {template.description}
        </Text>

        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center space-x-2">
            <View className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded">
              <Text className="text-blue-600 dark:text-blue-300 text-xs font-rubik-medium">
                {template.category}
              </Text>
            </View>
            <View className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 rounded">
              <Text className="text-purple-600 dark:text-purple-300 text-xs font-rubik-medium">
                {template.subject}
              </Text>
            </View>
          </View>
          
          <Text className={`font-rubik-bold text-primary-500`}>
            {template.templateData.maxPoints} pts
          </Text>
        </View>

        {/* Action Buttons */}
        <View className="flex-row items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
          <View className="flex-row space-x-2">
            <TouchableOpacity
              onPress={() => onUseTemplate?.(template)}
              className="flex-row items-center px-3 py-2 bg-primary-500 rounded-lg"
            >
              <Ionicons name="add-outline" size={16} color="white" />
              <Text className="text-white font-rubik-medium ml-1 text-sm">
                Use
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => onEditTemplate?.(template)}
              className={`flex-row items-center px-3 py-2 rounded-lg ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}
            >
              <Ionicons name="pencil-outline" size={16} color={isDark ? '#FFFFFF' : '#000000'} />
              <Text className={`font-rubik-medium ml-1 text-sm ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Edit
              </Text>
            </TouchableOpacity>
          </View>

          <View className="flex-row space-x-1">
            <TouchableOpacity
              onPress={() => handleDuplicateTemplate(template)}
              className="p-2"
            >
              <Ionicons name="copy-outline" size={18} color={isDark ? '#9CA3AF' : '#6B7280'} />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleShareTemplate(template)}
              className="p-2"
            >
              <Ionicons name="share-outline" size={18} color={isDark ? '#9CA3AF' : '#6B7280'} />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleTogglePublic(template)}
              className="p-2"
            >
              <Ionicons 
                name={template.isPublic ? 'eye-off-outline' : 'eye-outline'} 
                size={18} 
                color={isDark ? '#9CA3AF' : '#6B7280'} 
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleDeleteTemplate(template)}
              className="p-2"
            >
              <Ionicons name="trash-outline" size={18} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Animated.View>
  );

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="p-4 border-b border-gray-200 dark:border-gray-700">
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          My Templates
        </Text>
        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          Manage your assignment templates
        </Text>
      </View>

      {/* Templates List */}
      <ScrollView
        className="flex-1 p-4"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {templates.length > 0 ? (
          templates.map((template, index) => renderTemplate(template, index))
        ) : (
          <View className="items-center justify-center py-12">
            <Ionicons
              name="document-outline"
              size={48}
              color={isDark ? '#666' : '#999'}
            />
            <Text className={`mt-4 font-rubik text-lg ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              No templates yet
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Create your first template from an assignment
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
