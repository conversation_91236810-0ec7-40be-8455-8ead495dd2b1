import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, ScrollView, Text, TouchableOpacity, View } from 'react-native';

interface SubmissionItem {
  id: string;
  student_name: string;
  assignment_title: string;
  submitted_at: string;
  status: 'pending' | 'graded';
  type: 'assignment' | 'mock_test';
}

export default function GradeSubmissions() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [loading, setLoading] = useState(true);
  const [submissions, setSubmissions] = useState<SubmissionItem[]>([]);

  // TODO: Implement fetching submissions from the backend
  useEffect(() => {
    // Simulated data for now
    setSubmissions([
      {
        id: '1',
        student_name: '<PERSON>',
        assignment_title: 'Math Assignment 1',
        submitted_at: '2024-06-10T10:00:00Z',
        status: 'pending',
        type: 'assignment'
      },
      {
        id: '2',
        student_name: '<PERSON>',
        assignment_title: 'Science Mock Test',
        submitted_at: '2024-06-09T15:30:00Z',
        status: 'pending',
        type: 'mock_test'
      }
    ]);
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  return (
    <ScrollView 
      className={`flex-1 px-4 py-6 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}
    >
      <View className="space-y-4">
        {submissions.map((submission) => (
          <TouchableOpacity
            key={submission.id}
            className={`
              p-4 rounded-lg
              ${isDark ? 'bg-dark-card' : 'bg-light-card'}
            `}
            onPress={() => {
              // TODO: Navigate to detailed grading view
              console.log('Navigate to grade submission:', submission.id);
            }}
          >
            <View className="flex-row justify-between items-start">
              <View>
                <Text className={`font-rubik-medium text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  {submission.student_name}
                </Text>
                <Text className={`font-rubik-regular text-sm mt-1 ${isDark ? 'text-dark-secondary' : 'text-light-secondary'}`}>
                  {submission.assignment_title}
                </Text>
              </View>
              <View className={`
                px-2 py-1 rounded-full
                ${submission.status === 'pending' ? 'bg-amber-500' : 'bg-green-500'}
              `}>
                <Text className="text-white text-xs font-rubik-medium">
                  {submission.status.toUpperCase()}
                </Text>
              </View>
            </View>

            <View className="flex-row justify-between mt-4">
              <Text className={`text-xs ${isDark ? 'text-dark-secondary' : 'text-light-secondary'}`}>
                {new Date(submission.submitted_at).toLocaleDateString()}
              </Text>
              <Text className={`text-xs font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {submission.type === 'assignment' ? '📝 Assignment' : '📋 Mock Test'}
              </Text>
            </View>
          </TouchableOpacity>
        ))}

        {submissions.length === 0 && (
          <View className="py-8 items-center">
            <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              No pending submissions
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
}
