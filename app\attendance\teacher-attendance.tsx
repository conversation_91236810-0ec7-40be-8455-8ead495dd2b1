import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Stores
import { useAttendanceStore, type TeacherAttendance } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

// Location service
import { getDeviceLocationInfo, verifyAttendanceLocation } from '@/lib/locationService';

const TeacherAttendanceScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { userId: clerkUserId } = useAuth();

  const {
    teacherAttendance,
    isLoading,
    isSaving,
    error,
    markTeacherAttendance,
    clearError
  } = useAttendanceStore();

  const { currentTeacher, loadTeacherData } = useEnrollmentStore();

  const [todayAttendance, setTodayAttendance] = useState<TeacherAttendance | null>(null);
  const [showCamera, setShowCamera] = useState(false);

  const today = new Date().toISOString().split('T')[0];

  // Load teacher data
  useEffect(() => {
    if (clerkUserId && !currentTeacher) {
      loadTeacherData(clerkUserId);
    }
  }, [clerkUserId, currentTeacher, loadTeacherData]);

  // Check today's attendance
  useEffect(() => {
    if (teacherAttendance.length > 0 && currentTeacher) {
      const todayRecord = teacherAttendance.find(
        record => record.teacher_id === currentTeacher.id && record.attendance_date === today
      );
      setTodayAttendance(todayRecord || null);
    }
  }, [teacherAttendance, currentTeacher, today]);

  const handleCheckIn = async () => {
    if (!currentTeacher) {
      Alert.alert('Error', 'Teacher information not found');
      return;
    }

    Alert.alert(
      'Check In',
      'This will verify your location and mark your attendance. Make sure you are within school premises.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Verify & Check In',
          onPress: async () => {
            try {
              // Show loading state
              Alert.alert('Verifying Location', 'Please wait while we verify your location...');

              // Verify location first
              const locationVerification = await verifyAttendanceLocation();

              if (!locationVerification.success) {
                Alert.alert(
                  'Location Verification Failed',
                  locationVerification.message,
                  [{ text: 'OK' }]
                );
                return;
              }

              // Get detailed device location info
              const deviceLocationInfo = await getDeviceLocationInfo();

              const now = new Date();
              const attendanceData = {
                teacher_id: currentTeacher.id,
                attendance_date: today,
                check_in_time: now.toISOString(),
                status: 'present' as const,
                location_lat: locationVerification.location?.latitude,
                location_lng: locationVerification.location?.longitude,
                device_info: {
                  accuracy: locationVerification.location?.accuracy,
                  timestamp: locationVerification.location?.timestamp,
                  address: deviceLocationInfo?.address,
                  distance_from_school: locationVerification.distance,
                  verification_method: 'gps_location'
                },
                facial_recognition_confidence: 95.5, // Simulated confidence
                notes: `Location verified: ${locationVerification.message}`,
              };

              const result = await markTeacherAttendance(attendanceData);
              if (result) {
                setTodayAttendance(result);
                Alert.alert(
                  'Check-in Successful!',
                  `You have been checked in successfully!\n\nLocation: ${locationVerification.message}\nTime: ${now.toLocaleTimeString()}`,
                  [{ text: 'OK' }]
                );
              }
            } catch (error) {
              console.error('Check-in error:', error);
              Alert.alert('Error', 'Failed to check in. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleCheckOut = async () => {
    if (!currentTeacher || !todayAttendance) {
      Alert.alert('Error', 'No check-in record found for today');
      return;
    }

    Alert.alert(
      'Check Out',
      'This will verify your location and mark your check-out time.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Verify & Check Out',
          onPress: async () => {
            try {
              // Show loading state
              Alert.alert('Verifying Location', 'Please wait while we verify your location...');

              // Verify location for check-out
              const locationVerification = await verifyAttendanceLocation();

              if (!locationVerification.success) {
                Alert.alert(
                  'Location Verification Failed',
                  `${locationVerification.message}\n\nNote: You should check out from school premises for accurate records.`,
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Check Out Anyway',
                      style: 'destructive',
                      onPress: () => performCheckOut(null, 'Location verification failed')
                    }
                  ]
                );
                return;
              }

              // Perform check-out with location verification
              await performCheckOut(locationVerification, 'Location verified');

            } catch (error) {
              console.error('Check-out error:', error);
              Alert.alert('Error', 'Failed to check out. Please try again.');
            }
          }
        }
      ]
    );
  };

  const performCheckOut = async (locationVerification: any, locationNote: string) => {
    try {
      const now = new Date();
      const deviceLocationInfo = await getDeviceLocationInfo();

      // Calculate work duration
      const checkInTime = new Date(todayAttendance!.check_in_time!);
      const workDuration = Math.round((now.getTime() - checkInTime.getTime()) / (1000 * 60 * 60 * 100)) / 100; // Hours

      const updatedData = {
        ...todayAttendance!,
        check_out_time: now.toISOString(),
        location_lat: locationVerification?.location?.latitude || todayAttendance!.location_lat,
        location_lng: locationVerification?.location?.longitude || todayAttendance!.location_lng,
        device_info: {
          ...todayAttendance!.device_info,
          checkout_accuracy: locationVerification?.location?.accuracy,
          checkout_timestamp: locationVerification?.location?.timestamp,
          checkout_address: deviceLocationInfo?.address,
          checkout_distance_from_school: locationVerification?.distance,
          work_duration_hours: workDuration
        },
        notes: `${todayAttendance!.notes || ''} | Check-out: ${locationNote} | Duration: ${workDuration}h`,
      };

      const result = await markTeacherAttendance(updatedData);
      if (result) {
        setTodayAttendance(result);
        Alert.alert(
          'Check-out Successful!',
          `You have been checked out successfully!\n\nWork Duration: ${workDuration} hours\nTime: ${now.toLocaleTimeString()}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Check-out error:', error);
      Alert.alert('Error', 'Failed to check out. Please try again.');
    }
  };

  const handleFacialRecognition = () => {
    // Placeholder for facial recognition
    Alert.alert(
      'Facial Recognition',
      'Facial recognition feature will be implemented in the next update. This will use the device camera to verify your identity before marking attendance.',
      [
        { text: 'OK' },
        { text: 'Use Manual Check-in', onPress: handleCheckIn }
      ]
    );
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return 'Not recorded';
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'text-success';
      case 'absent': return 'text-error';
      case 'late': return 'text-warning';
      case 'half_day': return 'text-info';
      default: return isDark ? 'text-dark-text' : 'text-light-text';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present': return 'checkmark.circle.fill';
      case 'absent': return 'xmark.circle.fill';
      case 'late': return 'clock.fill';
      case 'half_day': return 'clock.badge.questionmark';
      default: return 'questionmark.circle';
    }
  };

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen message="Loading teacher attendance..." />;
  }

  // Show error screen
  if (error) {
    return (
      <ErrorScreen
        title="Attendance Error"
        message={error}
        onRetry={() => {
          clearError();
          if (clerkUserId) {
            loadTeacherData(clerkUserId);
          }
        }}
      />
    );
  }

  if (!currentTeacher) {
    return (
      <ErrorScreen
        title="Teacher Not Found"
        message="Unable to load teacher information."
        onRetry={() => router.back()}
      />
    );
  }

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <View className="flex-row items-center justify-between mb-2">
            <TouchableOpacity onPress={() => router.back()}>
              <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
            <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Teacher Attendance
            </Text>
            <View className="w-6" />
          </View>

          <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {currentTeacher.name}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
        </View>

        <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
          {/* Today's Status */}
          <View className={`p-6 rounded-lg mb-6 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-xl mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Today's Status
            </Text>

            {todayAttendance ? (
              <View>
                <View className="flex-row items-center mb-4">
                  <IconSymbol
                    name={getStatusIcon(todayAttendance.status)}
                    size={24}
                    color={getStatusColor(todayAttendance.status).includes('success') ? '#10B981' :
                           getStatusColor(todayAttendance.status).includes('error') ? '#EF4444' :
                           getStatusColor(todayAttendance.status).includes('warning') ? '#F59E0B' : '#3B82F6'}
                  />
                  <Text className={`font-rubik-semibold text-lg ml-3 capitalize ${getStatusColor(todayAttendance.status)}`}>
                    {todayAttendance.status}
                  </Text>
                </View>

                <View className="space-y-3">
                  <View className="flex-row justify-between">
                    <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                      Check-in Time:
                    </Text>
                    <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {formatTime(todayAttendance.check_in_time)}
                    </Text>
                  </View>

                  <View className="flex-row justify-between">
                    <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                      Check-out Time:
                    </Text>
                    <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      {formatTime(todayAttendance.check_out_time)}
                    </Text>
                  </View>

                  {todayAttendance.facial_recognition_confidence && (
                    <View className="flex-row justify-between">
                      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        Verification:
                      </Text>
                      <Text className={`font-rubik-semibold text-success`}>
                        {todayAttendance.facial_recognition_confidence}% confidence
                      </Text>
                    </View>
                  )}

                  {(todayAttendance.location_lat && todayAttendance.location_lng) && (
                    <View className="flex-row justify-between">
                      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        Location:
                      </Text>
                      <Text className={`font-rubik-semibold text-info`}>
                        Verified ✓
                      </Text>
                    </View>
                  )}

                  {todayAttendance.device_info?.distance_from_school && (
                    <View className="flex-row justify-between">
                      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        Distance:
                      </Text>
                      <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                        {todayAttendance.device_info.distance_from_school}m from school
                      </Text>
                    </View>
                  )}

                  {todayAttendance.device_info?.work_duration_hours && (
                    <View className="flex-row justify-between">
                      <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                        Duration:
                      </Text>
                      <Text className={`font-rubik-semibold text-primary-500`}>
                        {todayAttendance.device_info.work_duration_hours} hours
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            ) : (
              <View className="items-center py-8">
                <IconSymbol
                  name="clock.badge.questionmark"
                  size={48}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
                <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  No attendance recorded for today
                </Text>
                <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  Use the buttons below to check in
                </Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View className="space-y-6">
            {!todayAttendance ? (
              <>
                {/* Facial Recognition Check-in */}
                <TouchableOpacity
                  onPress={handleFacialRecognition}
                  className="bg-primary-500 p-4 rounded-lg flex-row items-center justify-center"
                >
                  <IconSymbol name="camera.fill" size={24} color="#FFFFFF" />
                  <Text className="text-white font-rubik-semibold text-base ml-3">
                    Check In with Facial Recognition
                  </Text>
                </TouchableOpacity>

                {/* Separator with "OR" text */}
                <View className="flex-row items-center">
                  <View className={`flex-1 h-px ${isDark ? 'bg-gray-600' : 'bg-gray-300'}`} />
                  <Text className={`mx-4 font-rubik-medium text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    OR
                  </Text>
                  <View className={`flex-1 h-px ${isDark ? 'bg-gray-600' : 'bg-gray-300'}`} />
                </View>

                {/* Manual Check-in */}
                <TouchableOpacity
                  onPress={handleCheckIn}
                  disabled={isSaving}
                  className={`p-4 rounded-lg flex-row items-center justify-center border-2 ${
                    isSaving ? 'bg-gray-400 border-gray-400' : 'bg-transparent border-primary-500'
                  }`}
                >
                  <IconSymbol
                    name="person.badge.plus"
                    size={24}
                    color={isSaving ? '#FFFFFF' : '#3B82F6'}
                  />
                  <Text className={`font-rubik-semibold text-base ml-3 ${
                    isSaving ? 'text-white' : 'text-primary-500'
                  }`}>
                    {isSaving ? 'Checking In...' : 'Manual Check In'}
                  </Text>
                </TouchableOpacity>
              </>
            ) : !todayAttendance.check_out_time ? (
              <TouchableOpacity
                onPress={handleCheckOut}
                disabled={isSaving}
                className={`p-4 rounded-lg flex-row items-center justify-center ${
                  isSaving ? 'bg-gray-400' : 'bg-success'
                }`}
              >
                <IconSymbol name="clock.arrow.circlepath" size={24} color="#FFFFFF" />
                <Text className="text-white font-rubik-semibold text-base ml-3">
                  {isSaving ? 'Checking Out...' : 'Check Out'}
                </Text>
              </TouchableOpacity>
            ) : (
              <View className={`p-4 rounded-lg border-2 border-dashed ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
                <Text className={`text-center font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  You have completed your attendance for today
                </Text>
              </View>
            )}
          </View>

          {/* Info Section */}
          <View className={`mt-6 p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <View className="flex-row items-center mb-3">
              <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                🛡️
              </Text>
              <Text className={`font-rubik-bold ml-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Security Features
              </Text>
            </View>
            <View className="space-y-2">
              <View className="flex-row">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  📍
                </Text>
                <Text className={`font-rubik text-sm ml-1 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  <Text className="font-rubik-semibold">Location Verification:</Text> Your GPS location is verified to ensure you're within school premises
                </Text>
              </View>

              <View className="flex-row">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  📱
                </Text>
                <Text className={`font-rubik text-sm ml-1 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  <Text className="font-rubik-semibold">Device Tracking:</Text> Device information is recorded for security purposes
                </Text>
              </View>

              <View className="flex-row">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  ⏰
                </Text>
                <Text className={`font-rubik text-sm ml-1 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  <Text className="font-rubik-semibold">Timestamp Accuracy:</Text> Precise check-in/check-out times are recorded
                </Text>
              </View>

              <View className="flex-row">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  📊
                </Text>
                <Text className={`font-rubik text-sm ml-1 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  <Text className="font-rubik-semibold">Work Duration:</Text> Automatic calculation of daily work hours
                </Text>
              </View>

              <View className="flex-row">
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  🔒
                </Text>
                <Text className={`font-rubik text-sm ml-1 ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  <Text className="font-rubik-semibold">Anti-Fraud:</Text> Prevents attendance marking from home or unauthorized locations
                </Text>
              </View>
            </View>
          </View>

          {/* Location Requirements */}
          <View className={`mt-4 p-4 rounded-lg border-2 border-dashed ${isDark ? 'border-warning bg-warning/10' : 'border-warning bg-warning/10'}`}>
            <View className="flex-row items-center mb-2">
              <Text className="font-rubik-bold text-warning">
                📍
              </Text>
              <Text className="font-rubik-bold ml-2 text-warning">
                Location Requirements
              </Text>
            </View>
            <Text className="font-rubik text-sm text-warning">
              You must be within 100 meters of the school campus to mark attendance. Make sure your GPS is enabled and you have a good signal.
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default TeacherAttendanceScreen;
