import { supabase } from '@/lib/supabase';
import { generateInstructionImprovements } from '@/lib/gemini';

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id: assignmentId } = params;

    // Fetch assignment details
    const { data: assignment, error: assignmentError } = await supabase
      .from('assignments')
      .select('*')
      .eq('id', assignmentId)
      .single();

    if (assignmentError || !assignment) {
      return Response.json({ error: 'Assignment not found' }, { status: 404 });
    }

    // Generate improved instructions
    const improvedInstructions = await generateInstructionImprovements(
      assignment.instructions || '',
      assignment.title,
      'General', // You might want to add subject field to assignments
      'High School' // You might want to add grade level field
    );

    return Response.json({
      success: true,
      improvedInstructions,
      originalInstructions: assignment.instructions,
    });

  } catch (error) {
    console.error('Error generating instruction improvements:', error);
    return Response.json(
      { error: 'Failed to generate instruction improvements' },
      { status: 500 }
    );
  }
}
