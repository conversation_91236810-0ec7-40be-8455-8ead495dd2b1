# Assignment System Testing Guide

## Overview
This guide provides comprehensive testing instructions for the complete assignment management system. The system includes authentication, CRUD operations, AI features, analytics, notifications, templates, and export functionality.

## Testing Tools Available

### 1. Debug Tab in App
The app includes a dedicated Debug tab with three testing interfaces:

#### **Auth Tab**
- **Purpose**: Verify authentication and user setup
- **Features**:
  - Check Clerk authentication status
  - Verify Supabase session
  - Test user and teacher record existence
  - Create missing database records automatically
  - Test Clerk-Supabase integration

#### **Debugger Tab**
- **Purpose**: Quick testing and debugging
- **Features**:
  - Run individual test functions
  - Create test data quickly
  - Debug specific issues
  - Test basic functionality

#### **Comprehensive Tab**
- **Purpose**: Full system testing
- **Features**:
  - Complete end-to-end testing
  - All feature validation
  - Performance testing
  - Automated test suites

## Testing Workflow

### Phase 1: Authentication Setup
1. **Open the Debug tab** in your app
2. **Go to Auth tab**
3. **Click "Check Authentication Status"**
4. **Verify all checks pass**:
   - ✅ Clerk authentication
   - ✅ Supabase session
   - ✅ User record exists
   - ✅ Teacher record exists
5. **If any fail**, click "Create Missing Records"

### Phase 2: Quick Functionality Test
1. **Go to Debugger tab**
2. **Run individual tests**:
   - Debug User & Teacher
   - Create Test Class
   - Create Test Assignment
   - Test Assignment Creation
   - Test AI Grading (if API key configured)

### Phase 3: Comprehensive System Test
1. **Go to Comprehensive tab**
2. **Click "Run All Tests"**
3. **Monitor test progress** through 9 test suites:
   - Database and Authentication
   - Class Management
   - Assignment CRUD Operations
   - Student and Submission Management
   - Rubrics System
   - AI Features
   - Export Functionality
   - Bulk Operations
   - Data Cleanup

## Test Coverage

### Core Features Tested
- ✅ **Authentication**: Clerk + Supabase integration
- ✅ **Database Operations**: CRUD for all entities
- ✅ **Assignment Management**: Create, update, publish, close, delete
- ✅ **Student Management**: Create students and class enrollment
- ✅ **Submission System**: Create and manage submissions
- ✅ **Grading System**: Manual grading with feedback
- ✅ **Rubrics**: Create and use rubric-based grading
- ✅ **AI Features**: Automated feedback generation
- ✅ **Export System**: CSV and JSON exports
- ✅ **Bulk Operations**: Mass assignment operations
- ✅ **Data Cleanup**: Proper cleanup of test data

### Integration Points Tested
- ✅ **Clerk ↔ Supabase**: Authentication flow
- ✅ **Store ↔ Database**: State management
- ✅ **UI ↔ Store**: Component interactions
- ✅ **AI ↔ Database**: Gemini API integration
- ✅ **Export ↔ File System**: File generation and sharing

## Expected Test Results

### Successful Test Run Should Show:
- **9 Test Suites**: All passing (green checkmarks)
- **40+ Individual Tests**: All passing
- **Performance**: Tests complete in under 30 seconds
- **Cleanup**: All test data removed automatically

### Common Issues and Solutions

#### Authentication Issues
- **Problem**: "No active session"
- **Solution**: Ensure you're logged in with Clerk
- **Fix**: Use Auth tab to create missing records

#### Database Issues
- **Problem**: "Teacher not found"
- **Solution**: Run "Create Missing Records" in Auth tab
- **Fix**: Verify database schema is up to date

#### AI Features Issues
- **Problem**: "API key not configured"
- **Solution**: Add EXPO_PUBLIC_GEMINI_API_KEY to .env
- **Fix**: AI tests will skip if no API key (expected behavior)

#### Export Issues
- **Problem**: "File sharing not available"
- **Solution**: Test on physical device, not simulator
- **Fix**: Files are saved to device storage

## Manual Testing Checklist

### Assignment Creation
- [ ] Create new assignment
- [ ] Set all required fields
- [ ] Add rubrics
- [ ] Publish assignment
- [ ] Verify students can see it

### Submission Process
- [ ] Student submits assignment
- [ ] Teacher receives notification
- [ ] Submission appears in teacher dashboard
- [ ] Content is properly saved

### Grading Workflow
- [ ] Teacher opens submission
- [ ] Provides grade and feedback
- [ ] Uses rubric for grading
- [ ] Student receives notification
- [ ] Grade appears in analytics

### AI Features
- [ ] Generate AI feedback
- [ ] Improve assignment instructions
- [ ] Auto-grade submissions
- [ ] Review AI suggestions

### Export Features
- [ ] Export single assignment
- [ ] Export multiple assignments
- [ ] Export class summary
- [ ] Verify CSV format
- [ ] Verify JSON format

### Bulk Operations
- [ ] Select multiple assignments
- [ ] Bulk publish
- [ ] Bulk close
- [ ] Bulk delete
- [ ] Bulk export

## Performance Benchmarks

### Expected Performance
- **Assignment Creation**: < 2 seconds
- **Submission Loading**: < 1 second
- **AI Feedback Generation**: < 10 seconds
- **Export Generation**: < 5 seconds
- **Bulk Operations**: < 3 seconds per item

### Memory Usage
- **Normal Operation**: < 100MB
- **During Export**: < 150MB
- **During AI Processing**: < 200MB

## Troubleshooting

### Debug Console Logs
Enable detailed logging by checking browser/device console:
```javascript
// Look for these log patterns:
"=== DEBUG: Checking User and Teacher Records ==="
"✅ Successfully found user and teacher"
"Test Report: # Assignment System Test Report"
```

### Database Verification
Check Supabase dashboard for:
- Users table has your record
- Teachers table has corresponding record
- Classes table has test classes
- Assignments table has test assignments

### File System Verification
Check device storage for exported files:
- iOS: Files app > On My iPhone > [App Name]
- Android: Downloads folder or app-specific folder

## Continuous Testing

### Automated Testing
The comprehensive test suite can be run regularly to ensure system stability:
- Run before major releases
- Run after database schema changes
- Run after adding new features

### Regression Testing
Use the test suite to verify:
- Existing features still work after updates
- New features don't break existing functionality
- Performance remains acceptable

## Support

### Getting Help
If tests fail consistently:
1. Check the console logs for detailed error messages
2. Verify your environment configuration
3. Ensure database schema is up to date
4. Check network connectivity
5. Verify API keys are properly configured

### Reporting Issues
When reporting test failures, include:
- Test suite name and specific test that failed
- Error message from console
- Device/platform information
- Steps to reproduce
- Expected vs actual behavior

## Conclusion

The assignment system testing framework provides comprehensive coverage of all features and integration points. Regular testing ensures system reliability and helps catch issues early in the development process.
