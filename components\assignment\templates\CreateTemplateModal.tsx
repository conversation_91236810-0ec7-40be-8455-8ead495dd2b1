import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  Modal,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface Assignment {
  id: string;
  title: string;
  description: string;
  instructions: string;
  max_points: number;
  rubrics?: any[];
  attachment_urls?: string[];
}

interface CreateTemplateModalProps {
  visible: boolean;
  onClose: () => void;
  assignment?: Assignment;
  onSave: (templateData: any) => void;
}

export default function CreateTemplateModal({
  visible,
  onClose,
  assignment,
  onSave,
}: CreateTemplateModalProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const [templateData, setTemplateData] = useState({
    name: assignment ? `${assignment.title} Template` : '',
    description: '',
    category: 'homework',
    subject: 'general',
    gradeLevel: 'High School',
    isPublic: false,
    tags: '',
  });

  const [loading, setLoading] = useState(false);

  const categories = [
    { id: 'essay', name: 'Essay' },
    { id: 'research', name: 'Research Project' },
    { id: 'lab', name: 'Lab Report' },
    { id: 'presentation', name: 'Presentation' },
    { id: 'quiz', name: 'Quiz' },
    { id: 'homework', name: 'Homework' },
    { id: 'project', name: 'Project' },
    { id: 'exam', name: 'Exam' },
  ];

  const subjects = [
    { id: 'general', name: 'General' },
    { id: 'math', name: 'Mathematics' },
    { id: 'science', name: 'Science' },
    { id: 'english', name: 'English' },
    { id: 'history', name: 'History' },
    { id: 'art', name: 'Art' },
    { id: 'music', name: 'Music' },
    { id: 'pe', name: 'Physical Education' },
    { id: 'computer', name: 'Computer Science' },
    { id: 'foreign', name: 'Foreign Language' },
  ];

  const gradeLevels = [
    'Elementary',
    'Middle School',
    'High School',
    'College',
  ];

  const handleSave = async () => {
    if (!templateData.name.trim()) {
      Alert.alert('Error', 'Please enter a template name');
      return;
    }

    if (!templateData.description.trim()) {
      Alert.alert('Error', 'Please enter a template description');
      return;
    }

    if (!assignment) {
      Alert.alert('Error', 'No assignment data available');
      return;
    }

    setLoading(true);
    try {
      const template = {
        name: templateData.name.trim(),
        description: templateData.description.trim(),
        category: templateData.category,
        subject: templateData.subject,
        gradeLevel: templateData.gradeLevel,
        isPublic: templateData.isPublic,
        tags: templateData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        templateData: {
          title: assignment.title,
          description: assignment.description,
          instructions: assignment.instructions,
          maxPoints: assignment.max_points,
          rubrics: assignment.rubrics || [],
          attachments: assignment.attachment_urls || [],
        },
      };

      await onSave(template);
      Alert.alert('Success', 'Template created successfully!');
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to create template');
    } finally {
      setLoading(false);
    }
  };

  const renderPickerSection = (
    title: string,
    value: string,
    options: { id: string; name: string }[],
    onSelect: (value: string) => void
  ) => (
    <View className="mb-4">
      <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        {title}
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} className="space-x-2">
        {options.map((option) => (
          <TouchableOpacity
            key={option.id}
            onPress={() => onSelect(option.id)}
            className={`px-4 py-2 rounded-lg mr-2 ${
              value === option.id
                ? 'bg-primary-500'
                : isDark
                ? 'bg-dark-background'
                : 'bg-light-background'
            }`}
          >
            <Text
              className={`font-rubik-medium text-sm ${
                value === option.id
                  ? 'text-white'
                  : isDark
                  ? 'text-dark-text'
                  : 'text-light-text'
              }`}
            >
              {option.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <TouchableOpacity onPress={onClose} className="p-2">
            <Ionicons name="close" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
          </TouchableOpacity>
          
          <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Create Template
          </Text>
          
          <TouchableOpacity
            onPress={handleSave}
            disabled={loading}
            className={`px-4 py-2 rounded-lg ${
              loading ? 'bg-gray-400' : 'bg-primary-500'
            }`}
          >
            <Text className="text-white font-rubik-medium">
              {loading ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
          <Animated.View entering={FadeInDown.duration(400)} className="space-y-6">
            {/* Template Name */}
            <View>
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Template Name *
              </Text>
              <TextInput
                value={templateData.name}
                onChangeText={(text) => setTemplateData(prev => ({ ...prev, name: text }))}
                placeholder="Enter template name"
                placeholderTextColor={isDark ? '#666' : '#999'}
                className={`p-4 rounded-xl border font-rubik ${
                  isDark ? 'bg-dark-card text-dark-text border-dark-border' : 'bg-light-card text-light-text border-light-border'
                }`}
              />
            </View>

            {/* Template Description */}
            <View>
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Description *
              </Text>
              <TextInput
                value={templateData.description}
                onChangeText={(text) => setTemplateData(prev => ({ ...prev, description: text }))}
                placeholder="Describe what this template is for"
                placeholderTextColor={isDark ? '#666' : '#999'}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
                className={`p-4 rounded-xl border font-rubik ${
                  isDark ? 'bg-dark-card text-dark-text border-dark-border' : 'bg-light-card text-light-text border-light-border'
                }`}
                style={{ minHeight: 80 }}
              />
            </View>

            {/* Category */}
            {renderPickerSection(
              'Category',
              templateData.category,
              categories,
              (value) => setTemplateData(prev => ({ ...prev, category: value }))
            )}

            {/* Subject */}
            {renderPickerSection(
              'Subject',
              templateData.subject,
              subjects,
              (value) => setTemplateData(prev => ({ ...prev, subject: value }))
            )}

            {/* Grade Level */}
            <View className="mb-4">
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Grade Level
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="space-x-2">
                {gradeLevels.map((level) => (
                  <TouchableOpacity
                    key={level}
                    onPress={() => setTemplateData(prev => ({ ...prev, gradeLevel: level }))}
                    className={`px-4 py-2 rounded-lg mr-2 ${
                      templateData.gradeLevel === level
                        ? 'bg-primary-500'
                        : isDark
                        ? 'bg-dark-background'
                        : 'bg-light-background'
                    }`}
                  >
                    <Text
                      className={`font-rubik-medium text-sm ${
                        templateData.gradeLevel === level
                          ? 'text-white'
                          : isDark
                          ? 'text-dark-text'
                          : 'text-light-text'
                      }`}
                    >
                      {level}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Tags */}
            <View>
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Tags
              </Text>
              <TextInput
                value={templateData.tags}
                onChangeText={(text) => setTemplateData(prev => ({ ...prev, tags: text }))}
                placeholder="Enter tags separated by commas (e.g., essay, research, writing)"
                placeholderTextColor={isDark ? '#666' : '#999'}
                className={`p-4 rounded-xl border font-rubik ${
                  isDark ? 'bg-dark-card text-dark-text border-dark-border' : 'bg-light-card text-light-text border-light-border'
                }`}
              />
              <Text className={`text-sm mt-1 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Tags help others find your template
              </Text>
            </View>

            {/* Public Template Toggle */}
            <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
              <View className="flex-row items-center justify-between">
                <View className="flex-1 mr-3">
                  <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                    Make Public
                  </Text>
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                    Allow other teachers to use this template
                  </Text>
                </View>
                <Switch
                  value={templateData.isPublic}
                  onValueChange={(value) => setTemplateData(prev => ({ ...prev, isPublic: value }))}
                  trackColor={{ false: "#767577", true: "#007AFF40" }}
                  thumbColor={templateData.isPublic ? "#007AFF" : "#f4f3f4"}
                />
              </View>
            </View>

            {/* Assignment Preview */}
            {assignment && (
              <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
                <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Assignment Preview
                </Text>
                
                <View className="space-y-2">
                  <View>
                    <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      Title: {assignment.title}
                    </Text>
                  </View>
                  
                  <View>
                    <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                      Max Points: {assignment.max_points}
                    </Text>
                  </View>
                  
                  {assignment.rubrics && assignment.rubrics.length > 0 && (
                    <View>
                      <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                        Rubrics: {assignment.rubrics.length} criteria
                      </Text>
                    </View>
                  )}
                  
                  <View>
                    <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                      {assignment.description.substring(0, 100)}...
                    </Text>
                  </View>
                </View>
              </View>
            )}
          </Animated.View>
        </ScrollView>
      </View>
    </Modal>
  );
}
