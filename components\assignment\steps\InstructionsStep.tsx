import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { AssignmentData } from '../AssignmentCreationWizard';

interface InstructionsStepProps {
  data: AssignmentData;
  updateData: (updates: Partial<AssignmentData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const INSTRUCTION_TEMPLATES = [
  {
    id: 'essay',
    title: 'Essay Assignment',
    content: `Instructions:
1. Write a well-structured essay of 500-750 words
2. Include an introduction, body paragraphs, and conclusion
3. Use proper citations and references
4. Proofread your work before submission

Grading Criteria:
- Content and understanding (40%)
- Organization and structure (30%)
- Grammar and writing style (20%)
- Citations and references (10%)`,
  },
  {
    id: 'research',
    title: 'Research Project',
    content: `Instructions:
1. Choose a topic related to our current unit of study
2. Conduct research using at least 3 credible sources
3. Create a presentation or written report
4. Include a bibliography of sources used

Requirements:
- Minimum 10 slides or 1000 words
- Visual aids (charts, images, graphs)
- Proper citation format
- Submit by the due date`,
  },
  {
    id: 'problem_solving',
    title: 'Problem Solving',
    content: `Instructions:
1. Read each problem carefully
2. Show all your work and calculations
3. Explain your reasoning for each step
4. Double-check your answers

Tips for Success:
- Start with what you know
- Break complex problems into smaller parts
- Use diagrams or charts when helpful
- Review your work before submitting`,
  },
  {
    id: 'creative',
    title: 'Creative Project',
    content: `Instructions:
1. Express your understanding through a creative medium
2. Choose from: poster, video, presentation, or artwork
3. Include key concepts and vocabulary
4. Be prepared to present your work to the class

Project Requirements:
- Demonstrate understanding of the topic
- Show creativity and effort
- Include at least 5 key terms or concepts
- Follow presentation guidelines`,
  },
];

export default function InstructionsStep({ data, updateData, onNext, onPrev }: InstructionsStepProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);

  const handleTemplateSelect = (template: typeof INSTRUCTION_TEMPLATES[0]) => {
    Alert.alert(
      'Use Template',
      `This will replace your current instructions with the "${template.title}" template. Continue?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Use Template',
          onPress: () => {
            updateData({ instructions: template.content });
            setSelectedTemplate(template.id);
            setShowTemplates(false);
          },
        },
      ]
    );
  };

  const handleNext = () => {
    if (!data.instructions.trim()) {
      Alert.alert(
        'Instructions Required',
        'Please provide instructions for students or use a template.',
        [{ text: 'OK' }]
      );
      return;
    }
    onNext();
  };

  const renderTemplateCard = (template: typeof INSTRUCTION_TEMPLATES[0]) => (
    <TouchableOpacity
      key={template.id}
      onPress={() => handleTemplateSelect(template)}
      className={`p-4 rounded-xl mb-3 border ${
        selectedTemplate === template.id
          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
          : isDark
          ? 'border-dark-border bg-dark-card'
          : 'border-light-border bg-light-card'
      }`}
    >
      <Text className={`font-rubik-medium text-base mb-2 ${
        selectedTemplate === template.id
          ? 'text-primary-600 dark:text-primary-400'
          : isDark ? 'text-dark-text' : 'text-light-text'
      }`}>
        {template.title}
      </Text>
      <Text className={`font-rubik text-sm ${
        isDark ? 'text-gray-300' : 'text-gray-600'
      }`} numberOfLines={3}>
        {template.content}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 p-4">
      <ScrollView showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="space-y-6">
          {/* Header */}
          <View>
            <Text className={`text-xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Assignment Instructions
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Provide clear, detailed instructions for students to follow.
            </Text>
          </View>

          {/* Template Toggle */}
          <TouchableOpacity
            onPress={() => setShowTemplates(!showTemplates)}
            className={`flex-row items-center justify-between p-4 rounded-xl ${
              isDark ? 'bg-dark-card' : 'bg-light-card'
            }`}
          >
            <View className="flex-row items-center">
              <Ionicons
                name="document-text-outline"
                size={20}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
              <Text className={`ml-3 font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Use Instruction Template
              </Text>
            </View>
            <Ionicons
              name={showTemplates ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          </TouchableOpacity>

          {/* Templates */}
          {showTemplates && (
            <Animated.View entering={FadeInDown.duration(300)} className="space-y-3">
              <Text className={`font-rubik-bold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Choose a Template
              </Text>
              {INSTRUCTION_TEMPLATES.map(renderTemplateCard)}
            </Animated.View>
          )}

          {/* Instructions Editor */}
          <View className="space-y-3">
            <View className="flex-row items-center justify-between">
              <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Instructions *
              </Text>
              <View className="flex-row items-center space-x-4">
                {data.instructions.length > 0 && (
                  <TouchableOpacity
                    onPress={() => updateData({ instructions: '' })}
                    className="px-3 py-1 rounded-lg bg-red-500"
                  >
                    <Text className="text-white font-rubik-medium text-sm">Clear</Text>
                  </TouchableOpacity>
                )}
                <Text className={`text-sm font-rubik ${
                  isDark ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {data.instructions.length} characters
                </Text>
              </View>
            </View>

            <View className={`rounded-xl overflow-hidden shadow-sm ${
              isDark ? 'bg-dark-card' : 'bg-light-card'
            }`}>
              <TextInput
                value={data.instructions}
                onChangeText={(text) => updateData({ instructions: text })}
                placeholder="Enter detailed instructions for students..."
                placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                multiline
                numberOfLines={12}
                textAlignVertical="top"
                className={`px-4 py-4 font-rubik text-base`}
                style={{
                  minHeight: 200,
                  color: isDark ? '#F3F4F6' : '#1F2937'
                }}
              />
            </View>
          </View>

          {/* Formatting Tips */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
            <View className="flex-row items-center mb-2">
              <Ionicons name="bulb-outline" size={20} color="#3B82F6" />
              <Text className="ml-2 font-rubik-medium text-blue-600 dark:text-blue-400">
                Formatting Tips
              </Text>
            </View>
            <Text className={`font-rubik text-sm ${isDark ? 'text-blue-200' : 'text-blue-700'}`}>
              • Use numbered lists for step-by-step instructions{'\n'}
              • Include clear expectations and grading criteria{'\n'}
              • Specify submission format and requirements{'\n'}
              • Add helpful resources or examples
            </Text>
          </View>

          {/* Spacer */}
          <View className="h-4" />

          {/* AI Enhancement Hint */}
          <View className={`p-4 rounded-xl border-2 border-dashed ${
            isDark ? 'border-purple-500/50 bg-purple-900/10' : 'border-purple-300 bg-purple-50'
          }`}>
            <View className="flex-row items-center mb-2">
              <Ionicons name="sparkles-outline" size={20} color="#8B5CF6" />
              <Text className="ml-2 font-rubik-medium text-purple-600 dark:text-purple-400">
                AI Enhancement Available
              </Text>
            </View>
            <Text className={`font-rubik text-sm ${isDark ? 'text-purple-200' : 'text-purple-700'}`}>
              In the next step, you can use AI to enhance your instructions, generate examples, or create additional content.
            </Text>
          </View>
        </Animated.View>
      </ScrollView>


    </View>
  );
}
