import { supabase } from '@/lib/supabase';

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id: assignmentId } = params;

    // Fetch assignment details
    const { data: assignment, error: assignmentError } = await supabase
      .from('assignments')
      .select('*')
      .eq('id', assignmentId)
      .single();

    if (assignmentError || !assignment) {
      return Response.json({ error: 'Assignment not found' }, { status: 404 });
    }

    // Fetch all submissions for this assignment
    const { data: submissions, error: submissionsError } = await supabase
      .from('assignment_submissions')
      .select(`
        *,
        student:students(name, email)
      `)
      .eq('assignment_id', assignmentId);

    if (submissionsError) {
      return Response.json({ error: 'Failed to fetch submissions' }, { status: 500 });
    }

    // Calculate analytics
    const totalSubmissions = submissions.length;
    const gradedSubmissions = submissions.filter(s => s.status === 'graded').length;
    const submittedSubmissions = submissions.filter(s => s.status === 'submitted' || s.status === 'graded').length;
    
    // Calculate average grade
    const gradedSubs = submissions.filter(s => s.grade !== null);
    const averageGrade = gradedSubs.length > 0 
      ? gradedSubs.reduce((sum, s) => sum + (s.grade || 0), 0) / gradedSubs.length 
      : 0;

    // Calculate completion rate (assuming we know total students in class)
    const { data: classStudents, error: studentsError } = await supabase
      .from('students')
      .select('id')
      .eq('class_id', assignment.class_id);

    const totalStudents = classStudents?.length || totalSubmissions;
    const completionRate = totalStudents > 0 ? (submittedSubmissions / totalStudents) * 100 : 0;

    // Grade distribution
    const gradeRanges = [
      { range: '90-100', min: 90, max: 100 },
      { range: '80-89', min: 80, max: 89 },
      { range: '70-79', min: 70, max: 79 },
      { range: '60-69', min: 60, max: 69 },
      { range: '0-59', min: 0, max: 59 },
    ];

    const gradeDistribution = gradeRanges.map(range => {
      const count = gradedSubs.filter(s => {
        const percentage = ((s.grade || 0) / assignment.max_points) * 100;
        return percentage >= range.min && percentage <= range.max;
      }).length;
      
      return {
        range: range.range,
        count,
        percentage: gradedSubs.length > 0 ? (count / gradedSubs.length) * 100 : 0,
      };
    });

    // Submission trends (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const submissionTrends = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const daySubmissions = submissions.filter(s => {
        const submissionDate = new Date(s.submitted_at).toISOString().split('T')[0];
        return submissionDate === dateStr;
      }).length;

      submissionTrends.push({
        date: dateStr,
        submissions: daySubmissions,
      });
    }

    // Performance metrics
    const highPerformers = gradedSubs.filter(s => ((s.grade || 0) / assignment.max_points) >= 0.9).length;
    const averagePerformers = gradedSubs.filter(s => {
      const percentage = (s.grade || 0) / assignment.max_points;
      return percentage >= 0.7 && percentage < 0.9;
    }).length;
    const needsImprovement = gradedSubs.filter(s => ((s.grade || 0) / assignment.max_points) < 0.7).length;

    // Late submissions
    const dueDate = new Date(assignment.due_date);
    const lateSubmissions = submissions.filter(s => {
      const submissionDate = new Date(s.submitted_at);
      return submissionDate > dueDate;
    }).length;

    const analyticsData = {
      totalSubmissions,
      gradedSubmissions,
      averageGrade,
      completionRate,
      gradeDistribution,
      submissionTrends,
      performanceMetrics: {
        highPerformers,
        averagePerformers,
        needsImprovement,
      },
      completionData: {
        totalStudents,
        submitted: submittedSubmissions,
        graded: gradedSubmissions,
        pending: submittedSubmissions - gradedSubmissions,
        late: lateSubmissions,
        missing: totalStudents - submittedSubmissions,
      },
      assignment: {
        id: assignment.id,
        title: assignment.title,
        max_points: assignment.max_points,
        due_date: assignment.due_date,
        status: assignment.status,
      },
    };

    return Response.json(analyticsData);

  } catch (error) {
    console.error('Error fetching assignment analytics:', error);
    return Response.json(
      { error: 'Failed to fetch assignment analytics' },
      { status: 500 }
    );
  }
}

// Get class-wide assignment analytics
export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const { classId } = await request.json();

    // Fetch all assignments for the class
    const { data: assignments, error: assignmentsError } = await supabase
      .from('assignments')
      .select(`
        *,
        submissions:assignment_submissions(*)
      `)
      .eq('class_id', classId)
      .order('created_at', { ascending: false });

    if (assignmentsError) {
      return Response.json({ error: 'Failed to fetch assignments' }, { status: 500 });
    }

    // Calculate class-wide metrics
    const classMetrics = assignments.map(assignment => {
      const submissions = assignment.submissions || [];
      const gradedSubmissions = submissions.filter((s: any) => s.status === 'graded');
      const averageGrade = gradedSubmissions.length > 0
        ? gradedSubmissions.reduce((sum: number, s: any) => sum + (s.grade || 0), 0) / gradedSubmissions.length
        : 0;

      return {
        id: assignment.id,
        title: assignment.title,
        averageGrade,
        completionRate: submissions.length > 0 ? (submissions.length / 30) * 100 : 0, // Assuming 30 students
        submissionCount: submissions.length,
        gradedCount: gradedSubmissions.length,
        maxPoints: assignment.max_points,
        dueDate: assignment.due_date,
      };
    });

    // Overall class performance
    const overallAverage = classMetrics.length > 0
      ? classMetrics.reduce((sum, a) => sum + a.averageGrade, 0) / classMetrics.length
      : 0;

    const overallCompletionRate = classMetrics.length > 0
      ? classMetrics.reduce((sum, a) => sum + a.completionRate, 0) / classMetrics.length
      : 0;

    return Response.json({
      classMetrics,
      overallAverage,
      overallCompletionRate,
      totalAssignments: assignments.length,
    });

  } catch (error) {
    console.error('Error fetching class analytics:', error);
    return Response.json(
      { error: 'Failed to fetch class analytics' },
      { status: 500 }
    );
  }
}
