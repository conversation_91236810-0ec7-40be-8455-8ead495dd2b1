import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';

type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused';

interface AttendanceStatusButtonProps {
  status: AttendanceStatus;
  isSelected: boolean;
  onPress: () => void;
  size?: 'small' | 'medium' | 'large';
}

export const AttendanceStatusButton: React.FC<AttendanceStatusButtonProps> = ({
  status,
  isSelected,
  onPress,
  size = 'medium'
}) => {
  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case 'present': return 'bg-success';
      case 'absent': return 'bg-error';
      case 'late': return 'bg-warning';
      case 'excused': return 'bg-info';
      default: return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case 'present': return 'checkmark.circle.fill';
      case 'absent': return 'xmark.circle.fill';
      case 'late': return 'clock.fill';
      case 'excused': return 'questionmark.circle.fill';
      default: return 'circle';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small': return 'p-1';
      case 'medium': return 'p-2';
      case 'large': return 'p-3';
      default: return 'p-2';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 12;
      case 'medium': return 14;
      case 'large': return 16;
      default: return 14;
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small': return 'text-xs';
      case 'medium': return 'text-xs';
      case 'large': return 'text-sm';
      default: return 'text-xs';
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`flex-1 rounded-lg flex-row items-center justify-center ${getSizeClasses()} ${
        isSelected ? getStatusColor(status) : 'bg-gray-200'
      }`}
    >
      <IconSymbol
        name={getStatusIcon(status)}
        size={getIconSize()}
        color={isSelected ? '#FFFFFF' : '#6B7280'}
      />
      <Text className={`font-rubik-medium ${getTextSize()} ml-1 capitalize ${
        isSelected ? 'text-white' : 'text-gray-600'
      }`}>
        {status}
      </Text>
    </TouchableOpacity>
  );
};
