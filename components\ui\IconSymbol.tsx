// Fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight } from 'expo-symbols';
import { ComponentProps } from 'react';
import { OpaqueColorValue, type StyleProp, type TextStyle } from 'react-native';

// Define the supported SF Symbols
export type SFSymbols6_0 =
  | 'house.fill'
  | 'paperplane.fill'
  | 'chevron.left.forwardslash.chevron.right'
  | 'chevron.right'
  | 'chevron.left'
  | 'eye.fill'
  | 'eye.slash.fill'
  | 'building.2.fill'
  | 'person.2.fill'
  | 'dollarsign.circle.fill'
  | 'chart.bar.fill'
  | 'megaphone.fill'
  | 'gearshape.fill'
  | 'plus.circle.fill'
  | 'trash.fill'
  | 'pencil'
  | 'arrow.left'
  | 'xmark'
  | 'checkmark'
  | 'envelope.fill'
  | 'phone.fill'
  | 'calendar'
  | 'doc.text.fill'
  | 'person.fill'
  | 'person.3.fill'
  | 'graduationcap.fill'
  | 'bell.fill'
  | 'book.fill'
  | 'list.bullet'
  | 'square.and.pencil'
  | 'arrow.clockwise'
  | 'magnifyingglass'
  | 'exclamationmark.triangle.fill'
  | 'square.and.arrow.up'
  | 'checkmark.circle.fill'
  | 'xmark.circle.fill'
  | 'clock.fill'
  | 'person.3.fill'
  | 'person.circle.fill'
  | 'paintbrush.fill'
  | 'info.circle.fill'
  | 'power'
  | 'lock.shield.fill'
  | 'questionmark.circle.fill'
  | 'person.badge.plus'
  | 'list.bullet.clipboard'
  | 'rectangle.3.group.fill'
  | 'checkmark.seal.fill'
  | 'pause.circle.fill'
  | 'arrow.right.circle.fill'
  | 'camera.fill'
  | 'person.text.rectangle.fill'
  | 'bell.badge.fill'
  | 'lock.fill'
  | 'arrow.triangle.2.circlepath'
  | 'folder.fill'
  | 'doc.text'
  | 'arrow.down.circle.fill'
  | 'plus';

type IconMapping = Record<SFSymbols6_0, ComponentProps<typeof MaterialIcons>['name']>;
type IconSymbolName = keyof typeof MAPPING;

/**
 * Add your SF Symbols to Material Icons mappings here.
 * - see Material Icons in the [Icons Directory](https://icons.expo.fyi).
 * - see SF Symbols in the [SF Symbols](https://developer.apple.com/sf-symbols/) app.
 */
const MAPPING = {
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  'chevron.left': 'chevron-left',
  'eye.fill': 'visibility',
  'eye.slash.fill': 'visibility-off',
  'building.2.fill': 'business',
  'person.2.fill': 'people',
  'dollarsign.circle.fill': 'attach-money',
  'chart.bar.fill': 'bar-chart',
  'megaphone.fill': 'campaign',
  'gearshape.fill': 'settings',
  'plus.circle.fill': 'add-circle',
  'trash.fill': 'delete',
  'pencil': 'edit',
  'arrow.left': 'arrow-back',
  'xmark': 'close',
  'checkmark': 'check',
  'envelope.fill': 'email',
  'phone.fill': 'phone',
  'calendar': 'calendar-today',
  'doc.text.fill': 'description',
  'person.fill': 'person',
  'person.3.fill': 'groups',
  'graduationcap.fill': 'school',
  'bell.fill': 'notifications',
  'book.fill': 'book',
  'list.bullet': 'list',
  'square.and.pencil': 'edit-note',
  'arrow.clockwise': 'refresh',
  'magnifyingglass': 'search',
  'exclamationmark.triangle.fill': 'warning',
  'square.and.arrow.up': 'share',
  'checkmark.circle.fill': 'check-circle',
  'xmark.circle.fill': 'cancel',
  'clock.fill': 'access-time',
  'person.circle.fill': 'account-circle',
  'paintbrush.fill': 'brush',
  'info.circle.fill': 'info',
  'power': 'power-settings-new',
  'lock.shield.fill': 'security',
  'questionmark.circle.fill': 'help',
  'person.badge.plus': 'person-add',
  'list.bullet.clipboard': 'assignment',
  'rectangle.3.group.fill': 'view-module',
  'checkmark.seal.fill': 'verified-user',
  'pause.circle.fill': 'pause-circle-filled',
  'arrow.right.circle.fill': 'arrow-forward',
  'camera.fill': 'camera-alt',
  'person.text.rectangle.fill': 'badge',
  'bell.badge.fill': 'notifications-active',
  'lock.fill': 'lock',
  'arrow.triangle.2.circlepath': 'sync',
  'folder.fill': 'folder',
  'doc.text': 'description',
  'arrow.down.circle.fill': 'download',
  'plus': 'add'
} as IconMapping;

/**
 * An icon component that uses native SF Symbols on iOS, and Material Icons on Android and web.
 * This ensures a consistent look across platforms, and optimal resource usage.
 * Icon `name`s are based on SF Symbols and require manual mapping to Material Icons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  return <MaterialIcons color={color} size={size} name={MAPPING[name]} style={style} />;
}
