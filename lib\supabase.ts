import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import "react-native-url-polyfill/auto";
import { CACHE_CONFIG, CacheHelpers, cacheManager } from './cache';

// Debug all environment variables first
console.log('=== ALL ENV VARS ===');
console.log('EXPO_PUBLIC_SUPABASE_URL:', process.env.EXPO_PUBLIC_SUPABASE_URL);
console.log('EXPO_PUBLIC_SUPABASE_ANON_KEY:', process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY);
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY);
console.log('=== END ENV VARS ===');

// Export the URL and key for use in the ClerkSupabaseProvider
export const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
export const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;
export const supabaseServiceKey = process.env.EXPO_PUBLIC_SUPABASE_SERVICE_ROLE_KEY!;

// Debug environment variables
console.log('Environment variables check:');
console.log('supabaseUrl:', supabaseUrl ? 'SET' : 'MISSING');
console.log('supabaseAnonKey:', supabaseAnonKey ? 'SET' : 'MISSING');
console.log('supabaseServiceKey:', supabaseServiceKey ? 'SET' : 'MISSING');

// Create a default Supabase client for non-component usage
// This client will be used for operations that don't require authentication
// For authenticated operations, use the client from ClerkSupabaseProvider
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Create a service role client for admin operations that bypass RLS
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// User types
export type UserRole = 'admin' | 'teacher' | 'student' | 'parent';

export interface User {
  id: string;
  tenant_id?: string;
  clerk_user_id: string;
  role: UserRole;
  email: string;
  name: string;
  created_at?: string;
}

export interface Tenant {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  subscription_status: 'trial' | 'active' | 'inactive';
  trial_start_date?: string;
  stripe_subscription_id?: string;
  created_at?: string;
}

// Create a new user in Supabase after Clerk authentication
export const createUser = async (
  clerkUserId: string,
  email: string,
  name: string,
  role: UserRole = 'admin'
): Promise<User | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert({
        clerk_user_id: clerkUserId,
        email,
        name,
        role,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating user:', error);
      return null;
    }

    return data as User;
  } catch (error) {
    console.error('Exception creating user:', error);
    return null;
  }
};

// Create a new tenant (school)
export const createTenant = async (
  name: string,
  userId: string,
  address?: string,
  phone?: string
): Promise<Tenant | null> => {
  try {
    console.log('Creating tenant with name:', name, 'address:', address, 'phone:', phone);

    // First create the tenant using the standard Supabase API
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        name,
        address,
        phone,
        subscription_status: 'trial',
        trial_start_date: new Date().toISOString(),
      })
      .select()
      .single();

    if (tenantError) {
      console.error('Error creating tenant:', tenantError);

      // If we get an RLS error, try again with a direct SQL query
      if (tenantError.code === '42501') {
        console.log('Attempting to create tenant with direct SQL query to bypass RLS...');

        // Use a direct SQL query to bypass RLS
        const { data: sqlData, error: sqlError } = await supabase.rpc('create_tenant', {
          p_name: name,
          p_address: address || '',
          p_phone: phone || '',
          p_subscription_status: 'trial',
          p_trial_start_date: new Date().toISOString()
        });

        if (sqlError) {
          console.error('Error creating tenant with SQL:', sqlError);
          throw new Error(`Failed to create tenant with SQL: ${sqlError.message}`);
        }

        if (!sqlData || !sqlData.id) {
          console.error('No tenant data returned from SQL');
          throw new Error('No tenant data returned from SQL');
        }

        console.log('Tenant created successfully with SQL:', sqlData);

        // Then update the user with the tenant_id
        const { error: userError } = await supabase
          .from('users')
          .update({ tenant_id: sqlData.id })
          .eq('id', userId);

        if (userError) {
          console.error('Error updating user with tenant_id:', userError);
          throw new Error(`Failed to update user with tenant_id: ${userError.message}`);
        }

        return sqlData as unknown as Tenant;
      }

      // If it's not an RLS error, throw the original error
      throw new Error(`Failed to create tenant: ${tenantError.message}`);
    }

    console.log('Tenant created successfully:', tenantData);

    if (!tenantData) {
      console.error('No tenant data returned');
      throw new Error('No tenant data returned from Supabase');
    }

    // Then update the user with the tenant_id
    const { error: userError } = await supabase
      .from('users')
      .update({ tenant_id: tenantData.id })
      .eq('id', userId);

    if (userError) {
      console.error('Error updating user with tenant_id:', userError);
      // Consider rolling back the tenant creation here

      // Try to delete the tenant we just created to avoid orphaned tenants
      try {
        await supabase
          .from('tenants')
          .delete()
          .eq('id', tenantData.id);
      } catch (rollbackError) {
        console.error('Error rolling back tenant creation:', rollbackError);
      }

      throw new Error(`Failed to update user with tenant_id: ${userError.message}`);
    }

    return tenantData as unknown as Tenant;
  } catch (error: any) {
    console.error('Exception creating tenant:', error);
    // Re-throw the error so it can be handled by the caller
    throw error;
  }
};

// Get user by Clerk ID with caching
export const getUserByClerkId = async (clerkUserId: string): Promise<User | null> => {
  const cacheKey = CacheHelpers.userKey(clerkUserId);

  return await cacheManager.get<User>(
    cacheKey,
    async () => {
      try {
        console.log('Fetching user from database for Clerk ID:', clerkUserId);

        // Use supabaseAdmin to bypass RLS for user lookup during authentication
        const { data, error } = await supabaseAdmin
          .from('users')
          .select('*')
          .eq('clerk_user_id', clerkUserId)
          .maybeSingle();

        if (error) {
          console.error('Error fetching user:', error);
          throw new Error(`Failed to fetch user: ${error.message}`);
        }

        if (data) {
          console.log('✅ Found user by Clerk ID:', data);
        } else {
          console.log('❌ No user found for Clerk ID:', clerkUserId);
        }

        return data as User;
      } catch (error) {
        console.error('Exception fetching user:', error);
        throw error;
      }
    },
    CACHE_CONFIG.USER_DATA
  );
};

// Get tenant by ID
export const getTenantById = async (tenantId: string): Promise<Tenant | null> => {
  try {
    const { data, error } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', tenantId)
      .single();

    if (error) {
      console.error('Error fetching tenant:', error);
      return null;
    }

    return data as Tenant;
  } catch (error) {
    console.error('Exception fetching tenant:', error);
    return null;
  }
};
