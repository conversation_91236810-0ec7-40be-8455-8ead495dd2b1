-- Create assignment and mock test system tables

-- Assignments table
CREATE TABLE IF NOT EXISTS assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  instructions TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  max_points INTEGER DEFAULT 100,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'closed')),
  gemini_generated BOOLEAN DEFAULT FALSE,
  attachment_urls TEXT[], -- Array of file URLs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mock Tests table
CREATE TABLE IF NOT EXISTS mock_tests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  instructions TEXT,
  time_limit INTEGER NOT NULL, -- Minutes
  max_points INTEGER DEFAULT 100,
  question_type VARCHAR(50) DEFAULT 'multiple_choice' CHECK (question_type IN ('multiple_choice', 'short_answer', 'essay', 'mixed')),
  auto_graded BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'closed')),
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  attempts_allowed INTEGER DEFAULT 1,
  show_results_immediately BOOLEAN DEFAULT FALSE,
  gemini_generated BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assignment Submissions table
CREATE TABLE IF NOT EXISTS assignment_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  assignment_id UUID NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
  content TEXT,
  attachment_urls TEXT[], -- Array of file URLs
  status VARCHAR(20) DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'graded', 'returned')),
  grade DECIMAL(5,2), -- Grade out of max_points
  feedback TEXT,
  gemini_feedback TEXT, -- AI-generated feedback
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  graded_at TIMESTAMP WITH TIME ZONE,
  graded_by UUID REFERENCES teachers(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(assignment_id, student_id)
);

-- Mock Test Attempts table
CREATE TABLE IF NOT EXISTS mock_test_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  mock_test_id UUID NOT NULL REFERENCES mock_tests(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
  attempt_number INTEGER NOT NULL DEFAULT 1,
  answers JSONB, -- Store answers as JSON
  score DECIMAL(5,2), -- Score out of max_points
  time_taken INTEGER, -- Minutes taken
  status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  graded_at TIMESTAMP WITH TIME ZONE,
  feedback TEXT,
  gemini_feedback TEXT, -- AI-generated feedback
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(mock_test_id, student_id, attempt_number)
);

-- Questions table for mock tests
CREATE TABLE IF NOT EXISTS mock_test_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  mock_test_id UUID NOT NULL REFERENCES mock_tests(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  question_type VARCHAR(50) NOT NULL CHECK (question_type IN ('multiple_choice', 'short_answer', 'essay')),
  options JSONB, -- For multiple choice questions
  correct_answer TEXT, -- For auto-grading
  points INTEGER DEFAULT 1,
  order_index INTEGER NOT NULL,
  gemini_generated BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assignment rubrics table
CREATE TABLE IF NOT EXISTS assignment_rubrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  assignment_id UUID NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
  criteria_name VARCHAR(255) NOT NULL,
  description TEXT,
  max_points INTEGER NOT NULL,
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security (RLS)
ALTER TABLE assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE mock_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignment_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE mock_test_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE mock_test_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignment_rubrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for assignments
CREATE POLICY "Teachers can manage their class assignments" ON assignments
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND class_id IN (
      SELECT id FROM classes
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- RLS Policies for mock tests
CREATE POLICY "Teachers can manage their class mock tests" ON mock_tests
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND class_id IN (
      SELECT id FROM classes
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- RLS Policies for assignment submissions
CREATE POLICY "Teachers can view submissions for their assignments" ON assignment_submissions
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND assignment_id IN (
      SELECT id FROM assignments
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- RLS Policies for mock test attempts
CREATE POLICY "Teachers can view attempts for their mock tests" ON mock_test_attempts
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND mock_test_id IN (
      SELECT id FROM mock_tests
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- RLS Policies for mock test questions
CREATE POLICY "Teachers can manage questions for their mock tests" ON mock_test_questions
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND mock_test_id IN (
      SELECT id FROM mock_tests
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- RLS Policies for assignment rubrics
CREATE POLICY "Teachers can manage rubrics for their assignments" ON assignment_rubrics
  FOR ALL USING (
    tenant_id IN (
      SELECT tenant_id FROM teachers
      WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
    AND assignment_id IN (
      SELECT id FROM assignments
      WHERE teacher_id = (
        SELECT id FROM teachers
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
      )
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assignments_tenant_id ON assignments(tenant_id);
CREATE INDEX IF NOT EXISTS idx_assignments_class_id ON assignments(class_id);
CREATE INDEX IF NOT EXISTS idx_assignments_teacher_id ON assignments(teacher_id);
CREATE INDEX IF NOT EXISTS idx_assignments_status ON assignments(status);
CREATE INDEX IF NOT EXISTS idx_assignments_due_date ON assignments(due_date);

CREATE INDEX IF NOT EXISTS idx_mock_tests_tenant_id ON mock_tests(tenant_id);
CREATE INDEX IF NOT EXISTS idx_mock_tests_class_id ON mock_tests(class_id);
CREATE INDEX IF NOT EXISTS idx_mock_tests_teacher_id ON mock_tests(teacher_id);
CREATE INDEX IF NOT EXISTS idx_mock_tests_status ON mock_tests(status);
CREATE INDEX IF NOT EXISTS idx_mock_tests_start_time ON mock_tests(start_time);

CREATE INDEX IF NOT EXISTS idx_assignment_submissions_assignment_id ON assignment_submissions(assignment_id);
CREATE INDEX IF NOT EXISTS idx_assignment_submissions_student_id ON assignment_submissions(student_id);
CREATE INDEX IF NOT EXISTS idx_assignment_submissions_status ON assignment_submissions(status);

CREATE INDEX IF NOT EXISTS idx_mock_test_attempts_mock_test_id ON mock_test_attempts(mock_test_id);
CREATE INDEX IF NOT EXISTS idx_mock_test_attempts_student_id ON mock_test_attempts(student_id);
CREATE INDEX IF NOT EXISTS idx_mock_test_attempts_status ON mock_test_attempts(status);

CREATE INDEX IF NOT EXISTS idx_mock_test_questions_mock_test_id ON mock_test_questions(mock_test_id);
CREATE INDEX IF NOT EXISTS idx_mock_test_questions_order_index ON mock_test_questions(order_index);

CREATE INDEX IF NOT EXISTS idx_assignment_rubrics_assignment_id ON assignment_rubrics(assignment_id);
CREATE INDEX IF NOT EXISTS idx_assignment_rubrics_order_index ON assignment_rubrics(order_index);

-- Update timestamp triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to all tables
DROP TRIGGER IF EXISTS update_assignments_updated_at ON assignments;
CREATE TRIGGER update_assignments_updated_at
  BEFORE UPDATE ON assignments
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_mock_tests_updated_at ON mock_tests;
CREATE TRIGGER update_mock_tests_updated_at
  BEFORE UPDATE ON mock_tests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_assignment_submissions_updated_at ON assignment_submissions;
CREATE TRIGGER update_assignment_submissions_updated_at
  BEFORE UPDATE ON assignment_submissions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_mock_test_attempts_updated_at ON mock_test_attempts;
CREATE TRIGGER update_mock_test_attempts_updated_at
  BEFORE UPDATE ON mock_test_attempts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_mock_test_questions_updated_at ON mock_test_questions;
CREATE TRIGGER update_mock_test_questions_updated_at
  BEFORE UPDATE ON mock_test_questions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_assignment_rubrics_updated_at ON assignment_rubrics;
CREATE TRIGGER update_assignment_rubrics_updated_at
  BEFORE UPDATE ON assignment_rubrics
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
