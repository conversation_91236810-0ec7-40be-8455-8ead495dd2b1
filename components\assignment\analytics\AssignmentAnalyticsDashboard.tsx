import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
import PerformanceCharts from './PerformanceCharts';
import CompletionRates from './CompletionRates';
import TimeAnalysis from './TimeAnalysis';
import ComparativeAnalysis from './ComparativeAnalysis';

interface Assignment {
  id: string;
  title: string;
  max_points: number;
  due_date: string;
  status: string;
  submissions_count: number;
  graded_count: number;
}

interface AnalyticsData {
  totalSubmissions: number;
  gradedSubmissions: number;
  averageGrade: number;
  completionRate: number;
  gradeDistribution: { range: string; count: number; percentage: number }[];
  submissionTrends: { date: string; submissions: number }[];
  performanceMetrics: {
    highPerformers: number;
    averagePerformers: number;
    needsImprovement: number;
  };
}

interface AssignmentAnalyticsDashboardProps {
  assignment: Assignment;
  classId: string;
}

export default function AssignmentAnalyticsDashboard({
  assignment,
  classId,
}: AssignmentAnalyticsDashboardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'trends' | 'comparison'>('overview');

  useEffect(() => {
    fetchAnalyticsData();
  }, [assignment.id]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockData: AnalyticsData = {
        totalSubmissions: assignment.submissions_count || 0,
        gradedSubmissions: assignment.graded_count || 0,
        averageGrade: 78.5,
        completionRate: 85.7,
        gradeDistribution: [
          { range: '90-100', count: 8, percentage: 25.8 },
          { range: '80-89', count: 12, percentage: 38.7 },
          { range: '70-79', count: 7, percentage: 22.6 },
          { range: '60-69', count: 3, percentage: 9.7 },
          { range: '0-59', count: 1, percentage: 3.2 },
        ],
        submissionTrends: [
          { date: '2024-01-15', submissions: 5 },
          { date: '2024-01-16', submissions: 12 },
          { date: '2024-01-17', submissions: 8 },
          { date: '2024-01-18', submissions: 6 },
        ],
        performanceMetrics: {
          highPerformers: 8,
          averagePerformers: 19,
          needsImprovement: 4,
        },
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', title: 'Overview', icon: 'analytics-outline' },
    { id: 'performance', title: 'Performance', icon: 'trending-up-outline' },
    { id: 'trends', title: 'Trends', icon: 'stats-chart-outline' },
    { id: 'comparison', title: 'Compare', icon: 'git-compare-outline' },
  ];

  const renderOverview = () => (
    <View className="space-y-4">
      {/* Key Metrics */}
      <View className="grid grid-cols-2 gap-4">
        <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`text-2xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {analyticsData?.totalSubmissions || 0}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Total Submissions
          </Text>
        </View>

        <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`text-2xl font-rubik-bold text-green-500`}>
            {analyticsData?.completionRate.toFixed(1) || 0}%
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Completion Rate
          </Text>
        </View>

        <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`text-2xl font-rubik-bold text-blue-500`}>
            {analyticsData?.averageGrade.toFixed(1) || 0}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Average Grade
          </Text>
        </View>

        <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`text-2xl font-rubik-bold text-purple-500`}>
            {analyticsData?.gradedSubmissions || 0}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Graded
          </Text>
        </View>
      </View>

      {/* Grade Distribution */}
      <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Grade Distribution
        </Text>
        {analyticsData?.gradeDistribution.map((item, index) => (
          <View key={index} className="flex-row items-center justify-between mb-3">
            <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {item.range}
            </Text>
            <View className="flex-row items-center flex-1 mx-3">
              <View className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <View
                  className="bg-primary-500 h-2 rounded-full"
                  style={{ width: `${item.percentage}%` }}
                />
              </View>
              <Text className={`ml-2 font-rubik-medium text-sm ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {item.count}
              </Text>
            </View>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {item.percentage}%
            </Text>
          </View>
        ))}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View className={`flex-1 items-center justify-center ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text className={`mt-4 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Loading analytics...
        </Text>
      </View>
    );
  }

  return (
    <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="p-4 border-b border-gray-200 dark:border-gray-700">
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {assignment.title} - Analytics
        </Text>
        <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          Assignment performance and insights
        </Text>
      </View>

      {/* Tabs */}
      <View className="flex-row p-4 space-x-2">
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            onPress={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl ${
              activeTab === tab.id
                ? 'bg-primary-500'
                : isDark
                ? 'bg-dark-card'
                : 'bg-light-card'
            }`}
          >
            <Ionicons
              name={tab.icon as any}
              size={16}
              color={activeTab === tab.id ? 'white' : isDark ? '#FFFFFF' : '#000000'}
            />
            <Text
              className={`ml-2 font-rubik-medium text-sm ${
                activeTab === tab.id
                  ? 'text-white'
                  : isDark
                  ? 'text-dark-text'
                  : 'text-light-text'
              }`}
            >
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInDown.duration(400)}>
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'performance' && (
            <PerformanceCharts data={analyticsData} assignment={assignment} />
          )}
          {activeTab === 'trends' && (
            <TimeAnalysis data={analyticsData} assignment={assignment} />
          )}
          {activeTab === 'comparison' && (
            <ComparativeAnalysis assignment={assignment} classId={classId} />
          )}
        </Animated.View>
      </ScrollView>
    </View>
  );
}
