import { SignOutButton } from '@/components/SignOutButton';
import { IconSymbol, type SFSymbols6_0 } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth, useUser } from '@clerk/clerk-expo';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import { Image, Pressable, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ProfileScreen() {
  const { isLoaded, user } = useUser();
  const { isSignedIn } = useAuth();
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [showFullEmail, setShowFullEmail] = useState(false);

  const handleImagePick = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      alert('Sorry, we need camera roll permissions to make this work!');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      // Here you would typically upload the image to your backend
      // and update the user's profile picture
      console.log('Selected image:', result.assets[0].uri);
    }
  };

  if (!isLoaded || !isSignedIn) {
    return (
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <View className="flex-1 items-center justify-center">
          <Text className={`text-lg font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const email = user?.primaryEmailAddress?.emailAddress || 'No email provided';
  const displayEmail = showFullEmail ? email : email.length > 25 ? `${email.substring(0, 25)}...` : email;

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`} edges={['top']}>
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Clean Profile Header */}
        <View className={`px-6 pt-8 pb-8 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
          <View className="items-center">
            {/* Profile Picture Section */}
            <View className="relative mb-6">
              <View className="w-28 h-28 rounded-full overflow-hidden">
                {user?.imageUrl ? (
                  <Image
                    source={{ uri: user.imageUrl }}
                    className="w-28 h-28 rounded-full"
                    defaultSource={require('@/assets/images/icon.png')}
                  />
                ) : (
                  <View className="w-28 h-28 rounded-full bg-primary-500 items-center justify-center">
                    <IconSymbol
                      name="person.circle.fill"
                      size={70}
                      color="#FFFFFF"
                    />
                  </View>
                )}
              </View>

              <TouchableOpacity
                onPress={handleImagePick}
                className="absolute -bottom-1 -right-1 w-8 h-8 bg-primary-500 rounded-full items-center justify-center"
              >
                <IconSymbol
                  name="camera.fill"
                  size={16}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </View>

            {/* User Info Section */}
            <View className="items-center">
              <Text className={`text-2xl font-rubik-bold text-center mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {user?.fullName || 'User Name'}
              </Text>

              <Pressable
                onPress={() => setShowFullEmail(!showFullEmail)}
                className="mb-4"
              >
                <Text className={`text-base font-rubik text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  {displayEmail}
                </Text>
              </Pressable>

              {/* Status Badges */}
              <View className="flex-row items-center justify-center gap-4">
                <View className="px-4 py-2 bg-primary-500 rounded-full">
                  <Text className="font-rubik-medium text-white text-sm">
                    Teacher
                  </Text>
                </View>
                <View className={`px-4 py-2 rounded-full ${isDark ? 'bg-green-900/30' : 'bg-green-100'}`}>
                  <Text className="font-rubik-medium text-green-600 dark:text-green-400 text-sm">
                    Active
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Content Cards */}
        <View className="px-6">
          {/* Account Information Card */}
          <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-white'} border ${isDark ? 'border-dark-border' : 'border-gray-200'} mb-8`}>
            {/* Card Header */}
            <View className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-primary-500/10 rounded-lg items-center justify-center mr-3">
                  <IconSymbol
                    name="person.text.rectangle.fill"
                    size={20}
                    color="#3B82F6"
                  />
                </View>
                <Text className={`text-lg font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Account Information
                </Text>
              </View>
            </View>

            {/* Card Content */}
            <View className="p-6 space-y-4">
              <ProfileItem
                icon="person.fill"
                label="Role"
                value="Teacher"
                highlighted
              />
              <ProfileItem
                icon="calendar"
                label="Joined"
                value={new Date(user?.createdAt || '').toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              />
              <ProfileItem
                icon="envelope.fill"
                label="Email"
                value={displayEmail}
                onPress={() => setShowFullEmail(!showFullEmail)}
              />
              <ProfileItem
                icon="checkmark.seal.fill"
                label="Status"
                value="Verified"
                highlighted
              />
            </View>
          </View>

          {/* App Settings Card */}
          <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-white'} border ${isDark ? 'border-dark-border' : 'border-gray-200'} mb-8`}>
            {/* Card Header */}
            <View className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-primary-500/10 rounded-lg items-center justify-center mr-3">
                  <IconSymbol
                    name="gearshape.fill"
                    size={20}
                    color="#3B82F6"
                  />
                </View>
                <Text className={`text-lg font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  App Settings
                </Text>
              </View>
            </View>

            {/* Card Content */}
            <View className="p-6 space-y-4">
              <ProfileItem
                icon="bell.badge.fill"
                label="Notifications"
                value="Enabled"
                highlighted
              />
              <ProfileItem
                icon="paintbrush.fill"
                label="Theme"
                value={colorScheme === 'dark' ? 'Dark Mode' : 'Light Mode'}
              />
              <ProfileItem
                icon="lock.fill"
                label="Privacy"
                value="Configure"
              />
              <ProfileItem
                icon="arrow.triangle.2.circlepath"
                label="Sync"
                value="Automatic"
                highlighted
              />
            </View>
          </View>

          {/* Quick Actions */}
          <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-white'} border ${isDark ? 'border-dark-border' : 'border-gray-200'} mb-8`}>
            <View className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-purple-500/10 rounded-lg items-center justify-center mr-3">
                  <IconSymbol
                    name="hand.tap.fill"
                    size={20}
                    color="#8B5CF6"
                  />
                </View>
                <Text className={`text-lg font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Quick Actions
                </Text>
              </View>
            </View>

            <View className="p-6 space-y-4">
              <ProfileItem
                icon="doc.text.fill"
                label="My Assignments"
                value="View All"
                onPress={() => {}}
              />
              <ProfileItem
                icon="chart.bar.fill"
                label="Analytics"
                value="Dashboard"
                onPress={() => {}}
              />
              <ProfileItem
                icon="bell.fill"
                label="Notifications"
                value="Settings"
                onPress={() => {}}
              />
            </View>
          </View>

          {/* Sign Out Section */}
          <View className="pt-4">
            <SignOutButton fullWidth={true} />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

interface ProfileItemProps {
  icon: SFSymbols6_0;
  label: string;
  value: string;
  highlighted?: boolean;
  onPress?: () => void;
}

const ProfileItem: React.FC<ProfileItemProps> = ({ icon, label, value, highlighted, onPress }) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <Pressable
      onPress={onPress}
      className={`flex-row items-center py-3 ${
        onPress ? (isDark ? 'active:bg-gray-800/50' : 'active:bg-gray-50') : ''
      }`}
    >
      <View className={`w-8 h-8 rounded-lg items-center justify-center mr-3 ${
        highlighted
          ? 'bg-primary-500/20'
          : isDark ? 'bg-gray-700/50' : 'bg-gray-100'
      }`}>
        <IconSymbol
          name={icon}
          size={16}
          color={highlighted ? '#3B82F6' : isDark ? '#D1D5DB' : '#6B7280'}
        />
      </View>

      <View className="flex-1 flex-row justify-between items-center">
        <Text className={`font-rubik-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
          {label}
        </Text>
        <View className="flex-row items-center">
          <Text className={`font-rubik text-sm ${
            highlighted
              ? 'text-primary-500 font-rubik-medium'
              : isDark ? 'text-gray-300' : 'text-gray-600'
          } mr-2`}>
            {value}
          </Text>
          {onPress && (
            <IconSymbol
              name="chevron.right"
              size={14}
              color={isDark ? '#9CA3AF' : '#6B7280'}
            />
          )}
        </View>
      </View>
    </Pressable>
  );
};