import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { useAuth, useUser } from '@clerk/clerk-expo';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function AuthTester() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const { isSignedIn, getToken } = useAuth();
  const { user } = useUser();
  const [authInfo, setAuthInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const checkAuth = async () => {
    setLoading(true);
    try {
      console.log('=== AUTH CHECK ===');

      // Check Clerk auth
      console.log('Clerk isSignedIn:', isSignedIn);
      console.log('Clerk user:', user?.id, user?.emailAddresses?.[0]?.emailAddress);

      if (!isSignedIn || !user) {
        Alert.alert('Not Signed In', 'Please sign in with <PERSON> first');
        return;
      }

      // Get Clerk token
      const token = await getToken({ template: 'supabase' });
      console.log('Clerk token:', token ? 'Token received' : 'No token');

      // Test the new auth helper
      console.log('Testing new auth helper...');
      try {
        const { getCurrentUserAndTeacher } = await import('@/lib/authHelpers');
        const result = await getCurrentUserAndTeacher();
        console.log('Auth helper result:', result);

        const authInfo = {
          clerk: {
            isSignedIn,
            userId: user?.id,
            email: user?.emailAddresses?.[0]?.emailAddress,
            hasToken: !!token,
          },
          supabase: {
            hasSession: !!result.session,
            userId: result.session?.user?.id,
            sessionError: null,
          },
          database: {
            userExists: !!result.user,
            teacherExists: !!result.teacher,
            userData: result.user,
            teacherData: result.teacher,
          },
          authHelper: {
            success: true,
            message: 'Auth helper working correctly',
          },
        };

        setAuthInfo(authInfo);
        console.log('=== AUTH CHECK COMPLETE (SUCCESS) ===');

      } catch (authHelperError) {
        console.error('Auth helper error:', authHelperError);

        // Fallback to manual check
        console.log('Falling back to manual auth check...');

        // Sign in to Supabase with Clerk token
        if (token) {
          const { error } = await supabase.auth.signInWithToken({
            token,
          });
          console.log('Supabase signInWithToken result:', { error });
        }

        // Check Supabase session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('Supabase session:', {
          hasSession: !!session,
          userId: session?.user?.id,
          error: sessionError
        });

        // Check if user exists in database
        let userData = null;
        if (session?.user) {
          const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('clerk_user_id', session.user.id)
            .single();

          console.log('User in database:', { data, error });
          userData = data;
        }

        // Check if teacher exists
        let teacherData = null;
        if (userData) {
          const { data, error } = await supabase
            .from('teachers')
            .select('*')
            .eq('user_id', userData.id)
            .single();

          console.log('Teacher in database:', { data, error });
          teacherData = data;
        }

        const authInfo = {
          clerk: {
            isSignedIn,
            userId: user?.id,
            email: user?.emailAddresses?.[0]?.emailAddress,
            hasToken: !!token,
          },
          supabase: {
            hasSession: !!session,
            userId: session?.user?.id,
            sessionError: sessionError?.message,
          },
          database: {
            userExists: !!userData,
            teacherExists: !!teacherData,
            userData,
            teacherData,
          },
          authHelper: {
            success: false,
            message: authHelperError.message,
          },
        };

        setAuthInfo(authInfo);
        console.log('=== AUTH CHECK COMPLETE (FALLBACK) ===');
      }

    } catch (error) {
      console.error('Auth check error:', error);
      Alert.alert('Error', `Auth check failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const createMissingRecords = async () => {
    if (!authInfo?.supabase?.hasSession) {
      Alert.alert('Error', 'No Supabase session. Run auth check first.');
      return;
    }

    setLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) throw new Error('No session');

      // Create user if missing
      if (!authInfo.database.userExists) {
        const { data: newUser, error } = await supabase
          .from('users')
          .insert({
            clerk_user_id: session.user.id,
            email: user?.emailAddresses?.[0]?.emailAddress || '<EMAIL>',
            name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Test User',
            role: 'teacher',
            tenant_id: 'default-tenant',
          })
          .select()
          .single();

        if (error) throw error;
        console.log('Created user:', newUser);
      }

      // Get user data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('clerk_user_id', session.user.id)
        .single();

      if (userError || !userData) throw new Error('Failed to get user data');

      // Create teacher if missing
      if (!authInfo.database.teacherExists) {
        const { data: newTeacher, error } = await supabase
          .from('teachers')
          .insert({
            user_id: userData.id,
            tenant_id: userData.tenant_id,
            subject_specialization: 'General',
            years_of_experience: 1,
          })
          .select()
          .single();

        if (error) throw error;
        console.log('Created teacher:', newTeacher);
      }

      Alert.alert('Success', 'Missing records created successfully!');
      // Refresh auth info
      await checkAuth();

    } catch (error) {
      console.error('Error creating records:', error);
      Alert.alert('Error', `Failed to create records: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderAuthInfo = () => {
    if (!authInfo) return null;

    return (
      <View className={`mt-4 p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
        <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Authentication Status
        </Text>

        {/* Clerk Status */}
        <View className="mb-4">
          <Text className={`font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Clerk Auth
          </Text>
          <Text className={`font-rubik text-sm ${authInfo.clerk.isSignedIn ? 'text-green-600' : 'text-red-600'}`}>
            Signed In: {authInfo.clerk.isSignedIn ? 'Yes' : 'No'}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            User ID: {authInfo.clerk.userId || 'None'}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            Email: {authInfo.clerk.email || 'None'}
          </Text>
          <Text className={`font-rubik text-sm ${authInfo.clerk.hasToken ? 'text-green-600' : 'text-red-600'}`}>
            Has Token: {authInfo.clerk.hasToken ? 'Yes' : 'No'}
          </Text>
        </View>

        {/* Supabase Status */}
        <View className="mb-4">
          <Text className={`font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Supabase Auth
          </Text>
          <Text className={`font-rubik text-sm ${authInfo.supabase.hasSession ? 'text-green-600' : 'text-red-600'}`}>
            Has Session: {authInfo.supabase.hasSession ? 'Yes' : 'No'}
          </Text>
          <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
            User ID: {authInfo.supabase.userId || 'None'}
          </Text>
          {authInfo.supabase.sessionError && (
            <Text className="font-rubik text-sm text-red-600">
              Error: {authInfo.supabase.sessionError}
            </Text>
          )}
        </View>

        {/* Database Status */}
        <View className="mb-4">
          <Text className={`font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Database Records
          </Text>
          <Text className={`font-rubik text-sm ${authInfo.database.userExists ? 'text-green-600' : 'text-red-600'}`}>
            User Record: {authInfo.database.userExists ? 'Exists' : 'Missing'}
          </Text>
          <Text className={`font-rubik text-sm ${authInfo.database.teacherExists ? 'text-green-600' : 'text-red-600'}`}>
            Teacher Record: {authInfo.database.teacherExists ? 'Exists' : 'Missing'}
          </Text>
        </View>

        {/* Auth Helper Status */}
        {authInfo.authHelper && (
          <View className="mb-4">
            <Text className={`font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Auth Helper Status
            </Text>
            <Text className={`font-rubik text-sm ${authInfo.authHelper.success ? 'text-green-600' : 'text-red-600'}`}>
              Status: {authInfo.authHelper.success ? 'Working' : 'Failed'}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Message: {authInfo.authHelper.message}
            </Text>
          </View>
        )}

        {/* Action Button */}
        {(!authInfo.database.userExists || !authInfo.database.teacherExists) && (
          <TouchableOpacity
            onPress={createMissingRecords}
            disabled={loading}
            className={`mt-2 p-3 rounded-lg ${loading ? 'bg-gray-400' : 'bg-green-500'}`}
          >
            <Text className="text-white text-center font-rubik-medium">
              {loading ? 'Creating...' : 'Create Missing Records'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <ScrollView className={`flex-1 p-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <Text className={`text-2xl font-rubik-bold mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        Authentication Tester
      </Text>

      <TouchableOpacity
        onPress={checkAuth}
        disabled={loading}
        className={`p-4 rounded-xl mb-4 ${loading ? 'bg-gray-400' : 'bg-primary-500'}`}
      >
        <Text className="text-white text-center font-rubik-bold">
          {loading ? 'Checking...' : 'Check Authentication Status'}
        </Text>
      </TouchableOpacity>

      {renderAuthInfo()}
    </ScrollView>
  );
}
