import { supabase } from './supabase';

/**
 * Invokes a Supabase Edge Function with proper authentication
 *
 * @param functionName The name of the Edge Function to invoke
 * @param body The request body to send to the function
 * @param token The JWT token from Clerk
 * @param options Additional options for the function call
 * @returns The function response
 */
export const invokeEdgeFunction = async (
  functionName: string,
  body: any,
  token: string,
  options?: {
    headers?: Record<string, string>;
  }
) => {
  try {
    if (!token) {
      throw new Error('Authentication token is required');
    }

    // Call the Edge Function with the token in headers
    const response = await supabase.functions.invoke(functionName, {
      body,
      headers: {
        ...options?.headers,
        Authorization: `Bearer ${token}`,
      },
    });

    return response;
  } catch (error: any) {
    console.error(`Error invoking ${functionName}:`, error);
    throw error;
  }
};
