import AsyncStorage from '@react-native-async-storage/async-storage';

// Cache configuration
const CACHE_CONFIG = {
  // Cache durations in milliseconds
  USER_DATA: 5 * 60 * 1000, // 5 minutes
  TEACHER_STATS: 2 * 60 * 1000, // 2 minutes
  CLASSES: 10 * 60 * 1000, // 10 minutes
  STUDENTS: 5 * 60 * 1000, // 5 minutes
  ASSIGNMENTS: 3 * 60 * 1000, // 3 minutes
  ATTENDANCE: 1 * 60 * 1000, // 1 minute
  NOTICES: 5 * 60 * 1000, // 5 minutes
  ENROLLMENT_REQUESTS: 2 * 60 * 1000, // 2 minutes
  CLASS_STUDENTS: 5 * 60 * 1000, // 5 minutes

  // Default cache duration
  DEFAULT: 5 * 60 * 1000, // 5 minutes

  // Maximum cache size (number of entries)
  MAX_MEMORY_CACHE_SIZE: 100,
  MAX_STORAGE_CACHE_SIZE: 500,
};

// Cache key prefixes
const CACHE_KEYS = {
  MEMORY: 'memory_cache_',
  STORAGE: 'storage_cache_',
  USER: 'user_',
  TEACHER_STATS: 'teacher_stats_',
  CLASSES: 'classes_',
  STUDENTS: 'students_',
  ASSIGNMENTS: 'assignments_',
  ATTENDANCE: 'attendance_',
  NOTICES: 'notices_',
  ENROLLMENT_REQUESTS: 'enrollment_requests_',
  CLASS_STUDENTS: 'class_students_',
  METADATA: 'cache_metadata_',
};

// Cache entry interface
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  key: string;
  version: number;
}

// Cache metadata for tracking
interface CacheMetadata {
  totalEntries: number;
  lastCleanup: number;
  hitCount: number;
  missCount: number;
}

// In-memory cache
class MemoryCache {
  private cache = new Map<string, CacheEntry>();
  private accessOrder = new Map<string, number>();
  private accessCounter = 0;

  set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.DEFAULT): void {
    const now = Date.now();
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + ttl,
      key,
      version: 1,
    };

    // Remove oldest entries if cache is full
    if (this.cache.size >= CACHE_CONFIG.MAX_MEMORY_CACHE_SIZE) {
      this.evictOldest();
    }

    this.cache.set(key, entry);
    this.accessOrder.set(key, ++this.accessCounter);
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now > entry.expiresAt) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      return null;
    }

    // Update access order
    this.accessOrder.set(key, ++this.accessCounter);
    return entry.data as T;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const now = Date.now();
    if (now > entry.expiresAt) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): void {
    this.cache.delete(key);
    this.accessOrder.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;
  }

  private evictOldest(): void {
    let oldestKey = '';
    let oldestAccess = Infinity;

    for (const [key, accessTime] of this.accessOrder) {
      if (accessTime < oldestAccess) {
        oldestAccess = accessTime;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessOrder.delete(oldestKey);
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: CACHE_CONFIG.MAX_MEMORY_CACHE_SIZE,
      accessCounter: this.accessCounter,
    };
  }
}

// Storage cache for persistence
class StorageCache {
  private metadata: CacheMetadata = {
    totalEntries: 0,
    lastCleanup: Date.now(),
    hitCount: 0,
    missCount: 0,
  };

  async set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.DEFAULT): Promise<void> {
    try {
      const now = Date.now();
      const entry: CacheEntry<T> = {
        data,
        timestamp: now,
        expiresAt: now + ttl,
        key,
        version: 1,
      };

      const storageKey = CACHE_KEYS.STORAGE + key;
      await AsyncStorage.setItem(storageKey, JSON.stringify(entry));

      this.metadata.totalEntries++;
      await this.saveMetadata();

      // Cleanup old entries periodically
      if (this.shouldCleanup()) {
        this.cleanup();
      }
    } catch (error) {
      console.error('Error setting storage cache:', error);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const storageKey = CACHE_KEYS.STORAGE + key;
      const cached = await AsyncStorage.getItem(storageKey);

      if (!cached) {
        this.metadata.missCount++;
        return null;
      }

      const entry: CacheEntry<T> = JSON.parse(cached);
      const now = Date.now();

      if (now > entry.expiresAt) {
        await AsyncStorage.removeItem(storageKey);
        this.metadata.missCount++;
        return null;
      }

      this.metadata.hitCount++;
      return entry.data;
    } catch (error) {
      console.error('Error getting storage cache:', error);
      this.metadata.missCount++;
      return null;
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      const storageKey = CACHE_KEYS.STORAGE + key;
      const cached = await AsyncStorage.getItem(storageKey);

      if (!cached) return false;

      const entry: CacheEntry = JSON.parse(cached);
      const now = Date.now();

      if (now > entry.expiresAt) {
        await AsyncStorage.removeItem(storageKey);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking storage cache:', error);
      return false;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      const storageKey = CACHE_KEYS.STORAGE + key;
      await AsyncStorage.removeItem(storageKey);
      this.metadata.totalEntries = Math.max(0, this.metadata.totalEntries - 1);
    } catch (error) {
      console.error('Error deleting storage cache:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(CACHE_KEYS.STORAGE));
      await AsyncStorage.multiRemove(cacheKeys);
      this.metadata.totalEntries = 0;
      await this.saveMetadata();
    } catch (error) {
      console.error('Error clearing storage cache:', error);
    }
  }

  private async saveMetadata(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        CACHE_KEYS.METADATA + 'storage',
        JSON.stringify(this.metadata)
      );
    } catch (error) {
      console.error('Error saving cache metadata:', error);
    }
  }

  private shouldCleanup(): boolean {
    const now = Date.now();
    const timeSinceLastCleanup = now - this.metadata.lastCleanup;
    const shouldCleanupByTime = timeSinceLastCleanup > 30 * 60 * 1000; // 30 minutes
    const shouldCleanupBySize = this.metadata.totalEntries > CACHE_CONFIG.MAX_STORAGE_CACHE_SIZE;

    return shouldCleanupByTime || shouldCleanupBySize;
  }

  private async cleanup(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(CACHE_KEYS.STORAGE));

      let removedCount = 0;
      for (const key of cacheKeys) {
        try {
          const cached = await AsyncStorage.getItem(key);
          if (cached) {
            const entry: CacheEntry = JSON.parse(cached);
            const now = Date.now();

            if (now > entry.expiresAt) {
              await AsyncStorage.removeItem(key);
              removedCount++;
            }
          }
        } catch (error) {
          // Remove corrupted entries
          await AsyncStorage.removeItem(key);
          removedCount++;
        }
      }

      this.metadata.totalEntries = Math.max(0, this.metadata.totalEntries - removedCount);
      this.metadata.lastCleanup = Date.now();
      await this.saveMetadata();

      console.log(`Cache cleanup completed. Removed ${removedCount} expired entries.`);
    } catch (error) {
      console.error('Error during cache cleanup:', error);
    }
  }

  async getStats() {
    return {
      ...this.metadata,
      hitRate: this.metadata.hitCount / (this.metadata.hitCount + this.metadata.missCount) || 0,
    };
  }
}

// Query deduplication to prevent duplicate API calls
class QueryDeduplicator {
  private pendingQueries = new Map<string, Promise<any>>();

  async deduplicate<T>(key: string, queryFn: () => Promise<T>): Promise<T> {
    // If query is already pending, return the existing promise
    if (this.pendingQueries.has(key)) {
      return this.pendingQueries.get(key) as Promise<T>;
    }

    // Start new query
    const promise = queryFn().finally(() => {
      // Remove from pending queries when completed
      this.pendingQueries.delete(key);
    });

    this.pendingQueries.set(key, promise);
    return promise;
  }

  clear(): void {
    this.pendingQueries.clear();
  }

  getStats() {
    return {
      pendingQueries: this.pendingQueries.size,
    };
  }
}

// Main cache manager
class CacheManager {
  private memoryCache = new MemoryCache();
  private storageCache = new StorageCache();
  private queryDeduplicator = new QueryDeduplicator();

  // Get data with multi-level caching
  async get<T>(key: string, fetchFn?: () => Promise<T>, ttl?: number): Promise<T | null> {
    // Try memory cache first
    const memoryResult = this.memoryCache.get<T>(key);
    if (memoryResult !== null) {
      return memoryResult;
    }

    // Try storage cache
    const storageResult = await this.storageCache.get<T>(key);
    if (storageResult !== null) {
      // Populate memory cache
      this.memoryCache.set(key, storageResult, ttl || CACHE_CONFIG.DEFAULT);
      return storageResult;
    }

    // If no fetchFn provided, return null
    if (!fetchFn) return null;

    // Fetch fresh data with deduplication
    try {
      const freshData = await this.queryDeduplicator.deduplicate(key, fetchFn);

      // Cache in both memory and storage
      const cacheTtl = ttl || CACHE_CONFIG.DEFAULT;
      this.memoryCache.set(key, freshData, cacheTtl);
      await this.storageCache.set(key, freshData, cacheTtl);

      return freshData;
    } catch (error) {
      console.error(`Error fetching data for key ${key}:`, error);
      return null;
    }
  }

  // Set data in both caches
  async set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.DEFAULT): Promise<void> {
    this.memoryCache.set(key, data, ttl);
    await this.storageCache.set(key, data, ttl);
  }

  // Check if data exists in cache
  async has(key: string): Promise<boolean> {
    return this.memoryCache.has(key) || await this.storageCache.has(key);
  }

  // Delete from both caches
  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    await this.storageCache.delete(key);
  }

  // Clear all caches
  async clear(): Promise<void> {
    this.memoryCache.clear();
    await this.storageCache.clear();
    this.queryDeduplicator.clear();
  }

  // Invalidate cache by pattern
  async invalidatePattern(pattern: string): Promise<void> {
    // For memory cache
    const memoryKeys = Array.from(this.memoryCache['cache'].keys());
    for (const key of memoryKeys) {
      if (key.includes(pattern)) {
        this.memoryCache.delete(key);
      }
    }

    // For storage cache
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key =>
        key.startsWith(CACHE_KEYS.STORAGE) && key.includes(pattern)
      );
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Error invalidating cache pattern:', error);
    }
  }

  // Get cache statistics
  async getStats() {
    const memoryStats = this.memoryCache.getStats();
    const storageStats = await this.storageCache.getStats();
    const deduplicatorStats = this.queryDeduplicator.getStats();

    return {
      memory: memoryStats,
      storage: storageStats,
      deduplicator: deduplicatorStats,
    };
  }
}

// Export singleton instance
export const cacheManager = new CacheManager();

// Export cache configuration and keys for use in other modules
export { CACHE_CONFIG, CACHE_KEYS };

// Helper functions for common cache operations
export const CacheHelpers = {
  // Generate cache key for user data
  userKey: (userId: string) => `${CACHE_KEYS.USER}${userId}`,

  // Generate cache key for teacher stats
  teacherStatsKey: (teacherId: string) => `${CACHE_KEYS.TEACHER_STATS}${teacherId}`,

  // Generate cache key for classes
  classesKey: (teacherId: string) => `${CACHE_KEYS.CLASSES}${teacherId}`,

  // Generate cache key for students
  studentsKey: (classId: string) => `${CACHE_KEYS.STUDENTS}${classId}`,

  // Generate cache key for assignments
  assignmentsKey: (teacherId: string) => `${CACHE_KEYS.ASSIGNMENTS}${teacherId}`,

  // Generate cache key for attendance
  attendanceKey: (classId: string, date: string) => `${CACHE_KEYS.ATTENDANCE}${classId}_${date}`,

  // Generate cache key for notices
  noticesKey: (tenantId: string) => `${CACHE_KEYS.NOTICES}${tenantId}`,

  // Generate cache key for enrollment requests
  enrollmentRequestsKey: (teacherId: string) => `${CACHE_KEYS.ENROLLMENT_REQUESTS}${teacherId}`,

  // Generate cache key for class students
  classStudentsKey: (classId: string) => `${CACHE_KEYS.CLASS_STUDENTS}${classId}`,
};
