import React from 'react';
import { View, Text } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface CompletionData {
  totalStudents: number;
  submitted: number;
  graded: number;
  pending: number;
  late: number;
  missing: number;
}

interface CompletionRatesProps {
  data: CompletionData;
  dueDate?: string;
}

export default function CompletionRates({ data, dueDate }: CompletionRatesProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const completionRate = (data.submitted / data.totalStudents) * 100;
  const gradingRate = (data.graded / data.submitted) * 100;
  const onTimeRate = ((data.submitted - data.late) / data.submitted) * 100;

  const renderProgressRing = (percentage: number, color: string, size: number = 80) => {
    const radius = (size - 8) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    return (
      <View className="items-center justify-center" style={{ width: size, height: size }}>
        <View className="absolute">
          <View
            className="rounded-full border-4 border-gray-200 dark:border-gray-700"
            style={{ width: size, height: size }}
          />
          <View
            className={`absolute rounded-full border-4 ${color} transform -rotate-90`}
            style={{
              width: size,
              height: size,
              borderTopColor: 'transparent',
              borderRightColor: 'transparent',
              borderBottomColor: 'transparent',
            }}
          />
        </View>
        <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {percentage.toFixed(0)}%
        </Text>
      </View>
    );
  };

  const renderStatusCard = (
    title: string,
    count: number,
    total: number,
    color: string,
    icon: string
  ) => (
    <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
      <View className="flex-row items-center justify-between mb-2">
        <Ionicons name={icon as any} size={24} color={color} />
        <Text className={`font-rubik-bold text-xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {count}
        </Text>
      </View>
      <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        {title}
      </Text>
      <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
        {((count / total) * 100).toFixed(1)}% of total
      </Text>
    </View>
  );

  return (
    <View className="space-y-6">
      {/* Overview Cards */}
      <Animated.View entering={FadeInDown.delay(100).duration(400)}>
        <View className="grid grid-cols-2 gap-4">
          {renderStatusCard('Submitted', data.submitted, data.totalStudents, '#10B981', 'checkmark-circle-outline')}
          {renderStatusCard('Graded', data.graded, data.totalStudents, '#3B82F6', 'star-outline')}
          {renderStatusCard('Pending', data.pending, data.totalStudents, '#F59E0B', 'time-outline')}
          {renderStatusCard('Missing', data.missing, data.totalStudents, '#EF4444', 'close-circle-outline')}
        </View>
      </Animated.View>

      {/* Progress Rings */}
      <Animated.View entering={FadeInDown.delay(200).duration(400)}>
        <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`font-rubik-bold text-lg mb-6 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Completion Progress
          </Text>

          <View className="flex-row justify-around items-center">
            <View className="items-center">
              {renderProgressRing(completionRate, 'border-green-500')}
              <Text className={`font-rubik-medium mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Completion
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {data.submitted}/{data.totalStudents}
              </Text>
            </View>

            <View className="items-center">
              {renderProgressRing(gradingRate, 'border-blue-500')}
              <Text className={`font-rubik-medium mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Graded
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {data.graded}/{data.submitted}
              </Text>
            </View>

            <View className="items-center">
              {renderProgressRing(onTimeRate, 'border-purple-500')}
              <Text className={`font-rubik-medium mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                On Time
              </Text>
              <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                {data.submitted - data.late}/{data.submitted}
              </Text>
            </View>
          </View>
        </View>
      </Animated.View>

      {/* Detailed Breakdown */}
      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
          <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Submission Breakdown
          </Text>

          <View className="space-y-4">
            {/* Submitted on Time */}
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="w-4 h-4 bg-green-500 rounded mr-3" />
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Submitted on Time
                </Text>
              </View>
              <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {data.submitted - data.late}
              </Text>
            </View>

            {/* Late Submissions */}
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="w-4 h-4 bg-orange-500 rounded mr-3" />
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Late Submissions
                </Text>
              </View>
              <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {data.late}
              </Text>
            </View>

            {/* Pending Grading */}
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="w-4 h-4 bg-yellow-500 rounded mr-3" />
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Pending Grading
                </Text>
              </View>
              <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {data.pending}
              </Text>
            </View>

            {/* Not Submitted */}
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="w-4 h-4 bg-red-500 rounded mr-3" />
                <Text className={`font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Not Submitted
                </Text>
              </View>
              <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {data.missing}
              </Text>
            </View>
          </View>

          {/* Progress Bar */}
          <View className="mt-4">
            <View className="flex-row h-4 rounded-lg overflow-hidden">
              <View 
                className="bg-green-500" 
                style={{ width: `${((data.submitted - data.late) / data.totalStudents) * 100}%` }}
              />
              <View 
                className="bg-orange-500" 
                style={{ width: `${(data.late / data.totalStudents) * 100}%` }}
              />
              <View 
                className="bg-gray-300 dark:bg-gray-600" 
                style={{ width: `${(data.missing / data.totalStudents) * 100}%` }}
              />
            </View>
          </View>
        </View>
      </Animated.View>

      {/* Due Date Info */}
      {dueDate && (
        <Animated.View entering={FadeInDown.delay(400).duration(400)}>
          <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <View className="flex-row items-center">
              <Ionicons name="calendar-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Due: {new Date(dueDate).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </View>
          </View>
        </Animated.View>
      )}
    </View>
  );
}
