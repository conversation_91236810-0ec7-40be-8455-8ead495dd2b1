import AssignmentCreationWizard, { AssignmentData } from "@/components/assignment/AssignmentCreationWizard";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useAssignmentStore } from "@/stores/assignmentStore";
import { useRouter } from "expo-router";
import React from "react";
import { Alert } from "react-native";

export default function CreateEnhancedAssignment() {
  const router = useRouter();
  const { createAssignmentWithRubrics } = useAssignmentStore();
  const { clerkUser, supabaseUser } = useSupabaseAuth();

  const handleComplete = async (assignmentData: AssignmentData) => {
    if (!clerkUser?.id) {
      throw new Error("Not authenticated");
    }

    if (!supabaseUser?.id) {
      throw new Error("Supabase user not found");
    }

    try {
      // Convert the wizard data to the format expected by the store
      const assignmentPayload = {
        title: assignmentData.title,
        description: assignmentData.description,
        instructions: assignmentData.instructions,
        due_date: assignmentData.due_date.toISOString(),
        max_points: assignmentData.max_points,
        class_id: assignmentData.class_id,
        status: assignmentData.status,
        gemini_generated: assignmentData.gemini_generated,
        attachment_urls: assignmentData.attachment_urls,
        clerk_user_id: clerkUser.id,
        teacher_id: supabaseUser.id,
        // Additional settings
        allow_late_submissions: assignmentData.allow_late_submissions,
        show_grades_immediately: assignmentData.show_grades_immediately,
      };

      // Create the assignment with rubrics
      const newAssignment = await createAssignmentWithRubrics(
        assignmentPayload,
        assignmentData.rubrics
      );

      if (!newAssignment) {
        throw new Error("Failed to create assignment");
      }

      Alert.alert(
        "Success",
        assignmentData.status === 'published' 
          ? "Assignment published successfully! Students can now see and submit to it."
          : "Assignment saved as draft successfully! You can publish it later.",
        [
          {
            text: "OK",
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error("Error creating assignment:", error);
      throw error; // Re-throw to be handled by the wizard
    }
  };

  const handleCancel = () => {
    Alert.alert(
      "Cancel Assignment Creation",
      "Are you sure you want to cancel? All progress will be lost.",
      [
        { text: "Continue Editing", style: "cancel" },
        {
          text: "Cancel",
          style: "destructive",
          onPress: () => router.back(),
        },
      ]
    );
  };

  return (
    <AssignmentCreationWizard
      onComplete={handleComplete}
      onCancel={handleCancel}
    />
  );
}
