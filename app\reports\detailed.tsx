import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@clerk/clerk-expo';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { RefreshControl, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Stores
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useEnrollmentStore } from '@/stores/enrollmentStore';

interface StudentAttendanceReport {
  student_id: string;
  student_name: string;
  total_sessions: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  excused_count: number;
  attendance_percentage: number;
}

const DetailedReportScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { classId } = useLocalSearchParams();
  const { userId: clerkUserId } = useAuth();

  const {
    sessions,
    isLoading,
    error,
    loadSessions,
    clearError
  } = useAttendanceStore();

  const { currentTeacher, availableClasses, classStudents, loadTeacherData, loadAvailableClasses, loadClassStudents } = useEnrollmentStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'attendance' | 'sessions'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedClass, setSelectedClass] = useState<string>(classId as string || '');
  const [refreshing, setRefreshing] = useState(false);

  // Load teacher data
  useEffect(() => {
    if (clerkUserId && !currentTeacher) {
      loadTeacherData(clerkUserId);
    }
  }, [clerkUserId, currentTeacher, loadTeacherData]);

  useEffect(() => {
    if (currentTeacher) {
      loadAvailableClasses(currentTeacher.id);
      loadSessions(currentTeacher.id, selectedClass || undefined);
    }
  }, [currentTeacher, loadAvailableClasses, loadSessions]);

  // Auto-select first class if none is selected and classes are available
  useEffect(() => {
    if (availableClasses.length > 0 && !selectedClass) {
      setSelectedClass(availableClasses[0].id);
    }
  }, [availableClasses, selectedClass]);

  useEffect(() => {
    if (selectedClass) {
      loadClassStudents(selectedClass);
    }
  }, [selectedClass, loadClassStudents]);

  const generateStudentReports = (): StudentAttendanceReport[] => {
    if (!selectedClass || classStudents.length === 0 || sessions.length === 0) {
      return [];
    }

    const classSessions = sessions.filter(session => session.class_id === selectedClass);

    return classStudents.map(classStudent => {
      const student = classStudent.student;
      if (!student) return null;

      const attendanceRecords = classSessions.flatMap(session =>
        (session as any).attendance_records?.filter((record: any) => record.student_id === student.id) || []
      );

      const present_count = attendanceRecords.filter(record => record.status === 'present').length;
      const absent_count = attendanceRecords.filter(record => record.status === 'absent').length;
      const late_count = attendanceRecords.filter(record => record.status === 'late').length;
      const excused_count = attendanceRecords.filter(record => record.status === 'excused').length;
      const total_sessions = attendanceRecords.length;

      const attendance_percentage = total_sessions > 0 ? (present_count / total_sessions) * 100 : 0;

      return {
        student_id: student.id,
        student_name: (student as any).full_name || student.name || 'Unknown Student',
        total_sessions,
        present_count,
        absent_count,
        late_count,
        excused_count,
        attendance_percentage
      };
    }).filter(Boolean) as StudentAttendanceReport[];
  };

  const getFilteredAndSortedReports = () => {
    let reports = generateStudentReports();

    // Filter by search query
    if (searchQuery) {
      reports = reports.filter(report =>
        report.student_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort reports
    reports.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.student_name.localeCompare(b.student_name);
          break;
        case 'attendance':
          comparison = a.attendance_percentage - b.attendance_percentage;
          break;
        case 'sessions':
          comparison = a.total_sessions - b.total_sessions;
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return reports;
  };

  const getAttendanceColor = (percentage: number) => {
    if (percentage >= 90) return 'text-success';
    if (percentage >= 75) return 'text-warning';
    return 'text-error';
  };

  const getClassStats = () => {
    const reports = generateStudentReports();
    if (reports.length === 0) return null;

    const totalStudents = reports.length;
    const averageAttendance = reports.reduce((sum, report) => sum + report.attendance_percentage, 0) / totalStudents;
    const highAttendance = reports.filter(report => report.attendance_percentage >= 90).length;
    const lowAttendance = reports.filter(report => report.attendance_percentage < 75).length;

    return {
      totalStudents,
      averageAttendance,
      highAttendance,
      lowAttendance
    };
  };

  const handleSort = (newSortBy: 'name' | 'attendance' | 'sessions') => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
  };

  const handleRefresh = async () => {
    if (currentTeacher) {
      setRefreshing(true);
      try {
        await loadAvailableClasses(currentTeacher.id);
        await loadSessions(currentTeacher.id, selectedClass || undefined);
        if (selectedClass) {
          await loadClassStudents(selectedClass);
        }
      } catch (err) {
        console.error('Error refreshing data:', err);
      } finally {
        setRefreshing(false);
      }
    }
  };

  const handleClassSelect = (classId: string) => {
    setSelectedClass(classId);
    setSearchQuery(''); // Clear search when switching classes
  };

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen message="Loading detailed report..." />;
  }

  // Show error screen
  if (error) {
    return (
      <ErrorScreen
        title="Report Error"
        message={error}
        onRetry={() => {
          clearError();
          if (currentTeacher) {
            loadSessions(currentTeacher.id, selectedClass || undefined);
          }
        }}
      />
    );
  }

  if (!currentTeacher) {
    return (
      <ErrorScreen
        title="Teacher Not Found"
        message="Unable to load teacher information."
        onRetry={() => router.back()}
      />
    );
  }

  const filteredReports = getFilteredAndSortedReports();
  const classStats = getClassStats();

  return (
    <SecurityErrorBoundary>
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`p-4 pt-6 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <View className="flex-row items-center justify-between mb-4">
            <TouchableOpacity onPress={() => router.back()}>
              <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
            <Text className={`text-lg font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Detailed Report
            </Text>
            <TouchableOpacity onPress={() => {}}>
              <IconSymbol name="square.and.arrow.up" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>

          {/* Class Selector */}
          {availableClasses.length > 0 ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} className="flex-row gap-2 mb-4">
              {availableClasses.map((cls) => (
                <TouchableOpacity
                  key={cls.id}
                  onPress={() => handleClassSelect(cls.id)}
                  className={`px-4 py-2 rounded-lg ${
                    selectedClass === cls.id ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                  }`}
                >
                  <Text className={`font-rubik-medium ${
                    selectedClass === cls.id ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                  }`}>
                    {cls.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          ) : (
            <View className={`p-3 rounded-lg mb-4 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
              <Text className={`text-center font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                No classes available. Please create classes first.
              </Text>
            </View>
          )}

          {/* Search Bar */}
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search students..."
            placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
            className={`p-3 rounded-lg font-rubik border ${
              isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
            }`}
          />
        </View>

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={isDark ? '#60A5FA' : '#2563EB'}
            />
          }
        >
          {/* Class Statistics */}
          {classStats && (
            <View className={`m-4 p-6 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
              <Text className={`font-rubik-bold text-lg mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Class Overview
              </Text>

              <View className="flex-row flex-wrap gap-4">
                <View className="flex-1 min-w-[100px]">
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Students
                  </Text>
                  <Text className={`font-rubik-bold text-xl text-primary-500`}>
                    {classStats.totalStudents}
                  </Text>
                </View>

                <View className="flex-1 min-w-[100px]">
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Avg Attendance
                  </Text>
                  <Text className={`font-rubik-bold text-xl ${getAttendanceColor(classStats.averageAttendance)}`}>
                    {classStats.averageAttendance.toFixed(1)}%
                  </Text>
                </View>

                <View className="flex-1 min-w-[100px]">
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    High (≥90%)
                  </Text>
                  <Text className={`font-rubik-bold text-xl text-success`}>
                    {classStats.highAttendance}
                  </Text>
                </View>

                <View className="flex-1 min-w-[100px]">
                  <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Low (&lt;75%)
                  </Text>
                  <Text className={`font-rubik-bold text-xl text-error`}>
                    {classStats.lowAttendance}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Sort Controls */}
          <View className="mx-4 mb-4">
            <View className="flex-row gap-2">
              <TouchableOpacity
                onPress={() => handleSort('name')}
                className={`px-3 py-2 rounded-lg flex-row items-center ${
                  sortBy === 'name' ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-medium text-sm ${
                  sortBy === 'name' ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Name
                </Text>
                {sortBy === 'name' && (
                  <Text className="text-white ml-1 text-xs">
                    {sortOrder === 'asc' ? '↑' : '↓'}
                  </Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => handleSort('attendance')}
                className={`px-3 py-2 rounded-lg flex-row items-center ${
                  sortBy === 'attendance' ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-medium text-sm ${
                  sortBy === 'attendance' ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Attendance
                </Text>
                {sortBy === 'attendance' && (
                  <Text className="text-white ml-1 text-xs">
                    {sortOrder === 'asc' ? '↑' : '↓'}
                  </Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => handleSort('sessions')}
                className={`px-3 py-2 rounded-lg flex-row items-center ${
                  sortBy === 'sessions' ? 'bg-primary-500' : isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
              >
                <Text className={`font-rubik-medium text-sm ${
                  sortBy === 'sessions' ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Sessions
                </Text>
                {sortBy === 'sessions' && (
                  <Text className="text-white ml-1 text-xs">
                    {sortOrder === 'asc' ? '↑' : '↓'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>

          {/* Student Reports */}
          <View className="mx-4 mb-4">
            {filteredReports.length > 0 ? (
              <View className="space-y-3">
                {filteredReports.map((report) => (
                  <View
                    key={report.student_id}
                    className={`p-4 rounded-lg ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}
                  >
                    <View className="flex-row items-center justify-between mb-3">
                      <Text className={`font-rubik-semibold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                        {report.student_name}
                      </Text>
                      <Text className={`font-rubik-bold text-lg ${getAttendanceColor(report.attendance_percentage)}`}>
                        {report.attendance_percentage.toFixed(1)}%
                      </Text>
                    </View>

                    <View className="flex-row justify-between">
                      <View className="items-center">
                        <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          Present
                        </Text>
                        <Text className="font-rubik-semibold text-success">
                          {report.present_count}
                        </Text>
                      </View>

                      <View className="items-center">
                        <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          Absent
                        </Text>
                        <Text className="font-rubik-semibold text-error">
                          {report.absent_count}
                        </Text>
                      </View>

                      <View className="items-center">
                        <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          Late
                        </Text>
                        <Text className="font-rubik-semibold text-warning">
                          {report.late_count}
                        </Text>
                      </View>

                      <View className="items-center">
                        <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          Excused
                        </Text>
                        <Text className="font-rubik-semibold text-info">
                          {report.excused_count}
                        </Text>
                      </View>

                      <View className="items-center">
                        <Text className={`font-rubik text-xs ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                          Total
                        </Text>
                        <Text className={`font-rubik-semibold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                          {report.total_sessions}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <View className={`py-12 items-center ${isDark ? 'bg-dark-surface' : 'bg-light-surface'} rounded-lg`}>
                <IconSymbol name="person.2.fill" size={48} color={isDark ? '#9CA3AF' : '#6B7280'} />
                <Text className={`text-center mt-4 font-rubik-medium ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                  {selectedClass ? 'No students found' : 'Select a class to view detailed reports'}
                </Text>
                {selectedClass && (
                  <Text className={`text-center mt-2 font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
                    Try adjusting your search or check if students are enrolled
                  </Text>
                )}
              </View>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </SecurityErrorBoundary>
  );
};

export default DetailedReportScreen;
