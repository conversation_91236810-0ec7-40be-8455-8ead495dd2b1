import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { TextInput, View } from 'react-native';
import { IconSymbol } from './IconSymbol';

interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
}

export function SearchBar({ placeholder = 'Search...', value, onChangeText }: SearchBarProps) {
  const isDark = useColorScheme() === 'dark';

  return (
    <View className={`flex-row items-center p-3 rounded-xl mb-3 ${
      isDark ? 'bg-dark-surface' : 'bg-light-surface'
    }`}>
      <IconSymbol name="magnifyingglass" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
      <TextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
        className={`flex-1 ml-3 font-rubik ${
          isDark ? 'text-dark-text' : 'text-light-text'
        }`}
      />
    </View>
  );
} 