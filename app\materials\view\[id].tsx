import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useMaterialStore } from '@/stores/materialStore';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

const MaterialViewScreen: React.FC = () => {
  const isDark = useColorScheme() === 'dark';
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { materials, sharedMaterials, incrementDownloadCount, deleteMaterial } = useMaterialStore();
  
  const [material, setMaterial] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      // Find material in both user's materials and shared materials
      const foundMaterial = [...materials, ...sharedMaterials].find(m => m.id === id);
      setMaterial(foundMaterial);
      setLoading(false);
    }
  }, [id, materials, sharedMaterials]);

  const handleDownload = async () => {
    if (!material) return;
    
    try {
      await incrementDownloadCount(material.id);
      
      if (material.file_url) {
        // In a real app, you would handle file download here
        Alert.alert('Download', 'File download would start here');
      } else {
        Alert.alert('No File', 'This material has no downloadable file');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to download material');
    }
  };

  const handleDelete = async () => {
    if (!material) return;

    Alert.alert(
      'Delete Material',
      'Are you sure you want to delete this material? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteMaterial(material.id);
              Alert.alert('Success', 'Material deleted successfully');
              router.back();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete material');
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <View className={`w-16 h-16 rounded-full items-center justify-center mb-4 ${
          isDark ? 'bg-primary-500/20' : 'bg-primary-50'
        }`}>
          <ActivityIndicator size="large" color="#3B82F6" />
        </View>
        <Text className={`text-lg font-rubik-semibold mb-2 ${
          isDark ? 'text-dark-text' : 'text-gray-900'
        }`}>
          Loading material...
        </Text>
        <Text className={`text-center ${
          isDark ? 'text-dark-textSecondary' : 'text-gray-600'
        }`}>
          Please wait while we fetch the content
        </Text>
      </SafeAreaView>
    );
  }

  if (!material) {
    return (
      <SafeAreaView className={`flex-1 justify-center items-center px-6 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <View className={`w-20 h-20 rounded-full items-center justify-center mb-6 ${
          isDark ? 'bg-gray-500/20' : 'bg-gray-100'
        }`}>
          <IconSymbol name="doc.text" size={40} color={isDark ? '#6B7280' : '#9CA3AF'} />
        </View>
        <Text className={`text-2xl font-rubik-bold mb-3 ${
          isDark ? 'text-dark-text' : 'text-gray-900'
        }`}>
          Material Not Found
        </Text>
        <Text className={`text-center text-base mb-6 ${
          isDark ? 'text-dark-textSecondary' : 'text-gray-600'
        }`}>
          The material you're looking for doesn't exist or has been removed.
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          className="bg-primary-500 px-8 py-4 rounded-2xl flex-row items-center"
          style={{
            shadowColor: '#3B82F6',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 6,
          }}
        >
          <IconSymbol name="arrow.left" size={16} color="#FFFFFF" />
          <Text className="text-white font-rubik-semibold ml-2">Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 pb-2">
        <View className="flex-row items-center flex-1">
          <TouchableOpacity
            onPress={() => router.back()}
            className="mr-4 p-2 rounded-lg"
            style={{ backgroundColor: isDark ? '#374151' : '#F3F4F6' }}
          >
            <IconSymbol
              name="arrow.left"
              size={20}
              color={isDark ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className={`text-xl font-rubik-bold ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`} numberOfLines={2}>
              {material?.title || 'Loading...'}
            </Text>
            <Text className={`${
              isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
            }`}>
              {material?.material_type?.replace('_', ' ').toUpperCase() || ''}
            </Text>
          </View>
        </View>

        {material && (
          <View className="flex-row">
            <TouchableOpacity
              onPress={handleDownload}
              disabled={!material.file_url}
              className={`p-3 rounded-xl mr-2 ${
                material.file_url
                  ? 'bg-primary-500'
                  : isDark ? 'bg-gray-600' : 'bg-gray-300'
              }`}
              style={{
                opacity: material.file_url ? 1 : 0.5,
              }}
            >
              <IconSymbol
                name="arrow.down.circle.fill"
                size={20}
                color="#FFFFFF"
              />
            </TouchableOpacity>

            {/* Only show delete button for user's own materials */}
            {materials.some(m => m.id === material.id) && (
              <TouchableOpacity
                onPress={handleDelete}
                className="bg-red-500 p-3 rounded-xl"
              >
                <IconSymbol name="trash.fill" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {material ? (
          <View className="px-6">
            {/* Description */}
            {material.description && (
              <View className={`p-6 rounded-2xl mb-6 ${
                isDark ? 'bg-dark-surface' : 'bg-white'
              }`}
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: isDark ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: 4,
              }}>
                <View className="flex-row items-center mb-4">
                  <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${
                    isDark ? 'bg-blue-500/20' : 'bg-blue-50'
                  }`}>
                    <IconSymbol
                      name="info.circle.fill"
                      size={20}
                      color={isDark ? '#60A5FA' : '#3B82F6'}
                    />
                  </View>
                  <Text className={`font-rubik-bold text-xl ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    Description
                  </Text>
                </View>
                <Text className={`font-rubik text-base leading-6 ${
                  isDark ? 'text-dark-text' : 'text-gray-700'
                }`}>
                  {material.description}
                </Text>
              </View>
            )}

            {/* Tags and Metadata */}
            <View className={`p-6 rounded-2xl mb-6 ${
              isDark ? 'bg-dark-surface' : 'bg-white'
            }`}
            style={{
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: isDark ? 0.3 : 0.1,
              shadowRadius: 8,
              elevation: 4,
            }}>
              <View className="flex-row items-center mb-4">
                <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${
                  isDark ? 'bg-green-500/20' : 'bg-green-50'
                }`}>
                  <Text className={`text-lg ${isDark ? 'text-green-300' : 'text-green-700'}`}>
                    📋
                  </Text>
                </View>
                <Text className={`font-rubik-bold text-xl ${
                  isDark ? 'text-dark-text' : 'text-gray-900'
                }`}>
                  Details
                </Text>
              </View>

              <View className="flex-row flex-wrap mb-4">
                {material.subject && (
                  <View className={`px-3 py-2 rounded-full mr-2 mb-2 ${
                    isDark ? 'bg-emerald-500/20' : 'bg-emerald-50'
                  }`}>
                    <Text className={`text-sm font-rubik-semibold ${
                      isDark ? 'text-emerald-300' : 'text-emerald-700'
                    }`}>
                      📚 {material.subject}
                    </Text>
                  </View>
                )}
                {material.grade_level && (
                  <View className={`px-3 py-2 rounded-full mr-2 mb-2 ${
                    isDark ? 'bg-amber-500/20' : 'bg-amber-50'
                  }`}>
                    <Text className={`text-sm font-rubik-semibold ${
                      isDark ? 'text-amber-300' : 'text-amber-700'
                    }`}>
                      🎓 {material.grade_level}
                    </Text>
                  </View>
                )}
                {material.tags?.map((tag: string, index: number) => (
                  <View key={index} className={`px-3 py-2 rounded-full mr-2 mb-2 ${
                    isDark ? 'bg-gray-500/20' : 'bg-gray-100'
                  }`}>
                    <Text className={`text-sm font-rubik-semibold ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      #{tag}
                    </Text>
                  </View>
                ))}
              </View>

              <View className={`pt-4 border-t ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
                <View className="flex-row justify-between items-center mb-3">
                  <Text className={`font-rubik ${
                    isDark ? 'text-dark-textSecondary' : 'text-gray-600'
                  }`}>
                    Visibility
                  </Text>
                  <Text className={`font-rubik-semibold ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    {material.visibility.charAt(0).toUpperCase() + material.visibility.slice(1)}
                  </Text>
                </View>
                <View className="flex-row justify-between items-center">
                  <Text className={`font-rubik ${
                    isDark ? 'text-dark-textSecondary' : 'text-gray-600'
                  }`}>
                    Created
                  </Text>
                  <Text className={`font-rubik-semibold ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    {new Date(material.created_at).toLocaleDateString()}
                  </Text>
                </View>
                {material.uploaded_by_name && (
                  <View className="flex-row justify-between items-center mt-3">
                    <Text className={`font-rubik ${
                      isDark ? 'text-dark-textSecondary' : 'text-gray-600'
                    }`}>
                      Created by
                    </Text>
                    <Text className={`font-rubik-semibold ${
                      isDark ? 'text-dark-text' : 'text-gray-900'
                    }`}>
                      {material.uploaded_by_name}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Content */}
            {material.content && (
              <View className={`p-6 rounded-2xl mb-6 ${
                isDark ? 'bg-dark-surface' : 'bg-white'
              }`}
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: isDark ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: 4,
              }}>
                <View className="flex-row items-center mb-4">
                  <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${
                    isDark ? 'bg-purple-500/20' : 'bg-purple-50'
                  }`}>
                    <IconSymbol
                      name="doc.text.fill"
                      size={20}
                      color={isDark ? '#A78BFA' : '#8B5CF6'}
                    />
                  </View>
                  <Text className={`font-rubik-bold text-xl ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    Content
                  </Text>
                </View>
                <View className={`p-4 rounded-xl ${
                  isDark ? 'bg-dark-background' : 'bg-gray-50'
                }`}>
                  <Text className={`font-rubik text-base leading-7 ${
                    isDark ? 'text-dark-text' : 'text-gray-700'
                  }`}>
                    {material.content}
                  </Text>
                </View>
              </View>
            )}

            {/* File Info */}
            {material.file_url && (
              <View className={`p-6 rounded-2xl mb-6 ${
                isDark ? 'bg-dark-surface' : 'bg-white'
              }`}
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: isDark ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: 4,
              }}>
                <View className="flex-row items-center mb-4">
                  <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${
                    isDark ? 'bg-orange-500/20' : 'bg-orange-50'
                  }`}>
                    <IconSymbol
                      name="paperclip"
                      size={20}
                      color={isDark ? '#FB923C' : '#EA580C'}
                    />
                  </View>
                  <Text className={`font-rubik-bold text-xl ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    Attached File
                  </Text>
                </View>

                <View className={`p-4 rounded-xl border-2 border-dashed ${
                  isDark ? 'border-gray-600 bg-dark-background' : 'border-gray-300 bg-gray-50'
                }`}>
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center flex-1">
                      <View className={`w-12 h-12 rounded-xl items-center justify-center mr-4 ${
                        isDark ? 'bg-primary-500/20' : 'bg-primary-50'
                      }`}>
                        <IconSymbol name="doc.text.fill" size={24} color={isDark ? '#60A5FA' : '#3B82F6'} />
                      </View>
                      <View className="flex-1">
                        <Text className={`font-rubik-semibold text-base ${
                          isDark ? 'text-dark-text' : 'text-gray-900'
                        }`}>
                          {material.file_type ? `${material.file_type.toUpperCase()} File` : 'Attached File'}
                        </Text>
                        {material.file_size && (
                          <Text className={`text-sm mt-1 ${
                            isDark ? 'text-dark-textSecondary' : 'text-gray-600'
                          }`}>
                            📁 {(material.file_size / 1024 / 1024).toFixed(2)} MB
                          </Text>
                        )}
                      </View>
                    </View>
                    <TouchableOpacity
                      onPress={handleDownload}
                      className="bg-primary-500 px-4 py-3 rounded-xl flex-row items-center"
                      style={{
                        shadowColor: '#3B82F6',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.3,
                        shadowRadius: 4,
                        elevation: 3,
                      }}
                    >
                      <IconSymbol name="arrow.down.circle" size={16} color="#FFFFFF" />
                      <Text className="text-white font-rubik-semibold ml-2">Download</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}

          </View>
        ) : (
          <View className="flex-1 justify-center items-center px-6">
            <View className={`w-16 h-16 rounded-full items-center justify-center mb-4 ${
              isDark ? 'bg-primary-500/20' : 'bg-primary-50'
            }`}>
              <ActivityIndicator size="large" color="#3B82F6" />
            </View>
            <Text className={`text-lg font-rubik-semibold mb-2 ${
              isDark ? 'text-dark-text' : 'text-gray-900'
            }`}>
              Loading material...
            </Text>
            <Text className={`text-center ${
              isDark ? 'text-dark-textSecondary' : 'text-gray-600'
            }`}>
              Please wait while we fetch the content
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default MaterialViewScreen;
