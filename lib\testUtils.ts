import { supabase } from './supabase';
import { Alert } from 'react-native';

export interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'pending';
  message: string;
  duration?: number;
  error?: string;
}

export interface TestSuite {
  name: string;
  tests: TestResult[];
  status: 'pass' | 'fail' | 'running' | 'pending';
  duration?: number;
}

// Test runner utility
export class AssignmentTestRunner {
  private results: TestSuite[] = [];
  private currentSuite: TestSuite | null = null;

  async runTest(name: string, testFn: () => Promise<void>): Promise<TestResult> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      return {
        name,
        status: 'pass',
        message: 'Test passed successfully',
        duration,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        name,
        status: 'fail',
        message: 'Test failed',
        duration,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  startSuite(name: string): void {
    this.currentSuite = {
      name,
      tests: [],
      status: 'running',
    };
  }

  async addTest(name: string, testFn: () => Promise<void>): Promise<void> {
    if (!this.currentSuite) {
      throw new Error('No test suite started');
    }

    const result = await this.runTest(name, testFn);
    this.currentSuite.tests.push(result);
  }

  finishSuite(): TestSuite {
    if (!this.currentSuite) {
      throw new Error('No test suite to finish');
    }

    const suite = this.currentSuite;
    const failedTests = suite.tests.filter(t => t.status === 'fail');
    suite.status = failedTests.length > 0 ? 'fail' : 'pass';
    suite.duration = suite.tests.reduce((sum, test) => sum + (test.duration || 0), 0);

    this.results.push(suite);
    this.currentSuite = null;
    return suite;
  }

  getResults(): TestSuite[] {
    return this.results;
  }

  clear(): void {
    this.results = [];
    this.currentSuite = null;
  }
}

// Database test utilities
export const testDatabaseConnection = async (): Promise<void> => {
  const { data, error } = await supabase.from('users').select('count').limit(1);
  if (error) {
    throw new Error(`Database connection failed: ${error.message}`);
  }
};

export const testUserAuthentication = async (): Promise<void> => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) {
    throw new Error(`Authentication check failed: ${error.message}`);
  }
  if (!session?.user) {
    throw new Error('No authenticated user found');
  }
};

export const testTeacherRecord = async (): Promise<any> => {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session?.user) {
    throw new Error('No authenticated user');
  }

  // Get user record
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('clerk_user_id', session.user.id)
    .single();

  if (userError || !userData) {
    throw new Error('User record not found');
  }

  // Get teacher record
  const { data: teacherData, error: teacherError } = await supabase
    .from('teachers')
    .select('*')
    .eq('user_id', userData.id)
    .single();

  if (teacherError || !teacherData) {
    throw new Error('Teacher record not found');
  }

  return teacherData;
};

export const createTestClass = async (teacherId: string, tenantId: string): Promise<any> => {
  const testClassData = {
    name: `Test Class ${Date.now()}`,
    grade_level: '10',
    subject: 'Test Subject',
    teacher_id: teacherId,
    tenant_id: tenantId,
    description: 'Test class for assignment system testing',
  };

  const { data, error } = await supabase
    .from('classes')
    .insert([testClassData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test class: ${error.message}`);
  }

  return data;
};

export const createTestAssignment = async (classId: string, teacherId: string, tenantId: string): Promise<any> => {
  const testAssignmentData = {
    title: `Test Assignment ${Date.now()}`,
    description: 'This is a test assignment for system testing',
    instructions: 'Complete this test assignment to verify the system works correctly.',
    class_id: classId,
    teacher_id: teacherId,
    tenant_id: tenantId,
    max_points: 100,
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
    status: 'draft',
    assignment_type: 'essay',
    difficulty: 'medium',
    number_of_questions: 5,
  };

  const { data, error } = await supabase
    .from('assignments')
    .insert([testAssignmentData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test assignment: ${error.message}`);
  }

  return data;
};

export const createTestStudent = async (classId: string, tenantId: string): Promise<any> => {
  // Create a test user first
  const testUserData = {
    clerk_user_id: `test_student_${Date.now()}`,
    email: `test.student.${Date.now()}@example.com`,
    name: `Test Student ${Date.now()}`,
    role: 'student',
    tenant_id: tenantId,
  };

  const { data: userData, error: userError } = await supabase
    .from('users')
    .insert([testUserData])
    .select()
    .single();

  if (userError) {
    throw new Error(`Failed to create test user: ${userError.message}`);
  }

  // Create student record
  const testStudentData = {
    user_id: userData.id,
    tenant_id: tenantId,
    grade_level: '10',
    enrollment_date: new Date().toISOString(),
  };

  const { data: studentData, error: studentError } = await supabase
    .from('students')
    .insert([testStudentData])
    .select()
    .single();

  if (studentError) {
    throw new Error(`Failed to create test student: ${studentError.message}`);
  }

  // Add student to class
  const { error: classError } = await supabase
    .from('class_students')
    .insert([{
      class_id: classId,
      student_id: studentData.id,
      tenant_id: tenantId,
    }]);

  if (classError) {
    throw new Error(`Failed to add student to class: ${classError.message}`);
  }

  return { user: userData, student: studentData };
};

export const createTestSubmission = async (
  assignmentId: string,
  studentId: string,
  tenantId: string
): Promise<any> => {
  const testSubmissionData = {
    assignment_id: assignmentId,
    student_id: studentId,
    tenant_id: tenantId,
    content: 'This is a test submission content for testing the assignment system.',
    status: 'submitted',
    submitted_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from('assignment_submissions')
    .insert([testSubmissionData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test submission: ${error.message}`);
  }

  return data;
};

export const createTestRubric = async (assignmentId: string, tenantId: string): Promise<any> => {
  const testRubricData = {
    assignment_id: assignmentId,
    tenant_id: tenantId,
    criteria: 'Content Quality',
    description: 'Quality and relevance of the content',
    max_points: 25,
    performance_levels: [
      { level: 'Excellent', points: 25, description: 'Outstanding content quality' },
      { level: 'Good', points: 20, description: 'Good content quality' },
      { level: 'Fair', points: 15, description: 'Fair content quality' },
      { level: 'Poor', points: 10, description: 'Poor content quality' },
    ],
  };

  const { data, error } = await supabase
    .from('assignment_rubrics')
    .insert([testRubricData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test rubric: ${error.message}`);
  }

  return data;
};

export const cleanupTestData = async (testIds: {
  classIds?: string[];
  assignmentIds?: string[];
  studentIds?: string[];
  userIds?: string[];
  submissionIds?: string[];
  rubricIds?: string[];
}): Promise<void> => {
  try {
    // Clean up in reverse dependency order
    if (testIds.submissionIds?.length) {
      await supabase.from('assignment_submissions').delete().in('id', testIds.submissionIds);
    }
    
    if (testIds.rubricIds?.length) {
      await supabase.from('assignment_rubrics').delete().in('id', testIds.rubricIds);
    }
    
    if (testIds.assignmentIds?.length) {
      await supabase.from('assignments').delete().in('id', testIds.assignmentIds);
    }
    
    if (testIds.studentIds?.length) {
      await supabase.from('students').delete().in('id', testIds.studentIds);
    }
    
    if (testIds.classIds?.length) {
      await supabase.from('classes').delete().in('id', testIds.classIds);
    }
    
    if (testIds.userIds?.length) {
      await supabase.from('users').delete().in('id', testIds.userIds);
    }
  } catch (error) {
    console.warn('Cleanup warning:', error);
    // Don't throw on cleanup errors
  }
};

export const generateTestReport = (suites: TestSuite[]): string => {
  const totalTests = suites.reduce((sum, suite) => sum + suite.tests.length, 0);
  const passedTests = suites.reduce((sum, suite) => 
    sum + suite.tests.filter(t => t.status === 'pass').length, 0
  );
  const failedTests = totalTests - passedTests;
  const totalDuration = suites.reduce((sum, suite) => sum + (suite.duration || 0), 0);

  let report = `# Assignment System Test Report\n\n`;
  report += `**Summary:**\n`;
  report += `- Total Tests: ${totalTests}\n`;
  report += `- Passed: ${passedTests}\n`;
  report += `- Failed: ${failedTests}\n`;
  report += `- Success Rate: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%\n`;
  report += `- Total Duration: ${totalDuration}ms\n\n`;

  suites.forEach(suite => {
    report += `## ${suite.name} (${suite.status.toUpperCase()})\n`;
    report += `Duration: ${suite.duration || 0}ms\n\n`;
    
    suite.tests.forEach(test => {
      const status = test.status === 'pass' ? '✅' : '❌';
      report += `${status} **${test.name}** (${test.duration || 0}ms)\n`;
      if (test.status === 'fail' && test.error) {
        report += `   Error: ${test.error}\n`;
      }
      report += `   ${test.message}\n\n`;
    });
  });

  return report;
};
