import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';
import { AssignmentData } from '../AssignmentCreationWizard';

interface AttachmentsStepProps {
  data: AssignmentData;
  updateData: (updates: Partial<AssignmentData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

interface AttachmentItem {
  id: string;
  name: string;
  type: string;
  size: number;
  uri: string;
  uploading?: boolean;
}

export default function AttachmentsStep({ data, updateData, onNext, onPrev }: AttachmentsStepProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  
  const [attachments, setAttachments] = useState<AttachmentItem[]>([]);
  const [uploading, setUploading] = useState(false);

  const getFileIcon = (type: string) => {
    if (type.includes('image')) return 'image-outline';
    if (type.includes('pdf')) return 'document-text-outline';
    if (type.includes('video')) return 'videocam-outline';
    if (type.includes('audio')) return 'musical-notes-outline';
    return 'document-outline';
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDocumentPicker = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
        multiple: true,
      });

      if (!result.canceled && result.assets) {
        const newAttachments = result.assets.map(asset => ({
          id: Date.now().toString() + Math.random().toString(),
          name: asset.name,
          type: asset.mimeType || 'application/octet-stream',
          size: asset.size || 0,
          uri: asset.uri,
        }));

        setAttachments(prev => [...prev, ...newAttachments]);
        // In a real app, you would upload these files to Supabase Storage here
        // For now, we'll just store the URIs
        updateData({ 
          attachment_urls: [...data.attachment_urls, ...newAttachments.map(a => a.uri)] 
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  const handleImagePicker = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets) {
        const newAttachments = result.assets.map(asset => ({
          id: Date.now().toString() + Math.random().toString(),
          name: asset.fileName || `image_${Date.now()}.jpg`,
          type: asset.type === 'video' ? 'video/mp4' : 'image/jpeg',
          size: asset.fileSize || 0,
          uri: asset.uri,
        }));

        setAttachments(prev => [...prev, ...newAttachments]);
        updateData({ 
          attachment_urls: [...data.attachment_urls, ...newAttachments.map(a => a.uri)] 
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const removeAttachment = (id: string) => {
    const attachment = attachments.find(a => a.id === id);
    if (attachment) {
      setAttachments(prev => prev.filter(a => a.id !== id));
      updateData({ 
        attachment_urls: data.attachment_urls.filter(url => url !== attachment.uri) 
      });
    }
  };

  const renderAttachmentItem = (attachment: AttachmentItem) => (
    <Animated.View
      key={attachment.id}
      entering={FadeInRight.duration(300)}
      className={`flex-row items-center p-3 rounded-lg mb-2 ${
        isDark ? 'bg-dark-card' : 'bg-light-card'
      }`}
    >
      <View className={`w-12 h-12 rounded-lg items-center justify-center ${
        isDark ? 'bg-dark-background' : 'bg-light-background'
      }`}>
        <Ionicons
          name={getFileIcon(attachment.type)}
          size={24}
          color={isDark ? '#9CA3AF' : '#6B7280'}
        />
      </View>

      <View className="flex-1 ml-3">
        <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`} numberOfLines={1}>
          {attachment.name}
        </Text>
        <Text className={`font-rubik text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
          {formatFileSize(attachment.size)} • {attachment.type.split('/')[1]?.toUpperCase()}
        </Text>
      </View>

      {attachment.uploading ? (
        <ActivityIndicator size="small" color="#3B82F6" />
      ) : (
        <TouchableOpacity
          onPress={() => removeAttachment(attachment.id)}
          className="p-2"
        >
          <Ionicons name="trash-outline" size={20} color="#EF4444" />
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  return (
    <View className="flex-1 p-5">
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 20 }}>
        <Animated.View entering={FadeInDown.delay(100).duration(400)} className="space-y-6">
          {/* Header */}
          <View>
            <Text className={`text-xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Assignment Attachments
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Add files, images, or resources that students will need for this assignment.
            </Text>
          </View>

          {/* Upload Options */}
          <View className="space-y-4">
            <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Add Files
            </Text>

            <View className="flex-row space-x-4">
              <TouchableOpacity
                onPress={handleDocumentPicker}
                disabled={uploading}
                className={`flex-1 p-3 rounded-lg border-2 border-dashed items-center ${
                  isDark ? 'border-dark-border bg-dark-card' : 'border-light-border bg-light-card'
                }`}
              >
                <Ionicons
                  name="document-attach-outline"
                  size={28}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
                <Text className={`font-rubik-medium mt-1 text-center text-sm ${
                  isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Documents
                </Text>
                <Text className={`font-rubik text-xs text-center ${
                  isDark ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  PDF, DOC, TXT
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleImagePicker}
                disabled={uploading}
                className={`flex-1 p-3 rounded-lg border-2 border-dashed items-center ${
                  isDark ? 'border-dark-border bg-dark-card' : 'border-light-border bg-light-card'
                }`}
              >
                <Ionicons
                  name="image-outline"
                  size={28}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
                <Text className={`font-rubik-medium mt-1 text-center text-sm ${
                  isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Media
                </Text>
                <Text className={`font-rubik text-xs text-center ${
                  isDark ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Images, Videos
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Attachments List */}
          {attachments.length > 0 && (
            <View className="space-y-3">
              <View className="flex-row items-center justify-between">
                <Text className={`text-base font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Attached Files ({attachments.length})
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    Alert.alert(
                      'Remove All',
                      'Are you sure you want to remove all attachments?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Remove All',
                          style: 'destructive',
                          onPress: () => {
                            setAttachments([]);
                            updateData({ attachment_urls: [] });
                          },
                        },
                      ]
                    );
                  }}
                  className="p-2"
                >
                  <Text className="text-red-500 font-rubik-medium text-sm">Remove All</Text>
                </TouchableOpacity>
              </View>

              {attachments.map(renderAttachmentItem)}
            </View>
          )}

          {/* File Guidelines - Ultra Compact */}
          <View className={`p-2 rounded-lg mt-2 ${isDark ? 'bg-yellow-900/10' : 'bg-yellow-50/50'}`}>
            <View className="flex-row items-center">
              <Ionicons name="information-circle-outline" size={14} color="#F59E0B" />
              <Text className={`ml-2 font-rubik text-xs ${isDark ? 'text-yellow-400' : 'text-yellow-700'}`}>
                Max 50MB • PDF, DOC, JPG, PNG, MP4 • Available to all students
              </Text>
            </View>
          </View>

          {/* Skip Option - Compact */}
          {attachments.length === 0 && (
            <View className={`p-3 rounded-lg border mt-2 ${
              isDark ? 'border-dark-border bg-dark-card' : 'border-light-border bg-light-card'
            }`}>
              <Text className={`font-rubik text-center text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                No files added. Skip this step if none are needed.
              </Text>
            </View>
          )}
        </Animated.View>
      </ScrollView>


    </View>
  );
}
