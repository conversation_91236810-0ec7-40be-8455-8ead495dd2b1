import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { useEnrollmentStore } from '@/stores/enrollmentStore';
import { Material, useMaterialStore } from '@/stores/materialStore';
import { useAuth } from '@clerk/clerk-expo';
import * as DocumentPicker from 'expo-document-picker';
import { Stack, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Switch,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';

interface FormData {
  title: string;
  description: string;
  content: string;
  subject: string;
  grade_level: string;
  material_type: Material['material_type'];
  visibility: Material['visibility'];
  tags: string;
  file?: DocumentPicker.DocumentPickerAsset;
}

interface FormErrors {
  title?: string;
  description?: string;
  content?: string;
  subject?: string;
  material_type?: string;
}

const CreateMaterialScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { isLoaded } = useSupabaseAuth();
  const { userId: clerkUserId } = useAuth();

  const {
    createMaterial,
    uploadMaterialFile,
    generateMaterialWithAI,
    isSaving,
    isUploading,
    uploadProgress,
    error,
    clearError,
  } = useMaterialStore();

  const {
    currentTeacher,
    loadTeacherData,
  } = useEnrollmentStore();

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    content: '',
    subject: '',
    grade_level: '',
    material_type: 'resource',
    visibility: 'private',
    tags: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [useAI, setUseAI] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const [shouldNavigateBack, setShouldNavigateBack] = useState(false);

  // Load teacher data
  useEffect(() => {
    if (isLoaded && clerkUserId && !currentTeacher) {
      loadTeacherData(clerkUserId);
    }
  }, [isLoaded, clerkUserId, currentTeacher, loadTeacherData]);

  // Handle navigation after material creation
  useEffect(() => {
    if (shouldNavigateBack) {
      setShouldNavigateBack(false);

      // Add a small delay to ensure the alert is fully dismissed
      const navigationTimer = setTimeout(() => {
        try {
          console.log('Attempting navigation...');
          if (router.canGoBack()) {
            console.log('Going back...');
            router.back();
          } else {
            console.log('Replacing with /materials...');
            router.replace('/materials');
          }
        } catch (error) {
          console.error('Navigation error:', error);
          console.log('Fallback to home...');
          try {
            router.replace('/(tabs)/home');
          } catch (fallbackError) {
            console.error('Fallback navigation also failed:', fallbackError);
          }
        }
      }, 500); // 500ms delay

      return () => clearTimeout(navigationTimer);
    }
  }, [shouldNavigateBack, router]);

  const updateFormData = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.material_type) {
      newErrors.material_type = 'Material type is required';
    }

    if (!formData.content.trim() && !formData.file && !useAI) {
      newErrors.content = 'Content, file, or AI generation is required';
    }

    if (useAI && !aiPrompt.trim()) {
      newErrors.content = 'AI prompt is required when using AI generation';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'image/*'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        setFormData(prev => ({ ...prev, file: result.assets[0] }));
        setSelectedFileName(result.assets[0].name);
      }
    } catch (error) {
      console.error('Error selecting file:', error);
      Alert.alert('Error', 'Failed to select file');
    }
  };

  const handleRemoveFile = () => {
    setFormData(prev => ({ ...prev, file: undefined }));
    setSelectedFileName(null);
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (!currentTeacher?.id) {
      Alert.alert('Error', 'Teacher information not found');
      return;
    }

    try {
      clearError();

      let materialData: Omit<Material, 'id' | 'tenant_id' | 'created_at' | 'updated_at' | 'download_count' | 'view_count'>;

      if (useAI && aiPrompt.trim()) {
        // Generate material with AI
        const aiMaterial = await generateMaterialWithAI(
          aiPrompt,
          formData.material_type,
          formData.subject,
          formData.grade_level,
          currentTeacher.id
        );

        if (aiMaterial) {
          Alert.alert(
            '✨ Material Created with AI!',
            `Your material "${aiMaterial.title}" has been generated successfully!`,
            [
              {
                text: 'OK',
                onPress: () => setShouldNavigateBack(true)
              }
            ]
          );
          return;
        }
      } else {
        // Upload file if selected
        let fileUrl: string | null = null;
        let fileType: string | undefined;
        let fileSize: number | undefined;

        if (formData.file) {
          fileUrl = await uploadMaterialFile(formData.file);
          if (!fileUrl) {
            Alert.alert('Error', 'Failed to upload file');
            return;
          }
          fileType = formData.file.mimeType?.split('/')[1];
          fileSize = formData.file.size;
        }

        // Parse tags
        const tags = formData.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);

        materialData = {
          uploaded_by: currentTeacher.id,
          title: formData.title.trim(),
          description: formData.description.trim() || undefined,
          content: formData.content.trim() || undefined,
          file_url: fileUrl || undefined,
          file_type: fileType,
          file_size: fileSize,
          subject: formData.subject.trim() || undefined,
          grade_level: formData.grade_level.trim() || undefined,
          material_type: formData.material_type,
          visibility: formData.visibility,
          tags: tags.length > 0 ? tags : undefined,
          gemini_generated: false,
          is_active: true,
        };

        const material = await createMaterial(materialData);

        if (material) {
          Alert.alert(
            '✅ Material Created!',
            `Your material "${material.title}" has been created successfully!`,
            [
              {
                text: 'OK',
                onPress: () => setShouldNavigateBack(true)
              }
            ]
          );
        }
      }
    } catch (error) {
      console.error('Error creating material:', error);
      Alert.alert('Error', 'Failed to create material');
    }
  };

  const materialTypes = [
    { value: 'resource', label: 'Resource' },
    { value: 'lesson_plan', label: 'Lesson Plan' },
    { value: 'worksheet', label: 'Worksheet' },
    { value: 'quiz', label: 'Quiz' },
    { value: 'assignment', label: 'Assignment' },
    { value: 'presentation', label: 'Presentation' },
  ];

  const visibilityOptions = [
    { value: 'private', label: 'Private (Only me)' },
    { value: 'class', label: 'Class (My students)' },
    { value: 'school', label: 'School (All teachers)' },
    { value: 'public', label: 'Public (Everyone)' },
  ];

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <Stack.Screen options={{ headerShown: false }} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {/* Header */}
          <Animated.View
            entering={FadeInUp.duration(600)}
            className="flex-row items-center justify-between p-4 pb-2"
          >
            <View className="flex-row items-center">
              <TouchableOpacity
                onPress={() => router.back()}
                className="mr-4 p-2 rounded-lg"
                style={{ backgroundColor: isDark ? '#374151' : '#F3F4F6' }}
              >
                <IconSymbol
                  name="arrow.left"
                  size={20}
                  color={isDark ? '#FFFFFF' : '#000000'}
                />
              </TouchableOpacity>
              <View>
                <Text className={`text-2xl font-rubik-bold ${
                  isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Create Material
                </Text>
                <Text className={`${
                  isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                }`}>
                  Add new teaching material
                </Text>
              </View>
            </View>
          </Animated.View>

          <View className="p-4">
            {/* AI Generation Toggle */}
            <Animated.View
              entering={FadeInDown.duration(600)}
              className={`p-6 rounded-2xl mb-6 ${
                useAI
                  ? 'bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-200'
                  : isDark ? 'bg-dark-surface' : 'bg-white'
              }`}
              style={{
                shadowColor: useAI ? '#8B5CF6' : '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: useAI ? 0.2 : 0.1,
                shadowRadius: 12,
                elevation: 6,
              }}
            >
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-row items-center flex-1">
                  <View className={`w-12 h-12 rounded-full items-center justify-center mr-4 ${
                    useAI
                      ? 'bg-gradient-to-br from-purple-500 to-pink-500'
                      : isDark ? 'bg-purple-500/20' : 'bg-purple-50'
                  }`}>
                    <Text className={`text-lg ${useAI ? 'text-white' : isDark ? 'text-purple-300' : 'text-purple-600'}`}>
                      ✨
                    </Text>
                  </View>
                  <View className="flex-1">
                    <Text className={`font-rubik-bold text-lg ${
                      isDark ? 'text-dark-text' : 'text-gray-900'
                    }`}>
                      AI Generation
                    </Text>
                    <Text className={`text-sm ${
                      isDark ? 'text-dark-textSecondary' : 'text-gray-600'
                    }`}>
                      Let AI create content for you
                    </Text>
                  </View>
                </View>
                <Switch
                  value={useAI}
                  onValueChange={setUseAI}
                  trackColor={{ false: isDark ? '#374151' : '#E5E7EB', true: '#8B5CF6' }}
                  thumbColor={useAI ? '#FFFFFF' : '#F9FAFB'}
                  ios_backgroundColor={isDark ? '#374151' : '#E5E7EB'}
                />
              </View>

              {useAI && (
                <Animated.View entering={FadeInDown.duration(400)}>
                  <Text className={`font-rubik-semibold mb-3 ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    Describe what you want to create *
                  </Text>
                  <View className={`rounded-xl overflow-hidden ${
                    isDark ? 'bg-dark-background' : 'bg-gray-50'
                  }`}>
                    <TextInput
                      value={aiPrompt}
                      onChangeText={setAiPrompt}
                      placeholder="e.g., 'Create a math worksheet for grade 5 about fractions with 10 practice problems'"
                      placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                      multiline
                      numberOfLines={4}
                      className={`p-4 font-rubik text-base ${
                        isDark ? 'text-dark-text' : 'text-gray-900'
                      }`}
                      style={{ textAlignVertical: 'top' }}
                    />
                  </View>
                  <Text className={`text-xs mt-2 ${
                    isDark ? 'text-purple-300' : 'text-purple-600'
                  }`}>
                    💡 Be specific about the topic, grade level, and type of content you need
                  </Text>
                </Animated.View>
              )}
            </Animated.View>

            {/* Basic Information */}
            <Animated.View
              entering={FadeInDown.duration(700)}
              className={`p-6 rounded-2xl mb-6 ${
                isDark ? 'bg-dark-surface' : 'bg-white'
              }`}
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: isDark ? 0.3 : 0.1,
                shadowRadius: 12,
                elevation: 6,
              }}
            >
              <View className="flex-row items-center mb-6">
                <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${
                  isDark ? 'bg-blue-500/20' : 'bg-blue-50'
                }`}>
                  <IconSymbol
                    name="info.circle.fill"
                    size={20}
                    color={isDark ? '#60A5FA' : '#3B82F6'}
                  />
                </View>
                <Text className={`font-rubik-bold text-xl ${
                  isDark ? 'text-dark-text' : 'text-gray-900'
                }`}>
                  Basic Information
                </Text>
              </View>

              {/* Title */}
              <View className="mb-6">
                <Text className={`font-rubik-semibold mb-3 ${
                  isDark ? 'text-dark-text' : 'text-gray-900'
                }`}>
                  Title *
                </Text>
                <View className={`rounded-xl overflow-hidden ${
                  errors.title ? 'border-2 border-red-500' : ''
                }`}>
                  <TextInput
                    value={formData.title}
                    onChangeText={(value) => updateFormData('title', value)}
                    placeholder="Enter a descriptive title for your material"
                    placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                    className={`p-4 font-rubik text-base ${
                      isDark ? 'bg-dark-background text-dark-text' : 'bg-gray-50 text-gray-900'
                    }`}
                  />
                </View>
                {errors.title && (
                  <View className="flex-row items-center mt-2">
                    <IconSymbol name="exclamationmark.circle.fill" size={16} color="#EF4444" />
                    <Text className="text-red-500 text-sm font-rubik-medium ml-2">{errors.title}</Text>
                  </View>
                )}
              </View>

              {/* Description */}
              <View className="mb-6">
                <Text className={`font-rubik-semibold mb-3 ${
                  isDark ? 'text-dark-text' : 'text-gray-900'
                }`}>
                  Description
                </Text>
                <View className={`rounded-xl overflow-hidden ${
                  isDark ? 'bg-dark-background' : 'bg-gray-50'
                }`}>
                  <TextInput
                    value={formData.description}
                    onChangeText={(value) => updateFormData('description', value)}
                    placeholder="Provide a brief description of what this material covers..."
                    placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                    multiline
                    numberOfLines={3}
                    className={`p-4 font-rubik text-base ${
                      isDark ? 'text-dark-text' : 'text-gray-900'
                    }`}
                    style={{ textAlignVertical: 'top' }}
                  />
                </View>
              </View>

              {/* Material Type */}
              <View className="mb-6">
                <Text className={`font-rubik-semibold mb-3 ${
                  isDark ? 'text-dark-text' : 'text-gray-900'
                }`}>
                  Material Type *
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-2">
                  <View className="flex-row">
                    {materialTypes.map((type, index) => (
                      <TouchableOpacity
                        key={type.value}
                        onPress={() => updateFormData('material_type', type.value as Material['material_type'])}
                        className={`px-4 py-3 rounded-full mr-3 ${
                          formData.material_type === type.value
                            ? 'bg-primary-500'
                            : isDark ? 'bg-dark-background border border-gray-600' : 'bg-gray-100 border border-gray-200'
                        }`}
                        style={formData.material_type === type.value ? {
                          shadowColor: '#3B82F6',
                          shadowOffset: { width: 0, height: 2 },
                          shadowOpacity: 0.3,
                          shadowRadius: 4,
                          elevation: 3,
                        } : {}}
                      >
                        <Text className={`font-rubik-semibold ${
                          formData.material_type === type.value
                            ? 'text-white'
                            : isDark ? 'text-dark-text' : 'text-gray-700'
                        }`}>
                          {type.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
                {errors.material_type && (
                  <View className="flex-row items-center mt-2">
                    <IconSymbol name="exclamationmark.circle.fill" size={16} color="#EF4444" />
                    <Text className="text-red-500 text-sm font-rubik-medium ml-2">{errors.material_type}</Text>
                  </View>
                )}
              </View>

              {/* Subject and Grade Level */}
              <View className="flex-row mb-6">
                <View className="flex-1 mr-3">
                  <Text className={`font-rubik-semibold mb-3 ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    Subject
                  </Text>
                  <View className={`rounded-xl overflow-hidden ${
                    isDark ? 'bg-dark-background' : 'bg-gray-50'
                  }`}>
                    <TextInput
                      value={formData.subject}
                      onChangeText={(value) => updateFormData('subject', value)}
                      placeholder="Mathematics"
                      placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                      className={`p-4 font-rubik text-base ${
                        isDark ? 'text-dark-text' : 'text-gray-900'
                      }`}
                    />
                  </View>
                </View>
                <View className="flex-1 ml-3">
                  <Text className={`font-rubik-semibold mb-3 ${
                    isDark ? 'text-dark-text' : 'text-gray-900'
                  }`}>
                    Grade Level
                  </Text>
                  <View className={`rounded-xl overflow-hidden ${
                    isDark ? 'bg-dark-background' : 'bg-gray-50'
                  }`}>
                    <TextInput
                      value={formData.grade_level}
                      onChangeText={(value) => updateFormData('grade_level', value)}
                      placeholder="Grade 5"
                      placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                      className={`p-4 font-rubik text-base ${
                        isDark ? 'text-dark-text' : 'text-gray-900'
                      }`}
                    />
                  </View>
                </View>
              </View>

              {/* Tags */}
              <View className="mb-4">
                <Text className={`font-rubik-medium mb-2 ${
                  isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Tags
                </Text>
                <TextInput
                  value={formData.tags}
                  onChangeText={(value) => updateFormData('tags', value)}
                  placeholder="Enter tags separated by commas"
                  placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                  className={`p-4 rounded-lg font-rubik border ${
                    isDark ? 'bg-dark-background text-dark-text border-dark-border' : 'bg-light-background text-light-text border-light-border'
                  }`}
                />
                <Text className={`text-xs mt-1 ${
                  isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                }`}>
                  e.g., fractions, homework, practice
                </Text>
              </View>

              {/* Visibility */}
              <View>
                <Text className={`font-rubik-semibold mb-4 ${
                  isDark ? 'text-dark-text' : 'text-gray-900'
                }`}>
                  Visibility
                </Text>
                <View>
                  {visibilityOptions.map((option, index) => (
                    <TouchableOpacity
                      key={option.value}
                      onPress={() => updateFormData('visibility', option.value as Material['visibility'])}
                      className={`p-4 rounded-xl border flex-row items-center ${
                        index < visibilityOptions.length - 1 ? 'mb-4' : ''
                      } ${
                        formData.visibility === option.value
                          ? 'border-primary-500 bg-primary-50'
                          : isDark ? 'border-gray-600 bg-dark-background' : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      <View className={`w-5 h-5 rounded-full border-2 mr-4 ${
                        formData.visibility === option.value
                          ? 'border-primary-500 bg-primary-500'
                          : isDark ? 'border-gray-500' : 'border-gray-300'
                      }`}>
                        {formData.visibility === option.value && (
                          <View className="w-2.5 h-2.5 rounded-full bg-white m-auto" />
                        )}
                      </View>
                      <Text className={`font-rubik-semibold ${
                        formData.visibility === option.value
                          ? 'text-primary-700'
                          : isDark ? 'text-dark-text' : 'text-gray-700'
                      }`}>
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </Animated.View>

            {/* Content Section */}
            {!useAI && (
              <Animated.View
                entering={FadeInDown.duration(800)}
                className={`p-4 rounded-xl mb-6 ${
                  isDark ? 'bg-dark-surface' : 'bg-light-surface'
                }`}
                style={{
                  elevation: 2,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.1,
                  shadowRadius: 2,
                }}
              >
                <Text className={`font-rubik-bold text-lg mb-4 ${
                  isDark ? 'text-dark-text' : 'text-light-text'
                }`}>
                  Content
                </Text>

                {/* Text Content */}
                <View className="mb-4">
                  <Text className={`font-rubik-medium mb-2 ${
                    isDark ? 'text-dark-text' : 'text-light-text'
                  }`}>
                    Text Content
                  </Text>
                  <TextInput
                    value={formData.content}
                    onChangeText={(value) => updateFormData('content', value)}
                    placeholder="Enter the material content..."
                    placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                    multiline
                    numberOfLines={6}
                    className={`p-4 rounded-lg font-rubik border ${
                      errors.content
                        ? 'border-error'
                        : isDark ? 'bg-dark-background text-dark-text border-dark-border' : 'bg-light-background text-light-text border-light-border'
                    }`}
                    style={{ textAlignVertical: 'top' }}
                  />
                  {errors.content && (
                    <Text className="text-error text-sm font-rubik mt-1">{errors.content}</Text>
                  )}
                </View>

                {/* File Upload */}
                <View>
                  <Text className={`font-rubik-medium mb-2 ${
                    isDark ? 'text-dark-text' : 'text-light-text'
                  }`}>
                    File Attachment
                  </Text>
                  
                  {selectedFileName ? (
                    <View className={`p-4 rounded-lg border border-dashed flex-row items-center justify-between ${
                      isDark ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background'
                    }`}>
                      <View className="flex-row items-center flex-1">
                        <IconSymbol
                          name="doc.text.fill"
                          size={24}
                          color={isDark ? '#60A5FA' : '#3B82F6'}
                        />
                        <Text className={`ml-3 font-rubik-medium flex-1 ${
                          isDark ? 'text-dark-text' : 'text-light-text'
                        }`} numberOfLines={1}>
                          {selectedFileName}
                        </Text>
                      </View>
                      <TouchableOpacity
                        onPress={handleRemoveFile}
                        className="ml-2 p-1"
                      >
                        <IconSymbol
                          name="xmark.circle.fill"
                          size={20}
                          color={isDark ? '#EF4444' : '#DC2626'}
                        />
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <TouchableOpacity
                      onPress={handleFileSelect}
                      className={`p-6 rounded-lg border border-dashed items-center ${
                        isDark ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background'
                      }`}
                    >
                      <IconSymbol
                        name="doc.text.fill"
                        size={32}
                        color={isDark ? '#9CA3AF' : '#6B7280'}
                      />
                      <Text className={`mt-2 font-rubik-medium ${
                        isDark ? 'text-dark-text' : 'text-light-text'
                      }`}>
                        Select File
                      </Text>
                      <Text className={`text-sm ${
                        isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                      }`}>
                        PDF, Word, PowerPoint, or Images
                      </Text>
                    </TouchableOpacity>
                  )}

                  {isUploading && (
                    <View className="mt-3">
                      <View className="flex-row items-center justify-between mb-1">
                        <Text className={`text-sm ${
                          isDark ? 'text-dark-text' : 'text-light-text'
                        }`}>
                          Uploading...
                        </Text>
                        <Text className={`text-sm ${
                          isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                        }`}>
                          {uploadProgress}%
                        </Text>
                      </View>
                      <View className={`h-2 rounded-full ${
                        isDark ? 'bg-dark-background' : 'bg-gray-200'
                      }`}>
                        <View
                          className="h-2 rounded-full bg-primary-500"
                          style={{ width: `${uploadProgress}%` }}
                        />
                      </View>
                    </View>
                  )}
                </View>
              </Animated.View>
            )}

            {/* Submit Button */}
            <Animated.View entering={FadeInDown.duration(900)} className="mb-8">
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={isSaving || isUploading}
                className={`p-5 rounded-2xl items-center flex-row justify-center ${
                  isSaving || isUploading
                    ? 'bg-gray-400'
                    : useAI
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                      : 'bg-primary-500'
                }`}
                style={{
                  shadowColor: useAI ? '#8B5CF6' : '#3B82F6',
                  shadowOffset: { width: 0, height: 6 },
                  shadowOpacity: 0.3,
                  shadowRadius: 12,
                  elevation: 8,
                }}
              >
                {isSaving ? (
                  <View className="flex-row items-center">
                    <ActivityIndicator size="small" color="#FFFFFF" />
                    <Text className="text-white font-rubik-semibold text-lg ml-3">
                      {useAI ? 'Generating with AI...' : 'Creating Material...'}
                    </Text>
                  </View>
                ) : (
                  <View className="flex-row items-center">
                    {useAI && <Text className="text-white text-lg mr-2">✨</Text>}
                    <Text className="text-white font-rubik-bold text-lg">
                      {useAI ? 'Generate with AI' : 'Create Material'}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            </Animated.View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {error && (
        <View className="absolute bottom-4 left-4 right-4">
          <View className="bg-red-500 p-3 rounded-lg flex-row items-center">
            <IconSymbol name="exclamationmark.triangle.fill" size={20} color="#FFFFFF" />
            <Text className="text-white ml-2 flex-1">{error}</Text>
            <TouchableOpacity onPress={clearError}>
              <IconSymbol name="xmark" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default CreateMaterialScreen; 