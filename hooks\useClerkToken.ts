import { useAuth } from '@clerk/clerk-expo';
import { useState, useCallback } from 'react';

export function useClerkToken() {
  const { getToken, isLoaded, isSignedIn } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getSupabaseToken = useCallback(async () => {
    if (!isLoaded || !isSignedIn) {
      setError('User is not signed in');
      return null;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Get the JWT token from <PERSON> with the Supabase template
      const token = await getToken({ template: 'supabase' });
      
      if (!token) {
        throw new Error('Failed to get token from <PERSON>');
      }

      return token;
    } catch (err: any) {
      console.error('Error getting Clerk token:', err);
      setError(err.message || 'An error occurred while getting the token');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, isSignedIn, getToken]);

  return {
    getSupabaseToken,
    isLoaded,
    isSignedIn,
    isLoading,
    error,
  };
}
