# Create User Edge Function

This Edge Function handles user creation in Supabase while integrating with Clerk authentication.

## Deployment Instructions

1. Deploy the Edge Function:
   ```bash
   supabase functions deploy create-user
   ```

2. Deploy the SQL function to bypass RLS:
   - Go to the Supabase dashboard
   - Navigate to the SQL Editor
   - Copy the contents of `sql/create_user_function.sql`
   - Run the SQL query to create the function

## Troubleshooting

If you encounter the error "Edge Function returned a non-2xx status code", check the following:

1. **Authentication Issues**:
   - Make sure the user is properly authenticated with Clerk
   - Ensure the Authorization header is being passed to the Edge Function

2. **RLS Policies**:
   - Ensure the SQL function `create_user_bypassing_rls` is deployed
   - Check that the function has SECURITY DEFINER privileges

3. **Missing Required Fields**:
   - Ensure all required fields (name, email, role, tenantId) are provided

4. **Permission Issues**:
   - Only users with the 'admin' role can create new users
   - Check that the current user has the correct role

5. **Tenant ID Mismatch**:
   - The tenant ID must match the current user's tenant ID

6. **Duplicate Email**:
   - Ensure the email is not already in use by another user

## Logs

To view logs for debugging:
```bash
supabase functions logs create-user
```
