import { SignOutButton } from '@/components/SignOutButton';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, View } from 'react-native';

interface DashboardHeaderProps {
  userName?: string;
}

// Helper function to get greeting based on time
const getGreeting = (): string => {
  const hour = new Date().getHours();
  if (hour < 12) return 'morning';
  if (hour < 17) return 'afternoon';
  return 'evening';
};

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({ 
  userName = 'Teacher' 
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  
  // Extract first name from full name
  const firstName = userName?.split(' ')[0] || 'Teacher';

  return (
    <View className="mb-6">
      <Text 
        className={`
          text-3xl font-rubik-bold mb-1
          ${isDark ? 'text-dark-text' : 'text-light-text'}
        `}
      >
        Good {getGreeting()}, {firstName}!
      </Text>
      
      {/* <SignOutButton fullWidth={false} /> */}
      
      <Text 
        className={`
          text-base font-rubik
          ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}
        `}
      >
        Here&apos;s your classroom overview
      </Text>
    </View>
  );
};
