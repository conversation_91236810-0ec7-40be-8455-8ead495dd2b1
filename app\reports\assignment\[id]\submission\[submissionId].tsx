import React, { useEffect, useState } from 'react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { View, Text, ActivityIndicator } from 'react-native';
import SubmissionGradingInterface from '@/components/assignment/SubmissionGradingInterface';
import { useAssignmentStore } from '@/stores/assignmentStore';

export default function SubmissionGrading() {
  const { id, submissionId } = useLocalSearchParams<{ id: string; submissionId: string }>();
  const router = useRouter();
  const { selectedAssignment, fetchSubmissionById } = useAssignmentStore();
  
  const [submission, setSubmission] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSubmission = async () => {
      if (submissionId) {
        const submissionData = await fetchSubmissionById(submissionId);
        setSubmission(submissionData);
      }
      setLoading(false);
    };

    loadSubmission();
  }, [submissionId, fetchSubmissionById]);

  const handleGradingComplete = () => {
    router.back();
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
        <ActivityIndicator size="large" color="#2196F3" />
        <Text className="mt-2 text-light-text dark:text-dark-text">Loading submission...</Text>
      </SafeAreaView>
    );
  }

  if (!selectedAssignment || !submission) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
        <Text className="text-light-text dark:text-dark-text">Submission not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-light-background dark:bg-dark-background">
      <SubmissionGradingInterface
        assignment={selectedAssignment}
        submission={submission}
        onGradingComplete={handleGradingComplete}
      />
    </SafeAreaView>
  );
}
