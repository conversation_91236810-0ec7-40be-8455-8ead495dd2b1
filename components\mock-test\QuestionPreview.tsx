import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface Question {
  id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'short_answer' | 'essay';
  options?: string[];
  correct_answer?: string;
  points: number;
  order_index: number;
  gemini_generated: boolean;
}

interface QuestionPreviewProps {
  question: Question;
  questionNumber: number;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
}

export function QuestionPreview({ 
  question, 
  questionNumber, 
  onEdit, 
  onDelete, 
  showActions = true 
}: QuestionPreviewProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  const getQuestionTypeColor = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'bg-blue-100 text-blue-700';
      case 'short_answer':
        return 'bg-green-100 text-green-700';
      case 'essay':
        return 'bg-purple-100 text-purple-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'Multiple Choice';
      case 'short_answer':
        return 'Short Answer';
      case 'essay':
        return 'Essay';
      default:
        return 'Unknown';
    }
  };

  return (
    <View className={`p-4 rounded-lg border mb-3 ${
      isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
    }`}>
      {/* Header */}
      <View className="flex-row justify-between items-start mb-3">
        <View className="flex-1 mr-3">
          <View className="flex-row items-center mb-2">
            <Text className={`font-rubik-semibold text-base ${
              isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              Question {questionNumber}
            </Text>
            
            <View className={`ml-2 px-2 py-1 rounded-full ${getQuestionTypeColor(question.question_type)}`}>
              <Text className="text-xs font-rubik-medium">
                {getQuestionTypeLabel(question.question_type)}
              </Text>
            </View>
            
            <Text className={`ml-2 font-rubik-medium text-sm ${
              isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
            }`}>
              {question.points} pts
            </Text>
          </View>
        </View>

        {showActions && (
          <View className="flex-row space-x-2">
            {onEdit && (
              <TouchableOpacity
                onPress={onEdit}
                className="p-2 rounded-lg bg-blue-500"
              >
                <IconSymbol name="pencil" size={16} color="white" />
              </TouchableOpacity>
            )}
            
            {onDelete && (
              <TouchableOpacity
                onPress={onDelete}
                className="p-2 rounded-lg bg-red-500"
              >
                <IconSymbol name="trash" size={16} color="white" />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      {/* Question Text */}
      <Text className={`font-rubik mb-3 text-base ${
        isDark ? 'text-dark-text' : 'text-light-text'
      }`}>
        {question.question_text}
      </Text>

      {/* Options for Multiple Choice */}
      {question.question_type === 'multiple_choice' && question.options && (
        <View className="ml-3">
          {question.options.map((option, index) => {
            const isCorrect = option === question.correct_answer;
            return (
              <View key={index} className="flex-row items-center mb-2">
                <View className={`w-4 h-4 rounded-full border-2 mr-3 ${
                  isCorrect 
                    ? 'bg-green-500 border-green-500' 
                    : isDark ? 'border-dark-border' : 'border-light-border'
                }`}>
                  {isCorrect && (
                    <View className="w-full h-full items-center justify-center">
                      <View className="w-2 h-2 bg-white rounded-full" />
                    </View>
                  )}
                </View>
                <Text className={`font-rubik text-sm flex-1 ${
                  isCorrect 
                    ? 'text-green-600 font-rubik-medium' 
                    : isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                }`}>
                  {String.fromCharCode(65 + index)}. {option}
                  {isCorrect && ' ✓'}
                </Text>
              </View>
            );
          })}
        </View>
      )}

      {/* Sample Answer for Non-Multiple Choice */}
      {question.question_type !== 'multiple_choice' && question.correct_answer && (
        <View className={`mt-2 p-3 rounded-lg border-l-4 border-green-500 ${
          isDark ? 'bg-dark-surface/50' : 'bg-green-50'
        }`}>
          <Text className={`font-rubik-medium text-sm mb-1 ${
            isDark ? 'text-dark-text' : 'text-green-700'
          }`}>
            Sample Answer / Keywords:
          </Text>
          <Text className={`font-rubik text-sm ${
            isDark ? 'text-dark-textSecondary' : 'text-green-600'
          }`}>
            {question.correct_answer}
          </Text>
        </View>
      )}

      {/* AI Generated Badge */}
      {question.gemini_generated && (
        <View className="flex-row items-center mt-3">
          <IconSymbol name="wand.and.stars" size={14} color="#8B5CF6" />
          <Text className="ml-1 text-purple-600 text-xs font-rubik-medium">
            AI Generated
          </Text>
        </View>
      )}
    </View>
  );
}

export default QuestionPreview; 