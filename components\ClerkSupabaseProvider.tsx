import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { setClerkAuthFunctions } from '@/lib/authHelpers';
import { supabaseAnonKey, supabaseUrl } from '@/lib/supabase';
import { useAuth, useUser } from '@clerk/clerk-expo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import 'react-native-url-polyfill/auto';

// Create a context for the Supabase client
interface SupabaseContextType {
  supabase: ReturnType<typeof createClient> | null;
}

const SupabaseContext = createContext<SupabaseContextType>({ supabase: null });

// Hook to use the Supabase client
export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (!context.supabase) {
    throw new Error('useSupabase must be used within a ClerkSupabaseProvider');
  }
  return context.supabase;
};

interface ClerkSupabaseProviderProps {
  children: ReactNode;
}

export function ClerkSupabaseProvider({ children }: ClerkSupabaseProviderProps) {
  const { getToken, isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [supabaseClient, setSupabaseClient] = useState<any>(null);
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  // The Supabase client is now created directly in the useEffect

  // Use a ref to track initialization state
  const initialized = useRef(false);
  // Store token in ref to avoid dependency changes
  const tokenRef = useRef<string | null>(null);

  // Create a stable version of the fetch function that doesn't depend on changing props
  const customFetch = useRef(async (url: RequestInfo | URL, options: RequestInit = {}) => {
    // Use the stored token if available
    const token = tokenRef.current;

    // Add the token to the request headers if available
    const headers = new Headers(options?.headers);
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    // Make the request with the updated headers
    return fetch(url, {
      ...options,
      headers,
    });
  }).current;

  // Create the Supabase client only once
  const stableClient = useRef(
    createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        storage: AsyncStorage,
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
      global: {
        fetch: customFetch,
      },
    })
  ).current;

  // Effect to update the token when auth state changes
  useEffect(() => {
    // Function to get and store the token
    const updateToken = async () => {
      if (!isLoaded) {
        return;
      }

      try {
        if (isSignedIn) {
          const token = await getToken({ template: 'supabase' });
          tokenRef.current = token;

          if (!initialized.current) {
            setSupabaseClient(stableClient);
            initialized.current = true;
            console.log('Supabase client created with Clerk authentication');

            // Set global auth functions for use in stores and utilities
            setClerkAuthFunctions({
              getToken,
              isSignedIn,
              user,
            });
            console.log('Global Clerk auth functions set');
          }
        } else {
          tokenRef.current = null;
          // Clear global auth functions when signed out
          setClerkAuthFunctions(null);
          console.log('Global Clerk auth functions cleared');
        }
      } catch (err) {
        console.error('Error getting Clerk token:', err);
      } finally {
        setIsLoading(false);
      }
    };

    updateToken();
  }, [isLoaded, isSignedIn, getToken, stableClient]);

  // Show loading indicator while connecting
  if (isLoading && isSignedIn) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ActivityIndicator
            size="large"
            color={isDark ? Colors.dark.primary : Colors.light.primary}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Provide the Supabase client to children through context
  return (
    <SupabaseContext.Provider value={{ supabase: supabaseClient }}>
      {children}
    </SupabaseContext.Provider>
  );
}
