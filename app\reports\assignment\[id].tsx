import { useColorScheme } from '@/hooks/useColorScheme';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function AssignmentDetail() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const {
    selectedAssignment,
    loading,
    error,
    fetchAssignmentById,
    deleteAssignment,
    publishAssignment,
    closeAssignment,
  } = useAssignmentStore();

  const [assignment, setAssignment] = useState(selectedAssignment);

  useEffect(() => {
    if (id && id !== assignment?.id) {
      fetchAssignmentById(id).then(setAssignment);
    }
  }, [id, fetchAssignmentById, assignment?.id]);

  const handleDelete = () => {
    if (!assignment) return;
    
    Alert.alert(
      'Delete Assignment',
      `Are you sure you want to delete "${assignment.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            await deleteAssignment(assignment.id);
            router.back();
          },
        },
      ]
    );
  };

  const handleStatusChange = () => {
    if (!assignment) return;
    
    if (assignment.status === 'draft') {
      Alert.alert(
        'Publish Assignment',
        `Publish "${assignment.title}" to make it available to students?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Publish',
            onPress: () => publishAssignment(assignment.id),
          },
        ]
      );
    } else if (assignment.status === 'published') {
      Alert.alert(
        'Close Assignment',
        `Close "${assignment.title}" to stop accepting submissions?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Close',
            onPress: () => closeAssignment(assignment.id),
          },
        ]
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-500';
      case 'published':
        return 'bg-green-500';
      case 'closed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <View className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text className={`mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Loading assignment...
        </Text>
      </View>
    );
  }

  if (error || !assignment) {
    return (
      <View className={`flex-1 justify-center items-center px-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        <Ionicons name="alert-circle" size={48} color="#EF4444" />
        <Text className={`text-center mt-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          {error || 'Assignment not found'}
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          className="mt-4 px-4 py-2 bg-primary rounded-lg"
        >
          <Text className="text-white font-rubik-medium">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className="flex-row items-center justify-between p-4">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
        </TouchableOpacity>
        
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Assignment Details
        </Text>
        
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              'Assignment Actions',
              'Choose an action',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Edit', onPress: () => Alert.alert('Edit', 'Edit functionality coming soon') },
                { text: 'View Submissions', onPress: () => router.push(`/reports/assignment/${assignment.id}/submissions`) },
                { 
                  text: assignment.status === 'draft' ? 'Publish' : assignment.status === 'published' ? 'Close' : 'Reopen',
                  onPress: handleStatusChange
                },
                { text: 'Delete', style: 'destructive', onPress: handleDelete },
              ]
            );
          }}
          className="p-2"
        >
          <Ionicons name="ellipsis-vertical" size={24} color={isDark ? '#9CA3AF' : '#6B7280'} />
        </TouchableOpacity>
      </View>

      {/* Assignment Info */}
      <View className="px-4 pb-4">
        <View className={`p-4 rounded-lg border ${isDark ? 'bg-dark-card border-dark-border' : 'bg-light-card border-light-border'}`}>
          {/* Title and Status */}
          <View className="flex-row items-start justify-between mb-3">
            <Text className={`text-2xl font-rubik-bold flex-1 mr-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {assignment.title}
            </Text>
            <View className={`px-3 py-1 rounded-full ${getStatusColor(assignment.status)}`}>
              <Text className="text-white text-sm font-rubik-medium capitalize">
                {assignment.status}
              </Text>
            </View>
          </View>

          {/* Description */}
          {assignment.description && (
            <View className="mb-4">
              <Text className={`text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {assignment.description}
              </Text>
            </View>
          )}

          {/* Instructions */}
          {assignment.instructions && (
            <View className="mb-4">
              <Text className={`text-sm font-rubik-medium mb-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Instructions:
              </Text>
              <Text className={`text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {assignment.instructions}
              </Text>
            </View>
          )}

          {/* Assignment Details */}
          <View className="space-y-3">
            <View className="flex-row items-center">
              <Ionicons name="calendar-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Due: {new Date(assignment.due_date).toLocaleDateString('en-US', { 
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="trophy-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Max Points: {assignment.max_points}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="document-text-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Submissions: {assignment.submissions_count || 0}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Ionicons name="checkmark-done-outline" size={20} color={isDark ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Graded: {assignment.graded_count || 0}
              </Text>
            </View>

            {assignment.gemini_generated && (
              <View className="flex-row items-center">
                <Ionicons name="sparkles" size={20} color="#8B5CF6" />
                <Text className="ml-2 text-purple-500 font-rubik-medium">
                  AI Generated Content
                </Text>
              </View>
            )}
          </View>

          {/* Created/Updated Info */}
          <View className="mt-4 pt-4 border-t border-gray-200">
            <Text className={`text-xs ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              Created: {new Date(assignment.created_at).toLocaleDateString()}
            </Text>
            {assignment.updated_at !== assignment.created_at && (
              <Text className={`text-xs ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                Updated: {new Date(assignment.updated_at).toLocaleDateString()}
              </Text>
            )}
          </View>
        </View>

        {/* Action Buttons */}
        <View className="flex-row space-x-3 mt-4">
          <TouchableOpacity
            onPress={() => router.push(`/reports/assignment/${assignment.id}/submissions`)}
            className="flex-1 py-3 bg-blue-500 rounded-lg items-center"
          >
            <Text className="text-white font-rubik-medium">View Submissions</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => Alert.alert('Edit', 'Edit functionality coming soon')}
            className="flex-1 py-3 bg-green-500 rounded-lg items-center"
          >
            <Text className="text-white font-rubik-medium">Edit Assignment</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}
