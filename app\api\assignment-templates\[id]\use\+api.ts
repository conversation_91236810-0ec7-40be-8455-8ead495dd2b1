import { supabase } from '@/lib/supabase';

// POST - Use a template to create a new assignment
export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id: templateId } = params;
    const body = await request.json();
    const { classId, customizations = {} } = body;

    if (!classId) {
      return Response.json({ error: 'Class ID required' }, { status: 400 });
    }

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('id, tenant_id')
      .eq('user_id', session.user.id)
      .single();

    if (teacherError || !teacherData) {
      return Response.json({ error: 'Teacher not found' }, { status: 404 });
    }

    // Get template
    const { data: template, error: templateError } = await supabase
      .from('assignment_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (templateError || !template) {
      return Response.json({ error: 'Template not found' }, { status: 404 });
    }

    // Check if template is accessible (public or owned by user)
    if (!template.is_public && template.teacher_id !== teacherData.id) {
      return Response.json({ error: 'Template not accessible' }, { status: 403 });
    }

    // Verify class belongs to teacher
    const { data: classData, error: classError } = await supabase
      .from('classes')
      .select('id')
      .eq('id', classId)
      .eq('teacher_id', teacherData.id)
      .single();

    if (classError || !classData) {
      return Response.json({ error: 'Class not found or not accessible' }, { status: 404 });
    }

    // Merge template data with customizations
    const templateData = template.template_data;
    const assignmentData = {
      title: customizations.title || templateData.title,
      description: customizations.description || templateData.description,
      instructions: customizations.instructions || templateData.instructions,
      max_points: customizations.maxPoints || templateData.maxPoints,
      due_date: customizations.dueDate || null,
      class_id: classId,
      teacher_id: teacherData.id,
      tenant_id: teacherData.tenant_id,
      status: 'draft',
      created_from_template: templateId,
    };

    // Create assignment
    const { data: assignment, error: assignmentError } = await supabase
      .from('assignments')
      .insert([assignmentData])
      .select()
      .single();

    if (assignmentError) {
      console.error('Error creating assignment from template:', assignmentError);
      return Response.json({ error: 'Failed to create assignment' }, { status: 500 });
    }

    // Create rubrics if they exist in template
    if (templateData.rubrics && templateData.rubrics.length > 0) {
      const rubricData = templateData.rubrics.map((rubric: any, index: number) => ({
        assignment_id: assignment.id,
        tenant_id: teacherData.tenant_id,
        criteria_name: rubric.criteria_name,
        description: rubric.description,
        max_points: rubric.max_points,
        order_index: index,
      }));

      const { error: rubricError } = await supabase
        .from('assignment_rubrics')
        .insert(rubricData);

      if (rubricError) {
        console.error('Error creating rubrics:', rubricError);
        // Don't fail the whole operation, just log the error
      }
    }

    // Log template usage
    const usageLogData = {
      template_id: templateId,
      used_by_teacher_id: teacherData.id,
      assignment_id: assignment.id,
    };

    const { error: usageError } = await supabase
      .from('template_usage_log')
      .insert([usageLogData]);

    if (usageError) {
      console.error('Error logging template usage:', usageError);
      // Don't fail the operation
    }

    // Increment template usage count
    const { error: incrementError } = await supabase
      .rpc('increment_template_usage', { template_uuid: templateId });

    if (incrementError) {
      console.error('Error incrementing template usage:', incrementError);
      // Don't fail the operation
    }

    // Fetch the complete assignment with rubrics
    const { data: completeAssignment, error: fetchError } = await supabase
      .from('assignments')
      .select(`
        *,
        rubrics:assignment_rubrics(*),
        class:classes(name),
        teacher:teachers(
          user:users(name)
        )
      `)
      .eq('id', assignment.id)
      .single();

    if (fetchError) {
      console.error('Error fetching complete assignment:', fetchError);
      return Response.json(assignment); // Return basic assignment if fetch fails
    }

    return Response.json({
      assignment: completeAssignment,
      templateUsed: {
        id: template.id,
        name: template.name,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Error using assignment template:', error);
    return Response.json(
      { error: 'Failed to use assignment template' },
      { status: 500 }
    );
  }
}
