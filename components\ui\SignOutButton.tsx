import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@clerk/clerk-expo';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

interface SignOutButtonProps {
  onSignOut?: () => void;
  redirectTo?: string;
  fullWidth?: boolean;
}

/**
 * A beautifully styled sign-out button component
 *
 * @param onSignOut - Optional callback function to execute before signing out
 * @param redirectTo - Path to redirect to after signing out (default: '/sign-in')
 * @param fullWidth - Whether the button should take up the full width (default: true)
 */
const SignOutButton: React.FC<SignOutButtonProps> = ({
  onSignOut,
  redirectTo = '/sign-in',
  fullWidth = true
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const { signOut: clerkSignOut } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Handle logout
  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);

      // Call custom onSignOut handler if provided
      if (onSignOut) {
        onSignOut();
      }

      // Sign out from Clerk
      await clerkSignOut();

      // Navigate to sign-in page or specified redirect
      router.replace(redirectTo as any);
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <View className="mt-10 mb-4">
      <View
        className={`w-full ${!fullWidth ? 'max-w-xs mx-auto' : ''} overflow-hidden rounded-2xl ${
          isDark ? 'bg-dark-surface' : 'bg-light-surface'
        }`}
        style={{
          elevation: 4,
          shadowColor: isDark ? '#000' : '#888',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
          borderWidth: 1,
          borderColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
        }}
      >
        <TouchableOpacity
          onPress={handleLogout}
          disabled={isLoggingOut}
          className={`py-5 px-6`}
          style={{
            backgroundColor: isLoggingOut
              ? isDark ? 'rgba(220, 38, 38, 0.7)' : 'rgba(252, 165, 165, 0.7)'
              : isDark ? 'rgba(220, 38, 38, 0.8)' : 'rgba(239, 68, 68, 0.9)',
          }}
        >
          <View className="flex-row justify-center items-center">
            <View
              className={`w-10 h-10 rounded-full items-center justify-center mr-4 ${
                isDark ? 'bg-white/10' : 'bg-white/25'
              }`}
            >
              {isLoggingOut ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <IconSymbol
                  name={"power" as any}
                  size={22}
                  color="white"
                />
              )}
            </View>
            <Text className="text-white font-rubik-medium text-lg">
              {isLoggingOut ? 'Signing Out...' : 'Sign Out'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
      <Text
        className={`text-center mt-3 text-xs ${
          isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
        }`}
      >
        You will be redirected to the sign-in screen
      </Text>
    </View>
  );
};

export default SignOutButton;
