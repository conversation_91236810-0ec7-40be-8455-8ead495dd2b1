import { useColorScheme } from '@/hooks/useColorScheme';
import { Stack } from 'expo-router';
import React from 'react';

export default function AttendanceLayout() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: isDark ? '#1F2937' : '#FFFFFF',
        },
        headerTintColor: isDark ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontFamily: 'Rubik_600SemiBold',
        },
        headerShadowVisible: false,
        headerBackTitleVisible: false,
      }}
    >
      <Stack.Screen
        name="create-session"
        options={{
          title: 'Create Session',
          presentation: 'modal',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="take-attendance"
        options={{
          title: 'Take Attendance',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="teacher-attendance"
        options={{
          title: 'Teacher Attendance',
          headerShown: false,
        }}
      />
    </Stack>
  );
}
