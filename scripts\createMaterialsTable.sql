-- Create materials table
CREATE TABLE IF NOT EXISTS materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  uploaded_by UUID NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT,
  file_url TEXT,
  file_type VARCHAR(50),
  file_size INTEGER,
  subject VARCHAR(100),
  grade_level VARCHAR(50),
  tags TEXT[],
  material_type VARCHAR(50) DEFAULT 'resource' CHECK (material_type IN ('lesson_plan', 'worksheet', 'quiz', 'resource', 'assignment', 'presentation')),
  visibility VARCHAR(20) DEFAULT 'private' CHECK (visibility IN ('private', 'class', 'school', 'public')),
  gemini_generated BOOLEAN DEFAULT FALSE,
  download_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create material_shares table
CREATE TABLE IF NOT EXISTS material_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  material_id UUID NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
  shared_with_type VARCHAR(20) NOT NULL CHECK (shared_with_type IN ('teacher', 'class', 'student')),
  shared_with_id UUID NOT NULL,
  shared_by UUID NOT NULL,
  permissions VARCHAR(20) DEFAULT 'view' CHECK (permissions IN ('view', 'download', 'edit')),
  shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(material_id, shared_with_type, shared_with_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_materials_tenant_id ON materials(tenant_id);
CREATE INDEX IF NOT EXISTS idx_materials_uploaded_by ON materials(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_materials_subject ON materials(subject);
CREATE INDEX IF NOT EXISTS idx_materials_grade_level ON materials(grade_level);
CREATE INDEX IF NOT EXISTS idx_materials_material_type ON materials(material_type);
CREATE INDEX IF NOT EXISTS idx_materials_visibility ON materials(visibility);
CREATE INDEX IF NOT EXISTS idx_materials_created_at ON materials(created_at);
CREATE INDEX IF NOT EXISTS idx_materials_tags ON materials USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_material_shares_tenant_id ON material_shares(tenant_id);
CREATE INDEX IF NOT EXISTS idx_material_shares_material_id ON material_shares(material_id);
CREATE INDEX IF NOT EXISTS idx_material_shares_shared_with ON material_shares(shared_with_type, shared_with_id);

-- Enable Row Level Security
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_shares ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for materials (simplified for now)
DROP POLICY IF EXISTS "materials_tenant_isolation" ON materials;
CREATE POLICY "materials_tenant_isolation" ON materials
  FOR ALL USING (true); -- Temporarily allow all access for testing

DROP POLICY IF EXISTS "material_shares_tenant_isolation" ON material_shares;
CREATE POLICY "material_shares_tenant_isolation" ON material_shares
  FOR ALL USING (true); -- Temporarily allow all access for testing

-- Create search_materials function
CREATE OR REPLACE FUNCTION search_materials(
  p_teacher_id UUID,
  p_search_query TEXT DEFAULT NULL,
  p_subject VARCHAR(100) DEFAULT NULL,
  p_material_type VARCHAR(50) DEFAULT NULL,
  p_grade_level VARCHAR(50) DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL,
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  title VARCHAR(255),
  description TEXT,
  subject VARCHAR(100),
  grade_level VARCHAR(50),
  material_type VARCHAR(50),
  file_url TEXT,
  file_type VARCHAR(50),
  tags TEXT[],
  view_count INTEGER,
  download_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  uploaded_by_name VARCHAR(255)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    m.id,
    m.title,
    m.description,
    m.subject,
    m.grade_level,
    m.material_type,
    m.file_url,
    m.file_type,
    m.tags,
    m.view_count,
    m.download_count,
    m.created_at,
    COALESCE(u.name, t.name, 'Unknown') as uploaded_by_name
  FROM materials m
  LEFT JOIN teachers t ON m.uploaded_by = t.id
  LEFT JOIN users u ON t.user_id = u.id
  WHERE 
    m.is_active = TRUE AND
    (m.uploaded_by = p_teacher_id OR m.visibility IN ('class', 'school', 'public')) AND
    (p_search_query IS NULL OR 
     m.title ILIKE '%' || p_search_query || '%' OR 
     m.description ILIKE '%' || p_search_query || '%') AND
    (p_subject IS NULL OR m.subject = p_subject) AND
    (p_material_type IS NULL OR m.material_type = p_material_type) AND
    (p_grade_level IS NULL OR m.grade_level = p_grade_level) AND
    (p_tags IS NULL OR m.tags && p_tags)
  ORDER BY m.created_at DESC
  LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment view count
CREATE OR REPLACE FUNCTION increment_material_view_count(material_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE materials 
  SET view_count = view_count + 1, updated_at = NOW()
  WHERE id = material_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment download count
CREATE OR REPLACE FUNCTION increment_material_download_count(material_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE materials 
  SET download_count = download_count + 1, updated_at = NOW()
  WHERE id = material_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 