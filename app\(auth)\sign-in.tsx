import { IconSymbol } from "@/components/ui/IconSymbol";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useSignIn } from "@clerk/clerk-expo";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React from "react";
import {
    ActivityIndicator,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function SignInScreen() {
  const { signIn, setActive, isLoaded } = useSignIn();
  const router = useRouter();
  const colorScheme = useColorScheme() ?? "light";
  const isDark = colorScheme === "dark";

  const [emailAddress, setEmailAddress] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);

  // Handle the submission of the sign-in form
  const onSignInPress = async () => {
    if (!isLoaded || isLoading) return;

    // Validate inputs
    if (!emailAddress.trim()) {
      setError("Email is required");
      return;
    }
    if (!password.trim()) {
      setError("Password is required");
      return;
    }

    setIsLoading(true);
    setError("");

    // Start the sign-in process using the email and password provided
    try {
      const signInAttempt = await signIn.create({
        identifier: emailAddress,
        password,
      });

      // If sign-in process is complete, set the created session as active
      // and redirect the user
      if (signInAttempt.status === "complete") {
        await setActive({ session: signInAttempt.createdSessionId });

        // Check if the user has completed signup and needs to register a school
        try {
          const signupCompleted = await AsyncStorage.getItem('signupCompleted');
          if (signupCompleted === 'true') {
            // Clear the flag
            await AsyncStorage.removeItem('signupCompleted');
            // Redirect to school registration
            router.replace('/(auth)/school-registration');
          } else {
            // Regular sign-in, redirect to home
            router.replace("/(tabs)/home");
          }
        } catch (err) {
          console.error('Error checking signup status:', err);
          router.replace("/(tabs)/home");
        }
      } else {
        // If the status isn't complete, check why. User might need to
        // complete further steps.
        console.error(JSON.stringify(signInAttempt, null, 2));
        setError("Sign in failed. Please try again.");
      }
    } catch (err: any) {
      // See https://clerk.com/docs/custom-flows/error-handling
      // for more info on error handling
      console.error(JSON.stringify(err, null, 2));

      // Check if the error is "session_exists"
      if (err.errors?.[0]?.code === "session_exists") {
        // If a session already exists, just redirect to the appropriate page
        try {
          const signupCompleted = await AsyncStorage.getItem('signupCompleted');
          if (signupCompleted === 'true') {
            // Clear the flag
            await AsyncStorage.removeItem('signupCompleted');
            // Redirect to school registration
            router.replace('/(auth)/school-registration');
          } else {
            // Regular sign-in, redirect to home
            router.replace("/(tabs)/home");
          }
        } catch (storageErr) {
          console.error('Error checking signup status:', storageErr);
          router.replace("/(tabs)/home");
        }
      } else {
        // For other errors, display the error message
        setError(err.errors?.[0]?.message || "An error occurred during sign in");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView
      className={`flex-1 ${
        isDark ? "bg-dark-background" : "bg-light-background"
      }`}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
        <StatusBar style={isDark ? "light" : "dark"} />
        <ScrollView
          contentContainerClassName="flex-grow justify-center px-6 py-10"
          keyboardShouldPersistTaps="handled"
        >
          <View className="items-center mb-8">
            <View
              className={`w-16 h-16 rounded-full items-center justify-center mb-4 ${
                isDark ? "bg-primary-700" : "bg-primary-100"
              }`}
            >
              <IconSymbol
                name="chevron.right"
                size={32}
                color={isDark ? "#60A5FA" : "#3B82F6"}
              />
            </View>
            <Text
              className={`text-2xl font-rubik-bold ${
                isDark ? "text-dark-text" : "text-light-text"
              }`}
            >
              Welcome back
            </Text>
            <Text
              className={`text-center mt-2 ${
                isDark ? "text-dark-textSecondary" : "text-light-textSecondary"
              }`}
            >
              Sign in to your account
            </Text>
          </View>

          {error ? (
            <View className="mb-4 p-3 rounded-lg bg-error-light">
              <Text className="text-error-dark">{error}</Text>
            </View>
          ) : null}

          <View className="mb-4">
            <Text
              className={`mb-2 font-rubik-medium ${
                isDark ? "text-dark-text" : "text-light-text"
              }`}
            >
              Email
            </Text>
            <TextInput
              className={`p-4 rounded-lg mb-1 font-rubik ${
                isDark
                  ? "bg-dark-surface text-dark-text border-dark-border"
                  : "bg-light-surface text-light-text border-light-border"
              } border`}
              autoCapitalize="none"
              value={emailAddress}
              placeholder="Enter your email"
              placeholderTextColor={isDark ? "#9CA3AF" : "#6B7280"}
              onChangeText={(email) => setEmailAddress(email)}
              keyboardType="email-address"
            />
          </View>

          <View className="mb-6">
            <Text
              className={`mb-2 font-rubik-medium ${
                isDark ? "text-dark-text" : "text-light-text"
              }`}
            >
              Password
            </Text>
            <View className="relative">
              <TextInput
                className={`p-4 rounded-lg mb-1 font-rubik ${
                  isDark
                    ? "bg-dark-surface text-dark-text border-dark-border"
                    : "bg-light-surface text-light-text border-light-border"
                } border pr-12`}
                value={password}
                placeholder="Enter your password"
                placeholderTextColor={isDark ? "#9CA3AF" : "#6B7280"}
                secureTextEntry={!showPassword}
                onChangeText={(password) => setPassword(password)}
              />
              <TouchableOpacity
                className="absolute right-3 top-0 bottom-0 justify-center"
                onPress={() => setShowPassword(!showPassword)}
              >
                <IconSymbol
                  name={showPassword ? "eye.slash.fill" : "eye.fill"}
                  size={24}
                  color={isDark ? "#9CA3AF" : "#6B7280"}
                />
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            className={`py-4 rounded-lg items-center justify-center ${
              isLoading ? "opacity-70" : ""
            } ${isDark ? "bg-primary-600" : "bg-primary-500"}`}
            onPress={onSignInPress}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text className="text-white font-rubik-medium text-base">
                Sign In
              </Text>
            )}
          </TouchableOpacity>

          <View className="flex-row justify-center mt-6">
            <Text
              className={`text-center ${
                isDark ? "text-dark-textSecondary" : "text-light-textSecondary"
              }`}
            >
              Don&apos;t have an account? Contact your school administrator.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

