import React, { memo } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface NoticeSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onClearSearch: () => void;
}

const NoticeSearch: React.FC<NoticeSearchProps> = memo(({
  searchQuery,
  onSearchChange,
  onClearSearch,
}) => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';

  return (
    <View className="mb-4">
      <View
        className={`flex-row items-center px-4 py-3 rounded-xl ${
          isDark
            ? "bg-dark-surface border-dark-border"
            : "bg-light-surface border-light-border"
        }`}
        style={{
          elevation: 2,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        }}
      >
        <IconSymbol
          name="magnifyingglass"
          size={20}
          color={isDark ? Colors.dark.secondary : Colors.light.secondary}
        />
        <TextInput
          className={`flex-1 ml-3 ${
            isDark ? "text-dark-text" : "text-light-text"
          }`}
          placeholder="Search notices"
          placeholderTextColor={isDark ? "#60A5FA" : Colors.light.secondary}
          value={searchQuery}
          onChangeText={onSearchChange}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={onClearSearch}
            className="ml-2 p-1"
          >
            <IconSymbol
              name="xmark.circle.fill"
              size={20}
              color={isDark ? Colors.dark.secondary : Colors.light.secondary}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {searchQuery.length > 0 && (
        <TouchableOpacity
          className={`mt-3 px-6 py-2.5 rounded-full self-center ${
            isDark ? "bg-primary-600" : "bg-primary-500"
          }`}
          onPress={onClearSearch}
          style={{
            elevation: 2,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}
        >
          <Text className="text-white font-rubik-medium">
            Clear Search
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
});

NoticeSearch.displayName = 'NoticeSearch';

export default NoticeSearch;
