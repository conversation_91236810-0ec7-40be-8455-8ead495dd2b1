import { supabase } from '@/lib/supabase';

// GET - Fetch notification preferences for the current user
export async function GET(request: Request) {
  try {
    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user record
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Get notification preferences
    const { data: preferences, error } = await supabase
      .from('notification_preferences')
      .select('preferences')
      .eq('user_id', userData.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      return Response.json({ error: 'Failed to fetch preferences' }, { status: 500 });
    }

    // Return preferences or default if none exist
    const defaultPreferences = {
      deadlineReminders: {
        enabled: true,
        timing: '2hours',
      },
      submissionNotifications: {
        enabled: true,
        immediate: true,
        digest: false,
      },
      gradeNotifications: {
        enabled: true,
        immediate: true,
      },
      lateSubmissionAlerts: {
        enabled: true,
        teacherOnly: true,
      },
      missingSubmissionReminders: {
        enabled: true,
        frequency: 'daily',
      },
      emailNotifications: {
        enabled: false,
        types: [],
      },
      pushNotifications: {
        enabled: true,
        quiet_hours: {
          enabled: true,
          start: '22:00',
          end: '07:00',
        },
      },
    };

    return Response.json({
      preferences: preferences?.preferences || defaultPreferences,
    });

  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return Response.json(
      { error: 'Failed to fetch notification preferences' },
      { status: 500 }
    );
  }
}

// POST/PUT - Update notification preferences
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { preferences } = body;

    if (!preferences) {
      return Response.json({ error: 'Preferences data required' }, { status: 400 });
    }

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user record
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Upsert notification preferences
    const { data: updatedPreferences, error } = await supabase
      .from('notification_preferences')
      .upsert({
        user_id: userData.id,
        preferences: preferences,
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating preferences:', error);
      return Response.json({ error: 'Failed to update preferences' }, { status: 500 });
    }

    return Response.json({
      success: true,
      preferences: updatedPreferences.preferences,
    });

  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return Response.json(
      { error: 'Failed to update notification preferences' },
      { status: 500 }
    );
  }
}

// PUT - Same as POST for updating preferences
export async function PUT(request: Request) {
  return POST(request);
}
