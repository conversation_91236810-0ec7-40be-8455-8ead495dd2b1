import { supabase } from '@/lib/supabase';
import { generateGradingFeedback } from '@/lib/gemini';

export async function POST(request: Request, { params }: { params: { id: string; submissionId: string } }) {
  try {
    const { id: assignmentId, submissionId } = params;

    // Fetch submission details
    const { data: submission, error: submissionError } = await supabase
      .from('assignment_submissions')
      .select('*')
      .eq('id', submissionId)
      .single();

    if (submissionError || !submission) {
      return Response.json({ error: 'Submission not found' }, { status: 404 });
    }

    // Fetch assignment details with rubrics
    const { data: assignment, error: assignmentError } = await supabase
      .from('assignments')
      .select(`
        *,
        rubrics:assignment_rubrics(*)
      `)
      .eq('id', assignmentId)
      .single();

    if (assignmentError || !assignment) {
      return Response.json({ error: 'Assignment not found' }, { status: 404 });
    }

    // Generate AI feedback
    const feedback = await generateGradingFeedback(
      submission.content || '',
      assignment.instructions || '',
      assignment.max_points,
      assignment.rubrics
    );

    // Update submission with AI feedback
    const { error: updateError } = await supabase
      .from('assignment_submissions')
      .update({
        gemini_feedback: feedback.feedback,
        grade: feedback.score,
      })
      .eq('id', submissionId);

    if (updateError) {
      return Response.json({ error: 'Failed to save AI feedback' }, { status: 500 });
    }

    return Response.json({
      success: true,
      feedback: feedback.feedback,
      score: feedback.score,
      strengths: feedback.strengths,
      improvements: feedback.improvements,
      rubricScores: feedback.rubricScores,
    });

  } catch (error) {
    console.error('Error generating AI grading feedback:', error);
    return Response.json(
      { error: 'Failed to generate AI grading feedback' },
      { status: 500 }
    );
  }
}
