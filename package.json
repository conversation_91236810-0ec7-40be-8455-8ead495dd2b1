{"name": "school-management", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@clerk/clerk-expo": "^2.11.5", "@expo/vector-icons": "^14.1.0", "@google/genai": "^1.4.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.49.5-next.1", "@types/redis": "^4.0.10", "expo": "53.0.19", "expo-blur": "^14.1.5", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.6", "expo-file-system": "^18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "immer": "^10.1.1", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.57.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "redis": "^5.1.0", "tailwindcss": "^3.4.17", "victory-native": "^41.17.1", "zod": "^3.25.64", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "supabase": "^2.24.3", "typescript": "~5.8.3"}, "private": true}