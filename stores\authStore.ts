import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { User } from '@/lib/supabase';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  lastActivity: number;
  sessionTimeout: number; // 30 minutes in milliseconds
}

interface AuthActions {
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateActivity: () => void;
  clearSession: () => void;
  isSessionValid: () => boolean;
}

type AuthStore = AuthState & AuthActions;

// Security: Session timeout for inactive users
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes

export const useAuthStore = create<AuthStore>()(
  subscribeWithSelector((set, get) => ({
    // State
    user: null,
    isLoading: false,
    error: null,
    lastActivity: Date.now(),
    sessionTimeout: SESSION_TIMEOUT,

    // Actions
    setUser: (user) => {
      set({ 
        user,
        error: null,
        lastActivity: Date.now()
      });
    },

    setLoading: (isLoading) => {
      set({ isLoading });
    },

    setError: (error) => {
      set({ error });
    },

    updateActivity: () => {
      set({ lastActivity: Date.now() });
    },

    clearSession: () => {
      set({
        user: null,
        error: null,
        lastActivity: 0
      });
    },

    isSessionValid: () => {
      const { lastActivity, sessionTimeout } = get();
      return Date.now() - lastActivity < sessionTimeout;
    }
  }))
);

// Security: Auto-logout on session timeout
useAuthStore.subscribe(
  (state) => state.lastActivity,
  (lastActivity) => {
    if (lastActivity > 0) {
      const timeout = setTimeout(() => {
        const { isSessionValid, clearSession } = useAuthStore.getState();
        if (!isSessionValid()) {
          clearSession();
          console.warn('Session expired due to inactivity');
        }
      }, SESSION_TIMEOUT);

      return () => clearTimeout(timeout);
    }
  }
);
