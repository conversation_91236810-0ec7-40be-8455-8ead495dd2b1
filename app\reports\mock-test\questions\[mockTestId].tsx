import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
  Switch,
  FlatList,
  Modal,
  ActivityIndicator
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAssignmentStore } from '@/stores/assignmentStore';
import { generateMaterialWithGemini } from '@/lib/gemini';
import Animated, { FadeInDown, FadeInUp, SlideInRight, SlideOutLeft } from 'react-native-reanimated';

interface Question {
  id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'short_answer' | 'essay';
  options: string[];
  correct_answer: string;
  points: number;
  order_index: number;
  gemini_generated: boolean;
}

interface QuestionFormData {
  question_text: string;
  question_type: 'multiple_choice' | 'short_answer' | 'essay';
  options: string[];
  correct_answer: string;
  points: number;
}

export default function MockTestQuestions() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { mockTestId } = useLocalSearchParams<{ mockTestId: string }>();
  
  const {
    selectedMockTest,
    questions,
    fetchMockTestById,
    fetchQuestions,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    loading
  } = useAssignmentStore();

  // State
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [questionForm, setQuestionForm] = useState<QuestionFormData>({
    question_text: '',
    question_type: 'multiple_choice',
    options: ['', '', '', ''],
    correct_answer: '',
    points: 1
  });
  const [showAIGenerator, setShowAIGenerator] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiGenerating, setAiGenerating] = useState(false);
  const [dragMode, setDragMode] = useState(false);

  // Load data
  useEffect(() => {
    if (mockTestId) {
      fetchMockTestById(mockTestId);
      fetchQuestions(mockTestId);
    }
  }, [mockTestId]);

  const resetForm = () => {
    setQuestionForm({
      question_text: '',
      question_type: 'multiple_choice',
      options: ['', '', '', ''],
      correct_answer: '',
      points: 1
    });
    setEditingQuestion(null);
  };

  const openQuestionModal = (question?: Question) => {
    if (question) {
      setEditingQuestion(question);
      setQuestionForm({
        question_text: question.question_text,
        question_type: question.question_type,
        options: question.options || ['', '', '', ''],
        correct_answer: question.correct_answer,
        points: question.points
      });
    } else {
      resetForm();
    }
    setShowQuestionModal(true);
  };

  const closeQuestionModal = () => {
    setShowQuestionModal(false);
    resetForm();
  };

  const validateQuestion = (): boolean => {
    if (!questionForm.question_text.trim()) {
      Alert.alert('Error', 'Question text is required');
      return false;
    }

    if (questionForm.question_type === 'multiple_choice') {
      const validOptions = questionForm.options.filter(opt => opt.trim() !== '');
      if (validOptions.length < 2) {
        Alert.alert('Error', 'At least 2 options are required for multiple choice questions');
        return false;
      }
      if (!questionForm.correct_answer.trim()) {
        Alert.alert('Error', 'Correct answer is required');
        return false;
      }
    }

    if (questionForm.points <= 0) {
      Alert.alert('Error', 'Points must be greater than 0');
      return false;
    }

    return true;
  };

  const handleSaveQuestion = async () => {
    if (!validateQuestion() || !mockTestId) return;

    try {
      const questionData = {
        mock_test_id: mockTestId,
        question_text: questionForm.question_text.trim(),
        question_type: questionForm.question_type,
        options: questionForm.question_type === 'multiple_choice' ? questionForm.options : null,
        correct_answer: questionForm.correct_answer.trim(),
        points: questionForm.points,
        order_index: editingQuestion ? editingQuestion.order_index : questions.length + 1,
        gemini_generated: false
      };

      if (editingQuestion) {
        await updateQuestion(editingQuestion.id, questionData);
      } else {
        await createQuestion(questionData);
      }

      closeQuestionModal();
      fetchQuestions(mockTestId);
    } catch (error) {
      Alert.alert('Error', 'Failed to save question');
    }
  };

  const handleDeleteQuestion = (questionId: string) => {
    Alert.alert(
      'Delete Question',
      'Are you sure you want to delete this question?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteQuestion(questionId);
              fetchQuestions(mockTestId!);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete question');
            }
          }
        }
      ]
    );
  };

  const generateQuestionsWithAI = async () => {
    if (!aiPrompt.trim() || !mockTestId) {
      Alert.alert('Error', 'Please enter a prompt for AI generation');
      return;
    }

    setAiGenerating(true);
    try {
      const prompt = `Generate 5 educational test questions based on: ${aiPrompt}. 
      Include a mix of multiple choice and short answer questions. 
      For multiple choice questions, provide 4 options with one correct answer.
      Format each question clearly with the question text, options (if applicable), and correct answer.`;

      const result = await generateMaterialWithGemini(
        prompt,
        'test questions',
        'Assessment',
        'Teacher'
      );

      if (result?.content) {
        // Parse the AI response and create questions
        // This is a simplified implementation - in production, you'd want more robust parsing
        Alert.alert(
          'AI Generated Content',
          'Questions generated successfully! You can now manually add them based on the AI suggestions.',
          [
            {
              text: 'View Suggestions',
              onPress: () => Alert.alert('AI Suggestions', result.content)
            },
            { text: 'Close' }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to generate questions with AI');
    } finally {
      setAiGenerating(false);
      setShowAIGenerator(false);
      setAiPrompt('');
    }
  };

  const renderQuestionTypeSelector = () => (
    <View className="space-y-3">
      <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
        Question Type
      </Text>
      <View className="flex-row space-x-2">
        {[
          { key: 'multiple_choice', label: 'Multiple Choice' },
          { key: 'short_answer', label: 'Short Answer' },
          { key: 'essay', label: 'Essay' }
        ].map((type) => (
          <TouchableOpacity
            key={type.key}
            onPress={() => setQuestionForm({ ...questionForm, question_type: type.key as any })}
            className={`px-4 py-2 rounded-lg border ${
              questionForm.question_type === type.key
                ? 'bg-primary-500 border-primary-500'
                : isDark ? 'border-dark-border bg-dark-surface' : 'border-light-border bg-light-surface'
            }`}
          >
            <Text className={`font-rubik-medium ${
              questionForm.question_type === type.key
                ? 'text-white'
                : isDark ? 'text-dark-text' : 'text-light-text'
            }`}>
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderOptionsEditor = () => {
    if (questionForm.question_type !== 'multiple_choice') return null;

    return (
      <View className="space-y-3">
        <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Answer Options
        </Text>
        {questionForm.options.map((option, index) => (
          <View key={index} className="flex-row items-center space-x-3">
            <TouchableOpacity
              onPress={() => setQuestionForm({
                ...questionForm,
                correct_answer: option
              })}
              className={`w-6 h-6 rounded-full border-2 items-center justify-center ${
                questionForm.correct_answer === option
                  ? 'bg-green-500 border-green-500'
                  : isDark ? 'border-dark-border' : 'border-light-border'
              }`}
            >
              {questionForm.correct_answer === option && (
                <IconSymbol name="checkmark" size={14} color="white" />
              )}
            </TouchableOpacity>
            <TextInput
              value={option}
              onChangeText={(text) => {
                const newOptions = [...questionForm.options];
                newOptions[index] = text;
                setQuestionForm({ ...questionForm, options: newOptions });
              }}
              placeholder={`Option ${index + 1}`}
              placeholderTextColor={isDark ? '#666' : '#999'}
              className={`flex-1 p-3 rounded-lg border font-rubik ${
                isDark ? 'bg-dark-surface border-dark-border text-dark-text' : 'bg-light-surface border-light-border text-light-text'
              }`}
            />
          </View>
        ))}
      </View>
    );
  };

  const renderQuestionCard = ({ item, index }: { item: Question; index: number }) => (
    <Animated.View
      entering={FadeInDown.delay(index * 100).duration(400)}
      className={`p-4 mb-3 rounded-lg border ${
        isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
      }`}
    >
      <View className="flex-row justify-between items-start mb-3">
        <View className="flex-1 mr-3">
          <View className="flex-row items-center mb-2">
            <Text className={`font-rubik-semibold text-base ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Question {item.order_index}
            </Text>
            <View className={`ml-2 px-2 py-1 rounded-full ${
              item.question_type === 'multiple_choice' ? 'bg-blue-100' :
              item.question_type === 'short_answer' ? 'bg-green-100' : 'bg-purple-100'
            }`}>
              <Text className={`text-xs font-rubik-medium ${
                item.question_type === 'multiple_choice' ? 'text-blue-700' :
                item.question_type === 'short_answer' ? 'text-green-700' : 'text-purple-700'
              }`}>
                {item.question_type.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
            <Text className={`ml-2 font-rubik-medium text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              {item.points} pts
            </Text>
          </View>
          
          <Text className={`font-rubik mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            {item.question_text}
          </Text>

          {item.question_type === 'multiple_choice' && item.options && (
            <View className="ml-3">
              {item.options.map((option, optIndex) => (
                <Text key={optIndex} className={`font-rubik text-sm mb-1 ${
                  option === item.correct_answer ? 'text-green-600 font-rubik-medium' : 
                  isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'
                }`}>
                  {String.fromCharCode(65 + optIndex)}. {option}
                  {option === item.correct_answer && ' ✓'}
                </Text>
              ))}
            </View>
          )}

          {item.gemini_generated && (
            <View className="flex-row items-center mt-2">
              <IconSymbol name="wand.and.stars" size={14} color="#8B5CF6" />
              <Text className="ml-1 text-purple-600 text-xs font-rubik-medium">AI Generated</Text>
            </View>
          )}
        </View>

        <View className="flex-row space-x-2">
          <TouchableOpacity
            onPress={() => openQuestionModal(item)}
            className="p-2 rounded-lg bg-blue-500"
          >
            <IconSymbol name="pencil" size={16} color="white" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleDeleteQuestion(item.id)}
            className="p-2 rounded-lg bg-red-500"
          >
            <IconSymbol name="trash" size={16} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );

  const renderQuestionModal = () => (
    <Modal
      visible={showQuestionModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-bg' : 'bg-light-bg'}`}>
        <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <View className="flex-row justify-between items-center">
            <Text className={`font-rubik-bold text-xl ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {editingQuestion ? 'Edit Question' : 'Add Question'}
            </Text>
            <TouchableOpacity onPress={closeQuestionModal}>
              <IconSymbol name="xmark" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
          <View className="space-y-6">
            {/* Question Type */}
            {renderQuestionTypeSelector()}

            {/* Question Text */}
            <View className="space-y-3">
              <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Question Text
              </Text>
              <TextInput
                value={questionForm.question_text}
                onChangeText={(text) => setQuestionForm({ ...questionForm, question_text: text })}
                placeholder="Enter your question..."
                placeholderTextColor={isDark ? '#666' : '#999'}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                className={`p-4 rounded-lg border font-rubik min-h-[120px] ${
                  isDark ? 'bg-dark-surface border-dark-border text-dark-text' : 'bg-light-surface border-light-border text-light-text'
                }`}
              />
            </View>

            {/* Options for Multiple Choice */}
            {renderOptionsEditor()}

            {/* Correct Answer for Non-Multiple Choice */}
            {questionForm.question_type !== 'multiple_choice' && (
              <View className="space-y-3">
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Sample Answer / Keywords
                </Text>
                <TextInput
                  value={questionForm.correct_answer}
                  onChangeText={(text) => setQuestionForm({ ...questionForm, correct_answer: text })}
                  placeholder="Enter sample answer or keywords for grading..."
                  placeholderTextColor={isDark ? '#666' : '#999'}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  className={`p-4 rounded-lg border font-rubik ${
                    isDark ? 'bg-dark-surface border-dark-border text-dark-text' : 'bg-light-surface border-light-border text-light-text'
                  }`}
                />
              </View>
            )}

            {/* Points */}
            <View className="space-y-3">
              <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Points
              </Text>
              <TextInput
                value={questionForm.points.toString()}
                onChangeText={(text) => setQuestionForm({ ...questionForm, points: parseInt(text) || 1 })}
                placeholder="Points"
                placeholderTextColor={isDark ? '#666' : '#999'}
                keyboardType="numeric"
                className={`p-4 rounded-lg border font-rubik ${
                  isDark ? 'bg-dark-surface border-dark-border text-dark-text' : 'bg-light-surface border-light-border text-light-text'
                }`}
              />
            </View>
          </View>
        </ScrollView>

        {/* Save Button */}
        <View className={`p-4 border-t ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
          <TouchableOpacity
            onPress={handleSaveQuestion}
            disabled={loading}
            className={`py-4 rounded-lg items-center ${loading ? 'bg-gray-400' : 'bg-primary-500'}`}
          >
            {loading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-white font-rubik-semibold text-base">
                {editingQuestion ? 'Update Question' : 'Add Question'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );

  if (!selectedMockTest) {
    return (
      <SafeAreaView className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-bg' : 'bg-light-bg'}`}>
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text className={`mt-4 font-rubik ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Loading mock test...
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-bg' : 'bg-light-bg'}`}>
      {/* Header */}
      <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={() => router.back()} className="mr-3">
            <IconSymbol name="chevron.left" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className={`font-rubik-bold text-lg ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              {selectedMockTest.title}
            </Text>
            <Text className={`font-rubik text-sm ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              {questions.length} Questions • {questions.reduce((sum, q) => sum + q.points, 0)} Total Points
            </Text>
          </View>
        </View>
      </View>

      {/* Action Bar */}
      <View className={`p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <View className="flex-row space-x-3">
          <TouchableOpacity
            onPress={() => openQuestionModal()}
            className="flex-1 bg-primary-500 py-3 rounded-lg items-center"
          >
            <View className="flex-row items-center">
              <IconSymbol name="plus" size={20} color="white" />
              <Text className="ml-2 text-white font-rubik-semibold">Add Question</Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => setShowAIGenerator(true)}
            className="bg-purple-500 py-3 px-4 rounded-lg"
          >
            <IconSymbol name="wand.and.stars" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Questions List */}
      <FlatList
        data={questions.sort((a, b) => a.order_index - b.order_index)}
        renderItem={renderQuestionCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: 16 }}
        ListEmptyComponent={
          <View className="items-center justify-center py-16">
            <IconSymbol name="questionmark.circle" size={64} color={isDark ? '#666' : '#999'} />
            <Text className={`mt-4 font-rubik-medium text-lg ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              No questions yet
            </Text>
            <Text className={`mt-2 text-center font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Add your first question to get started
            </Text>
          </View>
        }
      />

      {/* Question Modal */}
      {renderQuestionModal()}

      {/* AI Generator Modal */}
      <Modal
        visible={showAIGenerator}
        transparent
        animationType="fade"
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-4">
          <View className={`w-full max-w-md p-6 rounded-xl ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-bold text-xl mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Generate Questions with AI
            </Text>
            
            <TextInput
              value={aiPrompt}
              onChangeText={setAiPrompt}
              placeholder="Enter topic or subject area..."
              placeholderTextColor={isDark ? '#666' : '#999'}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              className={`p-4 rounded-lg border font-rubik mb-4 ${
                isDark ? 'bg-dark-bg border-dark-border text-dark-text' : 'bg-light-bg border-light-border text-light-text'
              }`}
            />
            
            <View className="flex-row space-x-3">
              <TouchableOpacity
                onPress={() => setShowAIGenerator(false)}
                className="flex-1 py-3 rounded-lg border border-gray-300 items-center"
              >
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={generateQuestionsWithAI}
                disabled={aiGenerating || !aiPrompt.trim()}
                className={`flex-1 py-3 rounded-lg items-center ${
                  aiGenerating || !aiPrompt.trim() ? 'bg-gray-400' : 'bg-purple-500'
                }`}
              >
                {aiGenerating ? (
                  <ActivityIndicator color="white" size="small" />
                ) : (
                  <Text className="text-white font-rubik-semibold">Generate</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
} 