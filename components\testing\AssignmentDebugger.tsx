import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { debugUserAndTeacher, createTestTeacherRecord, createTestClass } from '@/lib/debugUtils';
import { useAssignmentStore } from '@/stores/assignmentStore';

export default function AssignmentDebugger() {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [loading, setLoading] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const { createAssignment } = useAssignmentStore();

  const debugSteps = [
    {
      id: 'debug-user',
      title: 'Debug User & Teacher',
      description: 'Check if user and teacher records exist',
      action: async () => {
        const info = await debugUserAndTeacher();
        setDebugInfo(info);
        return info;
      },
    },
    {
      id: 'create-teacher',
      title: 'Create Teacher Record',
      description: 'Create missing teacher record if needed',
      action: async () => {
        const teacher = await createTestTeacherRecord();
        return teacher;
      },
    },
    {
      id: 'create-class',
      title: 'Create Test Class',
      description: 'Create a test class for assignments',
      action: async () => {
        const testClass = await createTestClass();
        return testClass;
      },
    },
    {
      id: 'test-assignment',
      title: 'Test Assignment Creation',
      description: 'Try creating a test assignment',
      action: async () => {
        // First make sure we have a class
        const testClass = await createTestClass();
        if (!testClass) {
          throw new Error('No test class available');
        }

        const testAssignment = {
          title: 'Debug Test Assignment',
          description: 'This is a test assignment created for debugging',
          instructions: 'Complete this test assignment to verify the system works',
          max_points: 100,
          due_date: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
          class_id: testClass.id,
        };

        const result = await createAssignment(testAssignment);
        return result;
      },
    },
  ];

  const runStep = async (step: any) => {
    setLoading(step.id);
    try {
      const result = await step.action();
      
      if (result) {
        Alert.alert('Success', `${step.title} completed successfully`);
        console.log(`${step.title} result:`, result);
      } else {
        Alert.alert('Warning', `${step.title} completed but returned no data`);
      }
    } catch (error) {
      console.error(`${step.title} error:`, error);
      Alert.alert('Error', `${step.title} failed: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const runAllSteps = async () => {
    for (const step of debugSteps) {
      await runStep(step);
      // Wait a bit between steps
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  return (
    <View className={`flex-1 p-4 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      <View className="mb-6">
        <Text className={`text-2xl font-rubik-bold mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Assignment System Debugger
        </Text>
        <Text className={`font-rubik ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
          Debug and fix assignment system issues
        </Text>
      </View>

      <TouchableOpacity
        onPress={runAllSteps}
        disabled={loading !== null}
        className={`mb-6 p-4 rounded-xl ${
          loading ? 'bg-gray-400' : 'bg-primary-500'
        }`}
      >
        <Text className="text-white text-center font-rubik-bold">
          {loading ? 'Running Steps...' : 'Run All Debug Steps'}
        </Text>
      </TouchableOpacity>

      <ScrollView showsVerticalScrollIndicator={false}>
        {debugSteps.map((step, index) => (
          <View
            key={step.id}
            className={`mb-4 p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}
          >
            <View className="flex-row items-center justify-between mb-2">
              <Text className={`font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                {index + 1}. {step.title}
              </Text>
              {loading === step.id && (
                <ActivityIndicator size="small" color="#007AFF" />
              )}
            </View>

            <Text className={`font-rubik mb-3 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
              {step.description}
            </Text>

            <TouchableOpacity
              onPress={() => runStep(step)}
              disabled={loading !== null}
              className={`p-3 rounded-lg ${
                loading === step.id
                  ? 'bg-gray-400'
                  : isDark
                  ? 'bg-dark-background'
                  : 'bg-light-background'
              }`}
            >
              <Text className={`text-center font-rubik-medium ${
                loading === step.id
                  ? 'text-white'
                  : isDark
                  ? 'text-dark-text'
                  : 'text-light-text'
              }`}>
                {loading === step.id ? 'Running...' : 'Run Step'}
              </Text>
            </TouchableOpacity>
          </View>
        ))}

        {debugInfo && (
          <View className={`mt-6 p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-light-card'}`}>
            <Text className={`font-rubik-bold text-lg mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Debug Information
            </Text>
            
            <View className="space-y-2">
              <View>
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Session User ID:
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {debugInfo.session?.id || 'Not found'}
                </Text>
              </View>
              
              <View>
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  User Record:
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {debugInfo.user ? `${debugInfo.user.name} (${debugInfo.user.role})` : 'Not found'}
                </Text>
              </View>
              
              <View>
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Teacher Record:
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {debugInfo.teacher ? `ID: ${debugInfo.teacher.id}` : 'Not found'}
                </Text>
              </View>
              
              <View>
                <Text className={`font-rubik-medium ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                  Classes:
                </Text>
                <Text className={`font-rubik text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>
                  {debugInfo.classes?.length || 0} classes found
                </Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
