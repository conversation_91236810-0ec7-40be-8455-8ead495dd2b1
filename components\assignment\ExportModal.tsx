import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAssignmentStore } from '@/stores/assignmentStore';

interface ExportModalProps {
  visible: boolean;
  onClose: () => void;
  assignmentId?: string;
  assignmentIds?: string[];
  classId?: string;
  exportType: 'single' | 'multiple' | 'class';
  assignmentTitle?: string;
}

export default function ExportModal({
  visible,
  onClose,
  assignmentId,
  assignmentIds = [],
  classId,
  exportType,
  assignmentTitle = 'Assignment',
}: ExportModalProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'json'>('csv');
  const [exporting, setExporting] = useState(false);

  const {
    exportAssignment,
    exportMultipleAssignments,
    exportClassSummary,
    loading,
  } = useAssignmentStore();

  const handleExport = async () => {
    try {
      setExporting(true);

      switch (exportType) {
        case 'single':
          if (!assignmentId) {
            Alert.alert('Error', 'Assignment ID is required');
            return;
          }
          await exportAssignment(assignmentId, selectedFormat);
          break;

        case 'multiple':
          if (assignmentIds.length === 0) {
            Alert.alert('Error', 'No assignments selected');
            return;
          }
          await exportMultipleAssignments(assignmentIds, selectedFormat);
          break;

        case 'class':
          if (!classId) {
            Alert.alert('Error', 'Class ID is required');
            return;
          }
          await exportClassSummary(classId, selectedFormat);
          break;

        default:
          Alert.alert('Error', 'Invalid export type');
          return;
      }

      Alert.alert(
        'Export Successful',
        `Your ${exportType === 'class' ? 'class summary' : 'assignment data'} has been exported successfully.`,
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert(
        'Export Failed',
        'Failed to export data. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setExporting(false);
    }
  };

  const getExportTitle = () => {
    switch (exportType) {
      case 'single':
        return `Export "${assignmentTitle}"`;
      case 'multiple':
        return `Export ${assignmentIds.length} Assignments`;
      case 'class':
        return 'Export Class Summary';
      default:
        return 'Export Data';
    }
  };

  const getExportDescription = () => {
    switch (exportType) {
      case 'single':
        return 'Export assignment data including submissions, grades, and analytics.';
      case 'multiple':
        return 'Export multiple assignments with all their submissions and grades.';
      case 'class':
        return 'Export a comprehensive summary of all assignments and class performance.';
      default:
        return 'Export your data in the selected format.';
    }
  };

  const formatOptions = [
    {
      value: 'csv' as const,
      label: 'CSV (Spreadsheet)',
      description: 'Compatible with Excel, Google Sheets, and other spreadsheet applications',
      icon: 'doc.text.fill' as const,
    },
    {
      value: 'json' as const,
      label: 'JSON (Data)',
      description: 'Structured data format for developers and advanced users',
      icon: 'curlybraces' as const,
    },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
        {/* Header */}
        <View className={`px-6 py-4 border-b ${isDark ? 'border-dark-border' : 'border-gray-200'}`}>
          <View className="flex-row items-center justify-between">
            <View className="flex-1">
              <Text className={`text-xl font-rubik-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {getExportTitle()}
              </Text>
              <Text className={`text-sm font-rubik mt-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                {getExportDescription()}
              </Text>
            </View>
            <TouchableOpacity
              onPress={onClose}
              className={`w-8 h-8 rounded-full items-center justify-center ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
            >
              <IconSymbol name="xmark" size={16} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Format Selection */}
          <View className="mb-8">
            <Text className={`text-lg font-rubik-bold mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Choose Export Format
            </Text>

            {formatOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                onPress={() => setSelectedFormat(option.value)}
                className={`p-4 rounded-xl mb-3 border-2 ${
                  selectedFormat === option.value
                    ? 'border-primary-500 bg-primary-500/10'
                    : isDark
                    ? 'border-gray-700 bg-dark-card'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <View className="flex-row items-center">
                  <View className={`w-12 h-12 rounded-lg items-center justify-center mr-4 ${
                    selectedFormat === option.value
                      ? 'bg-primary-500/20'
                      : isDark
                      ? 'bg-gray-700'
                      : 'bg-gray-100'
                  }`}>
                    <IconSymbol
                      name={option.icon}
                      size={24}
                      color={selectedFormat === option.value ? '#3B82F6' : isDark ? '#9CA3AF' : '#6B7280'}
                    />
                  </View>
                  <View className="flex-1">
                    <Text className={`font-rubik-bold text-base ${
                      selectedFormat === option.value
                        ? 'text-primary-500'
                        : isDark
                        ? 'text-white'
                        : 'text-gray-900'
                    }`}>
                      {option.label}
                    </Text>
                    <Text className={`font-rubik text-sm mt-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                      {option.description}
                    </Text>
                  </View>
                  {selectedFormat === option.value && (
                    <IconSymbol
                      name="checkmark.circle.fill"
                      size={24}
                      color="#3B82F6"
                    />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Export Info */}
          <View className={`p-4 rounded-xl ${isDark ? 'bg-blue-900/20' : 'bg-blue-50'} mb-6`}>
            <View className="flex-row items-start">
              <IconSymbol
                name="info.circle.fill"
                size={20}
                color="#3B82F6"
                style={{ marginTop: 2, marginRight: 12 }}
              />
              <View className="flex-1">
                <Text className={`font-rubik-medium text-sm ${isDark ? 'text-blue-300' : 'text-blue-800'}`}>
                  What's included in the export:
                </Text>
                <Text className={`font-rubik text-sm mt-2 ${isDark ? 'text-blue-200' : 'text-blue-700'}`}>
                  • Assignment details and settings{'\n'}
                  • Student submissions and content{'\n'}
                  • Grades and feedback{'\n'}
                  • Submission timestamps{'\n'}
                  • Performance analytics
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Footer */}
        <View className={`px-6 py-4 border-t ${isDark ? 'border-dark-border' : 'border-gray-200'}`}>
          <View className="flex-row space-x-3">
            <TouchableOpacity
              onPress={onClose}
              className={`flex-1 py-3 rounded-lg items-center justify-center ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
              disabled={exporting || loading}
            >
              <Text className={`font-rubik-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleExport}
              className={`flex-1 py-3 rounded-lg items-center justify-center ${
                exporting || loading ? 'bg-gray-400' : 'bg-primary-500'
              }`}
              disabled={exporting || loading}
            >
              {exporting || loading ? (
                <View className="flex-row items-center">
                  <ActivityIndicator size="small" color="#FFFFFF" />
                  <Text className="text-white font-rubik-medium ml-2">Exporting...</Text>
                </View>
              ) : (
                <View className="flex-row items-center">
                  <IconSymbol name="square.and.arrow.up" size={16} color="#FFFFFF" />
                  <Text className="text-white font-rubik-medium ml-2">Export</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
