import { supabase } from '@/lib/supabase';

// GET - Fetch assignment templates
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const search = url.searchParams.get('search') || '';
    const category = url.searchParams.get('category') || 'all';
    const subject = url.searchParams.get('subject') || 'all';
    const publicOnly = url.searchParams.get('public_only') !== 'false';
    const myTemplates = url.searchParams.get('my_templates') === 'true';

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('id')
      .eq('user_id', session.user.id)
      .single();

    let teacherUuid = null;
    if (!teacherError && teacherData) {
      teacherUuid = teacherData.id;
    }

    // Use the search function
    const { data: templates, error } = await supabase
      .rpc('search_templates', {
        search_query: search,
        category_filter: category,
        subject_filter: subject,
        public_only: myTemplates ? false : publicOnly,
        teacher_uuid: myTemplates ? teacherUuid : null,
      });

    if (error) {
      console.error('Error fetching templates:', error);
      return Response.json({ error: 'Failed to fetch templates' }, { status: 500 });
    }

    // Filter for my templates if requested
    let filteredTemplates = templates || [];
    if (myTemplates && teacherUuid) {
      filteredTemplates = templates?.filter((t: any) => t.teacher_id === teacherUuid) || [];
    }

    return Response.json({
      templates: filteredTemplates,
      total: filteredTemplates.length,
    });

  } catch (error) {
    console.error('Error fetching assignment templates:', error);
    return Response.json(
      { error: 'Failed to fetch assignment templates' },
      { status: 500 }
    );
  }
}

// POST - Create a new assignment template
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      category,
      subject,
      gradeLevel,
      templateData,
      isPublic = false,
      tags = [],
    } = body;

    // Validate required fields
    if (!name || !description || !category || !subject || !gradeLevel || !templateData) {
      return Response.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('id, tenant_id')
      .eq('user_id', session.user.id)
      .single();

    if (teacherError || !teacherData) {
      return Response.json({ error: 'Teacher not found' }, { status: 404 });
    }

    // Create template
    const templateRecord = {
      name: name.trim(),
      description: description.trim(),
      category,
      subject,
      grade_level: gradeLevel,
      template_data: templateData,
      is_public: isPublic,
      tags: Array.isArray(tags) ? tags : [],
      teacher_id: teacherData.id,
      tenant_id: teacherData.tenant_id,
    };

    const { data: template, error } = await supabase
      .from('assignment_templates')
      .insert([templateRecord])
      .select(`
        *,
        teacher:teachers(
          user:users(name)
        )
      `)
      .single();

    if (error) {
      console.error('Error creating template:', error);
      return Response.json({ error: 'Failed to create template' }, { status: 500 });
    }

    return Response.json(template, { status: 201 });

  } catch (error) {
    console.error('Error creating assignment template:', error);
    return Response.json(
      { error: 'Failed to create assignment template' },
      { status: 500 }
    );
  }
}

// PUT - Update an assignment template
export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const {
      id,
      name,
      description,
      category,
      subject,
      gradeLevel,
      templateData,
      isPublic,
      tags,
    } = body;

    if (!id) {
      return Response.json({ error: 'Template ID required' }, { status: 400 });
    }

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('id')
      .eq('user_id', session.user.id)
      .single();

    if (teacherError || !teacherData) {
      return Response.json({ error: 'Teacher not found' }, { status: 404 });
    }

    // Update template (RLS will ensure only owner can update)
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description.trim();
    if (category !== undefined) updateData.category = category;
    if (subject !== undefined) updateData.subject = subject;
    if (gradeLevel !== undefined) updateData.grade_level = gradeLevel;
    if (templateData !== undefined) updateData.template_data = templateData;
    if (isPublic !== undefined) updateData.is_public = isPublic;
    if (tags !== undefined) updateData.tags = Array.isArray(tags) ? tags : [];

    const { data: template, error } = await supabase
      .from('assignment_templates')
      .update(updateData)
      .eq('id', id)
      .eq('teacher_id', teacherData.id)
      .select(`
        *,
        teacher:teachers(
          user:users(name)
        )
      `)
      .single();

    if (error) {
      console.error('Error updating template:', error);
      return Response.json({ error: 'Failed to update template' }, { status: 500 });
    }

    return Response.json(template);

  } catch (error) {
    console.error('Error updating assignment template:', error);
    return Response.json(
      { error: 'Failed to update assignment template' },
      { status: 500 }
    );
  }
}

// DELETE - Delete an assignment template
export async function DELETE(request: Request) {
  try {
    const url = new URL(request.url);
    const templateId = url.searchParams.get('id');

    if (!templateId) {
      return Response.json({ error: 'Template ID required' }, { status: 400 });
    }

    // Get current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .select('id')
      .eq('user_id', session.user.id)
      .single();

    if (teacherError || !teacherData) {
      return Response.json({ error: 'Teacher not found' }, { status: 404 });
    }

    // Delete template (RLS will ensure only owner can delete)
    const { error } = await supabase
      .from('assignment_templates')
      .delete()
      .eq('id', templateId)
      .eq('teacher_id', teacherData.id);

    if (error) {
      console.error('Error deleting template:', error);
      return Response.json({ error: 'Failed to delete template' }, { status: 500 });
    }

    return Response.json({ success: true });

  } catch (error) {
    console.error('Error deleting assignment template:', error);
    return Response.json(
      { error: 'Failed to delete assignment template' },
      { status: 500 }
    );
  }
}
