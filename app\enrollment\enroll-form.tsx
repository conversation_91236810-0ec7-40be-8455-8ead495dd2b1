import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useEnrollmentStore, type Class } from '@/stores/enrollmentStore';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const EnrollFormScreen = () => {
  const colorScheme = useColorScheme() ?? 'light';
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { classId } = useLocalSearchParams<{ classId: string }>();

  const { createEnrollmentRequest, enrollStudent, availableClasses, isLoading } = useEnrollmentStore();

  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [formData, setFormData] = useState({
    student_name: '',
    student_email: '',
    student_grade: '',
    student_section: '',
    roll_number: '',
    date_of_birth: '',
    parent_name: '',
    parent_email: '',
    parent_phone: '',
  });

  const [enrollmentType, setEnrollmentType] = useState<'immediate' | 'request'>('immediate');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Find the selected class
  useEffect(() => {
    if (classId && availableClasses.length > 0) {
      const foundClass = availableClasses.find(c => c.id === classId);
      if (foundClass) {
        setSelectedClass(foundClass);
        setFormData(prev => ({
          ...prev,
          student_grade: foundClass.grade || '',
          student_section: foundClass.section || '',
        }));
      }
    }
  }, [classId, availableClasses]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.student_name.trim()) {
      newErrors.student_name = 'Student name is required';
    }

    if (!formData.student_email.trim()) {
      newErrors.student_email = 'Student email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.student_email)) {
      newErrors.student_email = 'Please enter a valid email address';
    }

    if (!formData.student_grade.trim()) {
      newErrors.student_grade = 'Grade is required';
    }

    if (!formData.roll_number.trim()) {
      newErrors.roll_number = 'Roll number is required';
    }

    if (formData.parent_email && !/\S+@\S+\.\S+/.test(formData.parent_email)) {
      newErrors.parent_email = 'Please enter a valid parent email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !selectedClass) return;

    try {
      if (enrollmentType === 'immediate') {
        // Direct enrollment
        const student = await enrollStudent(selectedClass.id, {
          name: formData.student_name,
          email: formData.student_email,
          class: formData.student_grade, // Use 'class' instead of 'grade'
          section: formData.student_section,
          roll_number: formData.roll_number,
        });

        if (student) {
          Alert.alert(
            'Student Enrolled Successfully',
            `${formData.student_name} has been enrolled in ${selectedClass.name}.\n\nStudent ID: ${student.student_id}\nEnrollment Code: ${student.enrollment_code}`,
            [{ text: 'OK', onPress: () => router.back() }]
          );
        }
      } else {
        // Create enrollment request - filter out empty date fields
        const requestData = {
          class_id: selectedClass.id,
          student_name: formData.student_name,
          student_email: formData.student_email,
          student_grade: formData.student_grade,
          student_section: formData.student_section,
          roll_number: formData.roll_number,
          parent_name: formData.parent_name || undefined,
          parent_email: formData.parent_email || undefined,
          parent_phone: formData.parent_phone || undefined,
          // Only include date_of_birth if it's not empty
          ...(formData.date_of_birth && { date_of_birth: formData.date_of_birth }),
        };

        const request = await createEnrollmentRequest(requestData);

        if (request) {
          Alert.alert(
            'Enrollment Request Created',
            `Enrollment request for ${formData.student_name} has been created.\n\nEnrollment Code: ${request.enrollment_code}`,
            [{ text: 'OK', onPress: () => router.back() }]
          );
        }
      }
    } catch (error) {
      console.error('Enrollment error:', error);
      Alert.alert('Error', 'Failed to process enrollment. Please try again.');
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-light-background'}`}>
      {/* Header */}
      <View className={`flex-row items-center p-4 border-b ${isDark ? 'border-dark-border' : 'border-light-border'}`}>
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <IconSymbol name="arrow.left" size={24} color={isDark ? '#9CA3AF' : '#6B7280'} />
        </TouchableOpacity>
        <Text className={`text-xl font-rubik-bold ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
          Enroll Student
        </Text>
      </View>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Class Info */}
        {selectedClass && (
          <View className={`p-4 rounded-lg mb-6 ${isDark ? 'bg-dark-surface' : 'bg-light-surface'}`}>
            <Text className={`font-rubik-semibold mb-1 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Class: {selectedClass.name}
            </Text>
            <Text className={`font-rubik ${isDark ? 'text-dark-textSecondary' : 'text-light-textSecondary'}`}>
              Grade: {selectedClass.grade} | Section: {selectedClass.section}
            </Text>
          </View>
        )}

        {/* Enrollment Type Selection */}
        <View className="mb-6">
          <Text className={`font-rubik-semibold mb-3 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Enrollment Type
          </Text>
          <View className="flex-row">
            <TouchableOpacity
              onPress={() => setEnrollmentType('immediate')}
              className={`flex-1 p-3 rounded-l-lg border ${
                enrollmentType === 'immediate'
                  ? 'bg-primary-500 border-primary-500'
                  : isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
              }`}
            >
              <Text className={`text-center font-rubik-medium ${
                enrollmentType === 'immediate' ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                Immediate
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setEnrollmentType('request')}
              className={`flex-1 p-3 rounded-r-lg border ${
                enrollmentType === 'request'
                  ? 'bg-primary-500 border-primary-500'
                  : isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border'
              }`}
            >
              <Text className={`text-center font-rubik-medium ${
                enrollmentType === 'request' ? 'text-white' : isDark ? 'text-dark-text' : 'text-light-text'
              }`}>
                Request
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Student Information */}
        <View className="mb-6">
          <Text className={`font-rubik-semibold mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Student Information
          </Text>

          {/* Student Name */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Full Name *
            </Text>
            <TextInput
              value={formData.student_name}
              onChangeText={(value) => updateFormData('student_name', value)}
              placeholder="Enter student's full name"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              className={`p-4 rounded-lg font-rubik border ${
                errors.student_name
                  ? 'border-error'
                  : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
            {errors.student_name && (
              <Text className="text-error text-sm font-rubik mt-1">{errors.student_name}</Text>
            )}
          </View>

          {/* Student Email */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Email Address *
            </Text>
            <TextInput
              value={formData.student_email}
              onChangeText={(value) => updateFormData('student_email', value)}
              placeholder="Enter student's email"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              keyboardType="email-address"
              autoCapitalize="none"
              className={`p-4 rounded-lg font-rubik border ${
                errors.student_email
                  ? 'border-error'
                  : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
            {errors.student_email && (
              <Text className="text-error text-sm font-rubik mt-1">{errors.student_email}</Text>
            )}
          </View>

          {/* Grade and Section */}
          <View className="flex-row mb-4">
            <View className="flex-1 mr-2">
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Grade *
              </Text>
              <TextInput
                value={formData.student_grade}
                onChangeText={(value) => updateFormData('student_grade', value)}
                placeholder="Grade"
                placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                className={`p-4 rounded-lg font-rubik border ${
                  errors.student_grade
                    ? 'border-error'
                    : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
                }`}
              />
              {errors.student_grade && (
                <Text className="text-error text-sm font-rubik mt-1">{errors.student_grade}</Text>
              )}
            </View>
            <View className="flex-1 ml-2">
              <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
                Section
              </Text>
              <TextInput
                value={formData.student_section}
                onChangeText={(value) => updateFormData('student_section', value)}
                placeholder="Section"
                placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
                className={`p-4 rounded-lg font-rubik border ${
                  isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
                }`}
              />
            </View>
          </View>

          {/* Roll Number */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Roll Number *
            </Text>
            <TextInput
              value={formData.roll_number}
              onChangeText={(value) => updateFormData('roll_number', value)}
              placeholder="Enter roll number"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              className={`p-4 rounded-lg font-rubik border ${
                errors.roll_number
                  ? 'border-error'
                  : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
            {errors.roll_number && (
              <Text className="text-error text-sm font-rubik mt-1">{errors.roll_number}</Text>
            )}
          </View>

          {/* Date of Birth */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Date of Birth
            </Text>
            <TextInput
              value={formData.date_of_birth}
              onChangeText={(value) => updateFormData('date_of_birth', value)}
              placeholder="YYYY-MM-DD (optional)"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              className={`p-4 rounded-lg font-rubik border ${
                isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
          </View>
        </View>

        {/* Parent Information */}
        <View className="mb-6">
          <Text className={`font-rubik-semibold mb-4 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
            Parent/Guardian Information
          </Text>

          {/* Parent Name */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Parent/Guardian Name
            </Text>
            <TextInput
              value={formData.parent_name}
              onChangeText={(value) => updateFormData('parent_name', value)}
              placeholder="Enter parent/guardian name (optional)"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              className={`p-4 rounded-lg font-rubik border ${
                isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
          </View>

          {/* Parent Email */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Parent/Guardian Email
            </Text>
            <TextInput
              value={formData.parent_email}
              onChangeText={(value) => updateFormData('parent_email', value)}
              placeholder="Enter parent/guardian email (optional)"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              keyboardType="email-address"
              autoCapitalize="none"
              className={`p-4 rounded-lg font-rubik border ${
                errors.parent_email
                  ? 'border-error'
                  : isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
            {errors.parent_email && (
              <Text className="text-error text-sm font-rubik mt-1">{errors.parent_email}</Text>
            )}
          </View>

          {/* Parent Phone */}
          <View className="mb-4">
            <Text className={`font-rubik-medium mb-2 ${isDark ? 'text-dark-text' : 'text-light-text'}`}>
              Parent/Guardian Phone
            </Text>
            <TextInput
              value={formData.parent_phone}
              onChangeText={(value) => updateFormData('parent_phone', value)}
              placeholder="Enter parent/guardian phone (optional)"
              placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
              keyboardType="phone-pad"
              className={`p-4 rounded-lg font-rubik border ${
                isDark ? 'bg-dark-surface text-dark-text border-dark-border' : 'bg-light-surface text-light-text border-light-border'
              }`}
            />
          </View>
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          onPress={handleSubmit}
          disabled={isLoading}
          className={`py-4 rounded-lg items-center justify-center mb-6 ${
            isLoading ? 'opacity-70' : ''
          } bg-primary-500`}
        >
          <Text className="text-white font-rubik-semibold text-base">
            {isLoading ? 'Processing...' : enrollmentType === 'immediate' ? 'Enroll Student' : 'Create Request'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EnrollFormScreen;
